<?php

namespace App\Actions\PurchaseReturn;

use App\Actions\CustomFields\GetCustomFieldsAction;
use App\Models\Configuration\PurchaseReturnConfiguration;
use App\Models\Master\ExpenseReturnTransactionMaster;
use App\Models\TransactionCustomField;
use Lorisleiva\Actions\Concerns\AsAction;

class GetConfiguration
{
    use AsAction;

    public function handle()
    {
        $response = [];

        $purchaseReturnTransactionMaster = ExpenseReturnTransactionMaster::first();
        $purchaseReturnConfiguration = PurchaseReturnConfiguration::first();

        // TO_COMMENT =>
        $response['companyId'] = getCurrentCompany()->id ?? null;
        $response['company_id'] = getCurrentCompany()->id ?? null;
        $response['is_gst_applicable'] = getCurrentCompany()->is_gst_applicable ?? null;

        $response['document_prefix'] = [
            'method_of_voucher_number' => $purchaseReturnTransactionMaster->voucher_number ?? ExpenseReturnTransactionMaster::AUTOMATIC_MANUAL_OVERRIDE,
            'change_every_financial_year' => $purchaseReturnTransactionMaster->is_change_every_financial_year ?? 0,
            'title_of_print' => $purchaseReturnTransactionMaster->default_title_of_print ?? null,
        ];

        $response['header'] = [
            'is_change_gst_details' => $purchaseReturnConfiguration->is_change_gst_details,
            'is_enabled_shipping_address' => $purchaseReturnConfiguration->is_enabled_shipping_address,
            'is_enabled_broker_details' => $purchaseReturnConfiguration->is_enabled_broker_details,
            'is_enabled_transport_details' => $purchaseReturnConfiguration->is_enabled_transport_details,
            'custom_fields' => GetCustomFieldsAction::run(TransactionCustomField::PURCHASE_RETURN),
            'is_enabled_eway_details' => $purchaseReturnConfiguration->is_enabled_eway_details,
            'custom_fields' => GetCustomFieldsAction::run(TransactionCustomField::PURCHASE_RETURN),
        ];

        $response['item_table_configuration'] = [
            'is_enable_free_quantity' => $purchaseReturnConfiguration->is_enable_free_quantity,
            'is_additional_ledger_description' => $purchaseReturnConfiguration->is_additional_ledger_description,
            'consolidating_items_to_invoice' => $purchaseReturnConfiguration->consolidating_items_to_invoice,
            'is_additional_item_description' => $purchaseReturnConfiguration->is_additional_item_description,
            'warn_on_negative_stock' => $purchaseReturnConfiguration->warn_on_negative_stock,
            'is_enabled_mrp' => $purchaseReturnConfiguration->is_enabled_mrp,
            'is_enabled_discount_2' => $purchaseReturnConfiguration->is_enabled_discount_2,
            'is_enabled_hsn_code' => $purchaseReturnConfiguration->is_enabled_hsn_code,
            'show_item_image' => $purchaseReturnConfiguration->show_item_image,
            'with_tax' => $purchaseReturnConfiguration->with_tax,
        ];

        $response['footer'] = [
            'is_enable_narration' => $purchaseReturnConfiguration->is_enable_narration,
            'is_enabled_tcs_details' => $purchaseReturnConfiguration->is_enabled_tcs_details,
            'is_enabled_tds_details' => $purchaseReturnConfiguration->is_enabled_tds_details,
            'is_enabled_payment_details' => $purchaseReturnConfiguration->is_enabled_payment_details,
            'round_off_method' => $purchaseReturnTransactionMaster->round_off_method ?? 3,
        ];

        return $response;
    }
}
