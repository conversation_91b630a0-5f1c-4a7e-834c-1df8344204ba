import { useFormik } from "formik";
import React, { useContext, useEffect, useRef, useState } from "react";
import { Col, Form, Modal, Row } from "react-bootstrap";
import PhoneInput from "react-phone-input-2";
import { useDispatch, useSelector } from "react-redux";
import * as Yup from "yup";
import Close from "../../../assets/images/svg/close";
import { FormInput } from "../../../components/ui/Input";
import { StateContext } from "../../../context/StateContext";
import { CheckGstValidate } from "../../../shared/calculation";
import CountrySelect from "../../../shared/CountrySelect";
import { convertNameIntoCamelCase, fetchGstDetail } from "../../../shared/prepareData";
import { checkGstNumberAlreadyUsed, fetchGstData, gstDetail } from "../../../store/gst/gstSlice";
import {
    addTransport,
    getTransportById,
    updateTransport,
} from "../../../store/transport/transportSlice";
import WarningModal from "../../common/WarningModal";
import { toastType } from "../../../constants";
import { errorToast } from "../../../store/actions/toastAction";

const AddTransportModal = ({
    transport,
    show,
    handleClose,
    modalType,
    setModalType,
    setTransporterDetail,
    isBackdrop = false,
    partyFormik,
}) => {
    const [countryId, setCountryId] = useState(101);
    const [stateId, setStateId] = useState("");
    const [cityId, setCityId] = useState("");
    const {
        GSTError,
        setGSTError,
        isDisable,
        setIsDisable
     } = useContext(StateContext);
    const { company } = useSelector(selector => selector);
    const { Country, State, City } = CountrySelect({
        countryId,
        setCountryId,
        stateId,
        setStateId,
        cityId,
        setCityId,
    });
    const companyState = company?.company?.billing_address?.state_id || "";
    const [localGSTData, setLocalGSTData] = useState({});
    const [showGstNotUnique, setShowGstNotUnique] = useState(false);
    const [gstNotUniqueMessage, setGstNotUniqueMessage] = useState("");
    const inputRef = useRef();
    useEffect(() => {
        if (show && inputRef.current) {
            inputRef.current.focus();
        }
    }, [show]);

    const schema = Yup.object({
        transporter_name: Yup.string().required("Transporter name is required"),
    });

    useEffect(() => {
        return () => {
            dispatch(gstDetail(""));
        };
    }, []);

    const initialValues = {
        id: "",
        transporter_name: "",
        gstin: "",
        address_1: "",
        address_2: "",
        country_id: "",
        state_id: "",
        city_id: "",
        pin_code: "",
        contact_name: "",
        region_iso: "",
        region_code: "",
        contact_phone_number: "",
        contact_number: "",
        contact_email: "",
        is_create_with_ledger: 0
    };

    const dispatch = useDispatch();

    useEffect(() => {
        const transporter = transport?.getTransportById;
        if (transporter) {
            formik.setFieldValue("id", transporter?.id);
            formik.setFieldValue("transporter_name", transporter?.transporter_name);
            formik.setFieldValue("gstin", transporter?.gstin);
            formik.setFieldValue("address_1", transporter?.addresses?.address_1);
            formik.setFieldValue("address_2", transporter?.addresses?.address_2);
            formik.setFieldValue("country_id", transporter?.addresses?.country_id);
            formik.setFieldValue("state_id", transporter?.addresses?.state_id);
            formik.setFieldValue("city_id", transporter?.addresses?.city_id);
            formik.setFieldValue("pin_code", transporter?.addresses?.pin_code);
            formik.setFieldValue("contact_name", transporter?.contact_name);
            formik.setFieldValue("region_iso", transporter?.region_iso || "");
            formik.setFieldValue("region_code", transporter?.region_code || "");
            formik.setFieldValue("contact_email", transporter?.contact_email);
            formik.setFieldValue("contact_number", transporter?.contact_phone_number || "");
            formik.setFieldValue("is_create_with_ledger", transporter?.is_create_with_ledger || 0);
            formik.setFieldValue(
                "contact_phone_number",
                "+" + (transporter?.region_code || "91") + (transporter?.contact_phone_number || ""),
            );
            setCountryId(transporter?.addresses?.country_id);
            setStateId(transporter?.addresses?.state_id);
            setCityId(transporter?.addresses?.city_id);
        }
    }, [transport?.getTransportById]);

    useEffect(() => {
        formik.setFieldValue("country_id", countryId);
        formik.setFieldValue("state_id", stateId || companyState);
        formik.setFieldValue("city_id", cityId);
        setStateId(stateId || companyState);
    }, [countryId, stateId, cityId]);

    const RemoveAllValue = () => {
        setCountryId("");
        setStateId("");
        setCityId("");
    };

    const handlePhoneChange = (value, country) => {
        const valueStr = value.toString();
        const dialCodeStr = country.dialCode.toString();
        let number = "";
        if (valueStr.startsWith(dialCodeStr)) {
            number = valueStr.slice(dialCodeStr.length);
        }
        formik.setFieldValue("contact_number", number);
        formik.setFieldValue("region_iso", country.countryCode);
        formik.setFieldValue("region_code", country.dialCode);
        formik.setFieldValue("contact_phone_number", value);
    };

    const formik = useFormik({
        initialValues: initialValues,
        validationSchema: schema,
        onSubmit: values => {
            const email = values.contact_email;

            if (email && !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email)) {

                dispatch(
                    errorToast({
                        text: "Enter a valid email address",
                        type: toastType.ERROR,
                    })
                );
                return;
            }
            handleSubmit(values)
        },
    });

    useEffect(() => {
        if (localGSTData && localGSTData?.result) {
            const addr = localGSTData?.result?.pradr?.addr;

            let address1 = addr?.bno || "";

            if (addr?.flno) {
                address1 += ", " + addr?.flno;
            }

            if (addr?.bnm) {
                address1 += ", " + addr?.bnm;
            }

            let address2 = addr?.st || "";

            if (addr?.landMark) {
                address2 += ", " + addr?.landMark;
            }

            if (addr?.locality) {
                address2 += ", " + addr?.locality;
            }
            let pinCode = localGSTData?.result?.pradr?.addr?.pncd || "";
            let state = localGSTData?.result?.pradr?.addr?.stcd || "";
            let city = localGSTData?.result?.pradr?.addr?.dst || "";
            const response = fetchGstDetail({ state, city });
            formik.setFieldValue("transporter_name", convertNameIntoCamelCase(localGSTData?.result?.tradeNam));
            formik.setFieldValue("address_1", address1);
            formik.setFieldValue("address_2", address2);
            formik.setFieldValue("country_id", response.country_id);
            formik.setFieldValue("state_id", response.state_id);
            formik.setFieldValue("city_id", response.city_id);
            formik.setFieldValue("pin_code", pinCode);
            setCountryId(response.country_id);
            setStateId(response.state_id);
            setCityId(response.city_id);
        }
    }, [localGSTData, formik.values.gstin]);

    const handleSubmit = (values, action) => {
        const {
            id,
            transporter_name,
            address_1,
            address_2,
            city_id,
            contact_email,
            contact_name,
            country_id,
            gstin,
            pin_code,
            region_code,
            region_iso,
            state_id,
            contact_number,
            is_create_with_ledger
        } = values;
        setIsDisable(true);
        const formdata = new FormData();
        formdata.append("id", id || "");
        formdata.append("transporter_name", transporter_name || "");
        formdata.append("gstin", gstin || "");
        formdata.append("address_1", address_1 || "");
        formdata.append("address_2", address_2 || "");
        formdata.append("country_id", country_id || 101);
        formdata.append("state_id", state_id || "");
        formdata.append("city_id", city_id || "");
        formdata.append("pin_code", pin_code || "");
        formdata.append("contact_email", contact_email || "");
        formdata.append("contact_name", contact_name || "");
        formdata.append("region_iso", region_iso || "");
        formdata.append("region_code", region_code || "");
        formdata.append("contact_phone_number", contact_number || "");
        if (modalType !== "edit") {
            formdata.append("is_create_with_ledger", is_create_with_ledger);
        }
        if (!id) {
            dispatch(
                addTransport(
                    formdata,
                    handleClose,
                    setModalType,
                    closeTransportModal,
                    setTransporterDetail,
                    partyFormik,
                    setIsDisable
                ),
            );
        } else {
            dispatch(updateTransport(id, formdata, handleClose, setModalType, closeTransportModal, setIsDisable));
        }
        setGSTError("");
    };

    const changeGstDetail = async e => {
        const { value } = e.target;
        const upperValue = value.toUpperCase();
        formik.setFieldValue("gstin", upperValue);

        if (upperValue.length === 15 && CheckGstValidate(upperValue)) {
            dispatch(fetchGstData(upperValue, true, setLocalGSTData));
            const response = await checkGstNumberAlreadyUsed(upperValue, formik.id);
            if (response?.is_not_unique_gst_number) {
                setShowGstNotUnique(true);
                setGstNotUniqueMessage(response?.massage);
            }
            setGSTError(""); // Clear error on valid GSTIN
        } else if (upperValue.length > 0 && upperValue.length !== 15) {
            setGSTError("GSTIN must be exactly 15 characters long");
        } else if (upperValue.length === 0) {
            setGSTError(""); // Clear error if input is cleared
        } else {
            setGSTError("Please enter a valid GSTIN");
        }
    };

    const closeTransportModal = () => {
        handleClose();
        RemoveAllValue();
        formik.resetForm();
        setModalType({});
        dispatch(getTransportById(""));
        setGSTError("");
    };

    return (
        <Modal
            show={show}
            onHide={closeTransportModal}
            className={`modal fade`}
            centered
            size="xl"
            backdrop={isBackdrop}
        >
            <div className="modal-dialog-centered modal-xl">
                <div className="modal-content">
                    <div className="modal-header py-3">
                        <h5 className="modal-title">
                            {modalType ? "Update Transport" : "Add Transport"}
                        </h5>
                        <button
                            type="button"
                            className="btn btn-icon btn-sm btn-active-light-primary ms-2"
                            onClick={closeTransportModal}
                        >
                            <Close />
                        </button>
                    </div>
                    <div className="modal-body item-transaction">
                        <form onSubmit={formik.handleSubmit}>
                            <Row className="justify-content-between">
                                <Col xl={6} lg={6} md={12} sm={12} className="px-3 mb-4">
                                    <Form.Group className="position-relative form-floating-group">
                                        <FormInput
                                            ref={inputRef}
                                            className="floating-label-input capitalize"
                                            type="text"
                                            required
                                            placeholder=""
                                            name="transporter_name"
                                            value={formik.values.transporter_name}
                                            onChange={formik.handleChange}
                                        />
                                        <Form.Label className="required">Transport Name</Form.Label>
                                    </Form.Group>
                                    {formik.errors.transporter_name &&
                                        formik.touched.transporter_name && (
                                            <div className="text-danger ps-1">
                                                {formik.errors.transporter_name}
                                            </div>
                                        )}
                                </Col>
                                {company?.company?.is_gst_applicable ? (
                                    <Col xl={6} lg={6} md={12} sm={12} className="px-3 mb-4">
                                        <Form.Group className="position-relative form-floating-group">
                                            <FormInput
                                                className="floating-label-input"
                                                type="text"
                                                placeholder=""
                                                name="gstin"
                                                value={formik.values.gstin}
                                                onChange={changeGstDetail}
                                                minLength="15"
                                                maxLength="15"
                                            />
                                            <Form.Label>GSTIN/Transporter ID</Form.Label>
                                        </Form.Group>
                                        {GSTError && (
                                            <span className="text-danger">{GSTError}</span>
                                        )}
                                    </Col>
                                ) : (
                                    <Col></Col>
                                )}
                                <Col xl={6} lg={6} md={12} sm={12} className="px-3 mb-4">
                                    <Form.Group className="position-relative form-floating-group">
                                        <FormInput
                                            className="floating-label-input capitalize"
                                            type="text"
                                            placeholder=""
                                            name="address_1"
                                            value={formik.values.address_1}
                                            onChange={formik.handleChange}
                                        />
                                        <Form.Label>Address Line 1</Form.Label>
                                    </Form.Group>
                                </Col>
                                <Col xl={6} lg={6} md={12} sm={12} className="px-3 mb-4">
                                    <Form.Group className="position-relative form-floating-group">
                                        <FormInput
                                            className="floating-label-input capitalize"
                                            type="text"
                                            placeholder=""
                                            name="address_2"
                                            value={formik.values.address_2}
                                            onChange={formik.handleChange}
                                        />
                                        <Form.Label>Address Line 2</Form.Label>
                                    </Form.Group>
                                </Col>
                                <Col xl={3} lg={6} md={12} sm={12} className="ps-3 mb-4 form-group">
                                    <div className="input-group flex-nowrap">
                                        <div className="position-relative h-40px w-100 focus-shadow">
                                            <Country formik={formik} name="country_id" />
                                        </div>
                                    </div>
                                </Col>
                                <Col xl={3} lg={6} md={12} sm={12} className="ps-3 mb-4 form-group">
                                    <div className="input-group flex-nowrap">
                                        <div className="position-relative h-40px w-100 focus-shadow">
                                            <State formik={formik} name="state_id" city_name="city_id" />
                                        </div>
                                    </div>
                                </Col>
                                <Col xl={3} lg={6} md={12} sm={12} className="ps-3 mb-4 form-group">
                                    <div className="input-group flex-nowrap">
                                        <div className="position-relative h-40px w-100 focus-shadow">
                                            <City formik={formik} name="city_id" />
                                        </div>
                                    </div>
                                </Col>
                                <Col xl={3} lg={6} md={12} sm={12} className="px-3 mb-4">
                                    <Form.Group className="position-relative form-floating-group">
                                        <FormInput
                                            className="floating-label-input"
                                            type="text"
                                            placeholder=""
                                            name="pin_code"
                                            value={formik.values.pin_code}
                                            onChange={formik.handleChange}
                                            minLength="6"
                                            maxLength="6"
                                        />
                                        <Form.Label>Pincode</Form.Label>
                                    </Form.Group>
                                </Col>
                                <Form.Label>Contact Detail</Form.Label>
                                <Col xl={4} lg={6} md={12} sm={12} className="px-3 mb-4">
                                    <Form.Group className="position-relative form-floating-group">
                                        <FormInput
                                            className="floating-label-input capitalize"
                                            type="text"
                                            placeholder=""
                                            name="contact_name"
                                            value={formik.values.contact_name}
                                            onChange={formik.handleChange}
                                        />
                                        <Form.Label>Name</Form.Label>
                                    </Form.Group>
                                </Col>
                                <Col xl={4} lg={6} md={12} sm={12} className="px-3 mb-4">
                                    <PhoneInput
                                        country={"in"}
                                        value={formik.values.contact_phone_number}
                                        onChange={(phone, code) => handlePhoneChange(phone, code)}
                                        containerClass="w-100"
                                        inputClass="w-100 h-40px fw-500 focus-shadow"
                                        countryCodeEditable={false}
                                    />
                                </Col>
                                <Col xl={4} lg={6} md={12} sm={12} className="px-3 mb-4">
                                    <Form.Group className="position-relative form-floating-group">
                                        <FormInput
                                            className="floating-label-input"
                                            type="text"
                                            placeholder=""
                                            name="contact_email"
                                            value={formik.values.contact_email}
                                            onChange={formik.handleChange}
                                        />
                                        <Form.Label>Email</Form.Label>
                                        <button
                                            type="button"
                                            className="input-group-text custom-group-text top-0"
                                        >
                                            <i className="fas fa-envelope text-gray-900"></i>
                                        </button>
                                    </Form.Group>
                                </Col>
                            </Row>
                            {modalType !== "edit" && (
                                <Row>
                                    <Col xs={12} md="auto" className="d-flex flex-wrap align-items-center gap-3">
                                        <div className="d-flex align-items-center gap-3 ">
                                            <label htmlFor="isCreateWithLedger" className=" fs-6 fw-7 fw-medium">Create With Ledger : </label>
                                            <div
                                                className="form-check form-switch d-flex gap-2"
                                                style={{
                                                    paddingLeft: "0",
                                                }}
                                            >
                                                <input
                                                    className="form-check-input"
                                                    style={{
                                                        float: "right",
                                                        marginLeft: "0",
                                                    }}
                                                    type="checkbox"
                                                    id="isCreateWithLedger"
                                                    checked={formik.values.is_create_with_ledger === 1} // Convert 1 to true
                                                    onChange={(e) => {
                                                        formik.setFieldValue("is_create_with_ledger", e.target.checked ? 1 : 0);
                                                    }}
                                                />
                                            </div>
                                        </div>
                                    </Col>
                                </Row>
                            )}
                            <div className="mt-4">
                                <button type="submit" className="btn btn-primary me-2"
                                    disabled={isDisable}
                                >
                                    Save
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {showGstNotUnique && (
                <WarningModal
                    show={showGstNotUnique}
                    handleSubmit={() => setShowGstNotUnique(false)}
                    message={gstNotUniqueMessage}
                    showConfirmButton
                />
            )}
        </Modal>
    );
};

export default AddTransportModal;
