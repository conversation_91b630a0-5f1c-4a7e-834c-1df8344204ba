<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('income_estimate_quote_configurations', function (Blueprint $table) {
            $table->boolean('with_tax')->default(0);
        });
        Schema::table('income_transaction_configurations', function (Blueprint $table) {
            $table->boolean('with_tax')->default(0);
        });
        Schema::table('delivery_challan_configurations', function (Blueprint $table) {
            $table->boolean('with_tax')->default(0);
        });
        Schema::table('sales_return_configuration', function (Blueprint $table) {
            $table->boolean('with_tax')->default(0);
        });
        Schema::table('income_credit_note_configuration', function (Blueprint $table) {
            $table->boolean('with_tax')->default(0);
        });
        Schema::table('income_debit_note_configuration', function (Blueprint $table) {
            $table->boolean('with_tax')->default(0);
        });

        Schema::table('purchase_order_configuration', function (Blueprint $table) {
            $table->boolean('with_tax')->default(0);
        });
        Schema::table('expense_configuration', function (Blueprint $table) {
            $table->boolean('with_tax')->default(0);
        });
        Schema::table('expense_purchase_configuration', function (Blueprint $table) {
            $table->boolean('with_tax')->default(0);
        });
        Schema::table('expense_credit_note_configuration', function (Blueprint $table) {
            $table->boolean('with_tax')->default(0);
        });
        Schema::table('expense_debit_note_configuration', function (Blueprint $table) {
            $table->boolean('with_tax')->default(0);
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('income_estimate_quote_configurations', function (Blueprint $table) {
            $table->dropColumn('with_tax');
        });
        Schema::table('income_transaction_configurations', function (Blueprint $table) {
            $table->dropColumn('with_tax');
        });
        Schema::table('delivery_challan_configurations', function (Blueprint $table) {
            $table->dropColumn('with_tax');
        });
        Schema::table('sales_return_configuration', function (Blueprint $table) {
            $table->dropColumn('with_tax');
        });
        Schema::table('income_credit_note_configuration', function (Blueprint $table) {
            $table->dropColumn('with_tax');
        });
        Schema::table('income_debit_note_configuration', function (Blueprint $table) {
            $table->dropColumn('with_tax');
        });

        Schema::table('purchase_order_configuration', function (Blueprint $table) {
            $table->dropColumn('with_tax');
        });
        Schema::table('expense_configuration', function (Blueprint $table) {
            $table->dropColumn('with_tax');
        });
        Schema::table('expense_purchase_configuration', function (Blueprint $table) {
            $table->dropColumn('with_tax');
        });
        Schema::table('expense_credit_note_configuration', function (Blueprint $table) {
            $table->dropColumn('with_tax');
        });
        Schema::table('expense_debit_note_configuration', function (Blueprint $table) {
            $table->dropColumn('with_tax');
        });
    }
};
