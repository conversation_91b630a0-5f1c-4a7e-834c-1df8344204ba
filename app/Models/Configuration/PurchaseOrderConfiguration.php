<?php

namespace App\Models\Configuration;

use App\Traits\HasCompany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseOrderConfiguration extends Model
{
    use HasCompany, HasFactory;

    protected $table = 'purchase_order_configuration';

    protected $fillable = [
        'company_id',
        'is_additional_item_description',
        'is_additional_ledger_description',
        'is_enable_narration',
        'is_enabled_broker_details',
        'is_enabled_credit_period_details',
        'is_enabled_shipping_address',
        'is_enabled_transport_details',
        'is_change_gst_details',
        'is_consolidating_items_to_invoice',
        'is_enabled_dispatch_details',
        'is_enabled_discount_2',
        'is_enabled_hsn_code',
        'show_item_image',
        'with_tax',
    ];

    public $casts = [
        'company_id' => 'integer',
        'is_additional_item_description' => 'boolean',
        'is_additional_ledger_description' => 'boolean',
        'is_enable_narration' => 'boolean',
        'is_enabled_broker_details' => 'boolean',
        'is_enabled_credit_period_details' => 'boolean',
        'is_enabled_shipping_address' => 'boolean',
        'is_enabled_transport_details' => 'boolean',
        'is_change_gst_details' => 'boolean',
        'is_consolidating_items_to_invoice' => 'boolean',
        'is_enabled_dispatch_details' => 'boolean',
        'is_enabled_discount_2' => 'boolean',
        'is_enabled_hsn_code' => 'boolean',
        'show_item_image' => 'boolean',
        'with_tax' => 'boolean',
    ];
}
