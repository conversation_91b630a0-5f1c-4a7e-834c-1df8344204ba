<?php

namespace App\Models\Configuration;

use App\Traits\HasCompany;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\Configuration\expenseCreditNote
 *
 * @property int $id
 * @property int $company_id
 * @property bool $is_enabled_shipping_address
 * @property bool $is_enabled_broker_details
 * @property bool $is_enabled_transport_details
 * @property bool $is_enabled_credit_period_details
 * @property bool $is_enabled_po_details_of_buyer
 * @property bool $is_additional_item_description
 * @property bool $is_additional_ledger_description
 * @property bool $is_enable_narration
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 *
 * @method static Builder|ExpenseCreditNote newModelQuery()
 * @method static Builder|ExpenseCreditNote newQuery()
 * @method static Builder|ExpenseCreditNote query()
 * @method static Builder|ExpenseCreditNote whereCompanyId($value)
 * @method static Builder|ExpenseCreditNote whereCreatedAt($value)
 * @method static Builder|ExpenseCreditNote whereId($value)
 * @method static Builder|ExpenseCreditNote whereIsAdditionalItemDescription($value)
 * @method static Builder|ExpenseCreditNote whereIsAdditionalLedgerDescription($value)
 * @method static Builder|ExpenseCreditNote whereIsEnableNarration($value)
 * @method static Builder|ExpenseCreditNote whereIsEnabledBrokerDetails($value)
 * @method static Builder|ExpenseCreditNote whereIsEnabledCreditPeriodDetails($value)
 * @method static Builder|ExpenseCreditNote whereIsEnabledPoDetailsOfBuyer($value)
 * @method static Builder|ExpenseCreditNote whereIsEnabledShippingAddress($value)
 * @method static Builder|ExpenseCreditNote whereIsEnabledTransportDetails($value)
 * @method static Builder|ExpenseCreditNote whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property bool $consolidating_items_to_invoice
 *
 * @method static Builder|ExpenseCreditNote whereConsolidatingItemsToInvoice($value)
 *
 * @property bool $is_change_gst_details
 *
 * @method static Builder|ExpenseCreditNote whereIsChangeGstDetails($value)
 */
class ExpenseCreditNote extends Model
{
    use HasCompany;
    use HasFactory;

    public $table = 'expense_credit_note_configuration';

    protected $fillable = [
        'is_additional_item_description',
        'is_additional_ledger_description',
        'is_enable_narration',
        'is_enabled_broker_details',
        'is_enabled_credit_period_details',
        'is_enabled_po_details_of_buyer',
        'is_enabled_shipping_address',
        'is_enabled_transport_details',
        'consolidating_items_to_invoice',
        'is_change_gst_details',
        'is_enabled_tcs_details',
        'company_id',
        'is_enabled_mrp',
        'is_enabled_tds_details',
        'is_enabled_payment_details',
        'is_enabled_discount_2',
        'is_enabled_hsn_code',
        'show_item_image',
        'with_tax',
    ];

    public $casts = [
        'company_id' => 'integer',
        'is_additional_item_description' => 'boolean',
        'is_additional_ledger_description' => 'boolean',
        'is_enable_narration' => 'boolean',
        'is_enabled_broker_details' => 'boolean',
        'is_enabled_credit_period_details' => 'boolean',
        'is_enabled_po_details_of_buyer' => 'boolean',
        'is_enabled_shipping_address' => 'boolean',
        'is_enabled_transport_details' => 'boolean',
        'consolidating_items_to_invoice' => 'boolean',
        'is_change_gst_details' => 'boolean',
        'is_enabled_tcs_details' => 'boolean',
        'is_enabled_mrp' => 'boolean',
        'is_enabled_tds_details' => 'boolean',
        'is_enabled_payment_details' => 'boolean',
        'is_enabled_discount_2' => 'boolean',
        'is_enabled_hsn_code' => 'boolean',
        'show_item_image' => 'boolean',
        'with_tax' => 'boolean',
    ];
}
