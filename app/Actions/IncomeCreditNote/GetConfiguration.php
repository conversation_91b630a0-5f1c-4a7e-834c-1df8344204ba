<?php

namespace App\Actions\IncomeCreditNote;

use App\Actions\CustomFields\GetCustomFieldsAction;
use App\Models\Configuration\IncomeCreditNote;
use App\Models\Master\IncomeCreditNoteTransactionMaster;
use App\Models\TransactionCustomField;
use Lorisleiva\Actions\Concerns\AsAction;

class GetConfiguration
{
    use AsAction;

    public function handle()
    {
        $response = [];

        $incomeCNTransactionMaster = IncomeCreditNoteTransactionMaster::first();
        $incomeCNConfiguration = IncomeCreditNote::first();
        // TO_COMMENT =>
        $response['companyId'] = getCurrentCompany()->id ?? null;
        $response['company_id'] = getCurrentCompany()->id ?? null;
        $response['is_gst_applicable'] = getCurrentCompany()->is_gst_applicable ?? null;

        $response['document_prefix'] = [
            'method_of_voucher_number' => $incomeCNTransactionMaster->method_of_voucher_number ?? IncomeCreditNoteTransactionMaster::AUTOMATIC_MANUAL_OVERRIDE,
            'start_with_number' => $incomeCNTransactionMaster->start_with_number ?? 1,
            'change_every_financial_year' => $incomeCNTransactionMaster->is_change_every_financial_year ?? 0,
            'prefix_method' => $incomeCNTransactionMaster->prefix_method ?? IncomeCreditNoteTransactionMaster::PREFIX_TEXT,
            'prefix' => $incomeCNTransactionMaster->prefix ?? null,
            'suffix_method' => $incomeCNTransactionMaster->suffix_method ?? IncomeCreditNoteTransactionMaster::PREFIX_TEXT,
            'suffix' => $incomeCNTransactionMaster->suffix ?? null,
            'bank_id' => $incomeCNTransactionMaster->bank_id ?? null,
            'title_of_print' => $incomeCNTransactionMaster->title_of_print ?? null,
            'terms_and_conditions' => $incomeCNTransactionMaster->set_alter_declaration ?? null,
        ];

        $response['header'] = [
            'is_change_gst_details' => $incomeCNConfiguration->is_change_gst_details,
            'is_enabled_dispatch_details' => $incomeCNConfiguration->is_enabled_dispatch_details,
            'is_enabled_shipping_address' => $incomeCNConfiguration->is_enabled_shipping_address,
            'is_enabled_broker_details' => $incomeCNConfiguration->is_enabled_broker_details,
            'is_enabled_transport_details' => $incomeCNConfiguration->is_enabled_transport_details,
            'is_enabled_po_details_of_buyer' => $incomeCNConfiguration->is_enabled_po_details_of_buyer,
            'is_enabled_credit_period_details' => $incomeCNConfiguration->is_enabled_credit_period_details,
            'is_enabled_phone_number' => $incomeCNConfiguration->is_enabled_phone_number,
            'custom_fields' => GetCustomFieldsAction::run(TransactionCustomField::INCOME_CREDIT_NOTE),
        ];

        $response['item_table_configuration'] = [
            'is_additional_ledger_description' => $incomeCNConfiguration->is_additional_ledger_description,
            'is_enabled_mrp' => $incomeCNConfiguration->is_enabled_mrp,
            'consolidating_items_to_invoice' => $incomeCNConfiguration->consolidating_items_to_invoice,
            'is_additional_item_description' => $incomeCNConfiguration->is_additional_item_description,
            'is_enabled_discount_2' => $incomeCNConfiguration->is_enabled_discount_2, //
            'is_enabled_hsn_code' => $incomeCNConfiguration->is_enabled_hsn_code,
            'show_item_image' => $incomeCNConfiguration->show_item_image,
            'with_tax' => $incomeCNConfiguration->with_tax,
        ];

        $response['footer'] = [
            'is_enable_narration' => $incomeCNConfiguration->is_enable_narration,
            'is_enabled_terms_and_conditions' => $incomeCNConfiguration->is_enabled_terms_and_conditions, //
            'is_enabled_tcs_details' => $incomeCNConfiguration->is_enabled_tcs_details,
            'is_enabled_tds_details' => $incomeCNConfiguration->is_enabled_tds_details, //
            'is_enabled_payment_details' => $incomeCNConfiguration->is_enabled_payment_details,
            'is_enabled_bank_details' => $incomeCNConfiguration->is_enabled_bank_details,
            'round_off_method' => $incomeCNTransactionMaster->round_off_method ?? 3,
        ];

        return $response;
    }
}
