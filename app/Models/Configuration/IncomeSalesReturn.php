<?php

namespace App\Models\Configuration;

use App\Traits\HasCompany;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\Configuration\SalesReturnConfiguration
 *
 * @property int $id
 * @property int $company_id
 * @property int $is_enabled_shipping_address
 * @property int $is_enabled_broker_details
 * @property int $is_enabled_transport_details
 * @property int $is_enabled_credit_period_details
 * @property int $is_enabled_po_details_of_buyer
 * @property int $is_additional_item_description
 * @property int $is_additional_ledger_description
 * @property int $is_enable_narration
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 *
 * @method static Builder|IncomeSalesReturn newModelQuery()
 * @method static Builder|IncomeSalesReturn newQuery()
 * @method static Builder|IncomeSalesReturn query()
 * @method static Builder|IncomeSalesReturn whereCompanyId($value)
 * @method static Builder|IncomeSalesReturn whereCreatedAt($value)
 * @method static Builder|IncomeSalesReturn whereId($value)
 * @method static Builder|IncomeSalesReturn whereIsAdditionalItemDescription($value)
 * @method static Builder|IncomeSalesReturn whereIsAdditionalLedgerDescription($value)
 * @method static Builder|IncomeSalesReturn whereIsEnableNarration($value)
 * @method static Builder|IncomeSalesReturn whereIsEnabledBrokerDetails($value)
 * @method static Builder|IncomeSalesReturn whereIsEnabledCreditPeriodDetails($value)
 * @method static Builder|IncomeSalesReturn whereIsEnabledPoDetailsOfBuyer($value)
 * @method static Builder|IncomeSalesReturn whereIsEnabledShippingAddress($value)
 * @method static Builder|IncomeSalesReturn whereIsEnabledTransportDetails($value)
 * @method static Builder|IncomeSalesReturn whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property bool $consolidating_items_to_invoice
 *
 * @method static Builder|IncomeSalesReturn whereConsolidatingItemsToInvoice($value)
 *
 * @property bool $is_change_gst_details
 *
 * @method static Builder|IncomeSalesReturn whereIsChangeGstDetails($value)
 */
class IncomeSalesReturn extends Model
{
    use HasCompany;
    use HasFactory;

    public $table = 'sales_return_configuration';

    public $fillable = [
        'is_enabled_shipping_address',
        'is_enabled_broker_details',
        'is_enabled_transport_details',
        'is_enabled_credit_period_details',
        'is_enabled_po_details_of_buyer',
        'is_additional_item_description',
        'is_additional_ledger_description',
        'is_enable_narration',
        'consolidating_items_to_invoice',
        'is_change_gst_details',
        'is_enable_free_quantity',
        'is_enabled_tcs_details',
        'company_id',
        'is_enabled_mrp',
        'is_enabled_discount_2',
        'is_enabled_dispatch_details',
        'is_enabled_terms_and_conditions',
        'is_enabled_tds_details',
        'is_enabled_phone_number',
        'is_enabled_payment_details',
        'is_enabled_hsn_code',
        'show_item_image',
        'with_tax',
    ];

    public $casts = [
        'company_id' => 'integer',
        'is_additional_item_description' => 'boolean',
        'is_additional_ledger_description' => 'boolean',
        'is_enable_narration' => 'boolean',
        'is_enabled_broker_details' => 'boolean',
        'is_enabled_credit_period_details' => 'boolean',
        'is_enabled_po_details_of_buyer' => 'boolean',
        'is_enabled_shipping_address' => 'boolean',
        'is_enabled_transport_details' => 'boolean',
        'consolidating_items_to_invoice' => 'boolean',
        'is_change_gst_details' => 'boolean',
        'is_enable_free_quantity' => 'boolean',
        'is_enabled_tcs_details' => 'boolean',
        'is_enabled_mrp' => 'boolean',
        'is_enabled_discount_2' => 'boolean',
        'is_enabled_dispatch_details' => 'boolean',
        'is_enabled_terms_and_conditions' => 'boolean',
        'is_enabled_tds_details' => 'boolean',
        'is_enabled_payment_details' => 'boolean',
        'is_enabled_phone_number' => 'boolean',
        'is_enabled_hsn_code' => 'boolean',
        'show_item_image' => 'boolean',
        'with_tax' => 'boolean',
    ];
}
