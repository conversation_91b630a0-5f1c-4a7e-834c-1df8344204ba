<?php

namespace App\Actions\SaleTransaction;

use App\Models\Configuration\SaleConfiguration;
use App\Models\Master\IncomeTransactionMaster;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateConfigurationType
{
    use AsAction;

    public function handle($input)
    {
        $saleConfiguration = SaleConfiguration::first();

        if (! $saleConfiguration) {
            throw new \Exception('Sale Configuration not found');
        }

        if (isset($input['is_change_every_financial_year'])) {
            $saleTransactionMaster = IncomeTransactionMaster::first();
            $saleTransactionMaster->update([
                'is_change_every_financial_year' => $input['is_change_every_financial_year'],
            ]);

            return true;
        }

        if (isset($input['round_off_method'])) {
            $saleTransactionMaster = IncomeTransactionMaster::first();
            $saleTransactionMaster->update([
                'round_off_method' => $input['round_off_method'],
            ]);

            return true;
        }

        if (array_key_exists('terms_and_conditions', $input)) {
            $saleTransactionMaster = IncomeTransactionMaster::first();
            $saleTransactionMaster->update([
                'set_alter_declaration' => $input['terms_and_conditions'] ?? null,
            ]);

            return true;
        }

        if (isset($input['on_pay_ledger_id'])) {
            $saleTransactionMaster = IncomeTransactionMaster::first();
            $saleTransactionMaster->update([
                'on_pay_ledger_id' => $input['on_pay_ledger_id'],
            ]);

            return true;
        }

        $validKeys = [
            'is_enabled_estimate_quote', 'is_enabled_delivery_challan', 'is_enabled_dispatch_details', 'is_enabled_shipping_address',
            'is_enabled_broker_details', 'is_enabled_transport_details', 'is_enabled_eway_details', 'is_enabled_po_details_of_buyer',
            'is_enabled_credit_period_details', 'is_additional_ledger_description', 'consolidating_items_to_invoice', 'warn_on_negative_stock',
            'is_additional_item_description', 'is_enable_narration', 'is_enabled_terms_and_conditions', 'is_enabled_tcs_details',
            'is_enabled_tds_details', 'is_enabled_mrp', 'is_enabled_payment_details', 'is_change_gst_details', 'is_enabled_delivery_challan',
            'is_enabled_discount_2', 'is_enabled_phone_number', 'is_enabled_hsn_code', 'is_enable_free_quantity', 'show_item_image', 'is_enabled_bank_details',
            'with_tax',
        ];

        $validInput = array_intersect_key($input, array_flip($validKeys));

        foreach ($validInput as $key => $value) {
            $saleConfiguration->{$key} = $value;
        }

        try {
            $saleConfiguration->save();
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return false;
        }

        return true;
    }
}
