<?php

namespace App\Actions\DeliveryChallanTransaction;

use App\Actions\CustomFields\GetCustomFieldsAction;
use App\Models\Configuration\DeliveryChallanConfiguration;
use App\Models\DeliveryChallanTransactionMaster;
use App\Models\TransactionCustomField;
use Lorisleiva\Actions\Concerns\AsAction;

class GetConfiguration
{
    use AsAction;

    public function handle()
    {
        $deliveryChallanTransactionMaster = DeliveryChallanTransactionMaster::first();
        $deliveryChallanConfiguration = DeliveryChallanConfiguration::toBase()->first();

        // TO_COMMENT =>
        $response['companyId'] = getCurrentCompany()->id ?? null;
        $response['company_id'] = getCurrentCompany()->id ?? null;
        $response['is_gst_applicable'] = getCurrentCompany()->is_gst_applicable ?? null;

        $response['document_prefix'] = [
            'method_of_voucher_number' => $deliveryChallanTransactionMaster->method_of_voucher_number ?? DeliveryChallanTransactionMaster::AUTOMATIC_MANUAL_OVERRIDE,
            'start_with_number' => $deliveryChallanTransactionMaster->start_with_number ?? 1,
            'change_every_financial_year' => $deliveryChallanTransactionMaster->is_change_every_financial_year ?? 0,
            'prefix_method' => $deliveryChallanTransactionMaster->prefix_method ?? DeliveryChallanTransactionMaster::PREFIX_TEXT,
            'prefix' => $deliveryChallanTransactionMaster->prefix ?? null,
            'suffix_method' => $deliveryChallanTransactionMaster->suffix_method ?? DeliveryChallanTransactionMaster::PREFIX_TEXT,
            'suffix' => $deliveryChallanTransactionMaster->suffix ?? null,
            'title_of_print' => $deliveryChallanTransactionMaster->title_of_print ?? null,
            'terms_and_conditions' => $deliveryChallanTransactionMaster->terms_and_condition ?? null,
        ];
        $response['header'] = [
            'is_change_gst_details' => $this->getBooleanValue($deliveryChallanConfiguration->is_change_gst_details),
            'is_enabled_dispatch_details' => $this->getBooleanValue($deliveryChallanConfiguration->is_enabled_dispatch_details),
            'is_enabled_shipping_address' => $this->getBooleanValue($deliveryChallanConfiguration->is_enabled_shipping_address),
            'is_enabled_broker_details' => $this->getBooleanValue($deliveryChallanConfiguration->is_enabled_broker_details),
            'is_enabled_transport_details' => $this->getBooleanValue($deliveryChallanConfiguration->is_enabled_transport_details),
            'is_enabled_po_details_of_buyer' => $this->getBooleanValue($deliveryChallanConfiguration->is_enabled_po_details_of_buyer),
            'is_enabled_phone_number' => $this->getBooleanValue($deliveryChallanConfiguration->is_enabled_phone_number),
            'custom_fields' => GetCustomFieldsAction::run(TransactionCustomField::DELIVERY_CHALLAN),
        ];

        $response['item_table_configuration'] = [
            'consolidating_items_to_invoice' => $this->getBooleanValue($deliveryChallanConfiguration->consolidating_items_to_invoice),
            'warn_on_negative_stock' => $this->getBooleanValue($deliveryChallanConfiguration->warn_on_negative_stock),
            'is_additional_item_description' => $this->getBooleanValue($deliveryChallanConfiguration->is_additional_item_description),
            'is_enabled_mrp' => $this->getBooleanValue($deliveryChallanConfiguration->is_enabled_mrp),
            'is_enabled_discount_2' => $this->getBooleanValue($deliveryChallanConfiguration->is_enabled_discount_2),
            'is_enabled_hsn_code' => $this->getBooleanValue($deliveryChallanConfiguration->is_enabled_hsn_code),
            'show_item_image' => $this->getBooleanValue($deliveryChallanConfiguration->show_item_image),
            'with_tax' => $this->getBooleanValue($deliveryChallanConfiguration->with_tax),
        ];

        $response['footer'] = [
            'is_enable_narration' => $this->getBooleanValue($deliveryChallanConfiguration->is_enable_narration),
            'is_enabled_terms_and_conditions' => $this->getBooleanValue($deliveryChallanConfiguration->is_enabled_terms_and_conditions),
            'is_enabled_tcs_details' => $this->getBooleanValue($deliveryChallanConfiguration->is_enabled_tcs_details),
            'round_off_method' => $deliveryChallanTransactionMaster->round_off_method ?? 3,
        ];

        return $response;
    }

    private function getBooleanValue($value)
    {
        $value = $value == 1 ? true : false;

        return $value;
    }
}
