<?php

namespace App\Models\Configuration;

use App\Traits\HasCompany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IncomeEstimateQuoteConfiguration extends Model
{
    use HasCompany, HasFactory;

    public $table = 'income_estimate_quote_configurations';

    public $fillable = [
        'is_additional_item_description',
        'is_additional_ledger_description',
        'is_enable_narration',
        'is_enabled_broker_details',
        'is_enabled_credit_period_details',
        'is_enabled_po_details_of_buyer',
        'is_enabled_shipping_address',
        'is_enabled_transport_details',
        'consolidating_items_to_invoice',
        'is_change_gst_details',
        'warn_on_negative_stock',
        'is_enabled_tcs_details',
        'company_id',
        'is_enabled_mrp',
        'is_enabled_discount_2',
        'is_enabled_dispatch_details',
        'is_enabled_terms_and_conditions',
        'is_enabled_tds_details',
        'is_enabled_phone_number',
        'is_enabled_payment_details',
        'is_enabled_hsn_code',
        'show_item_image',
        'with_tax',
    ];

    public $casts = [
        'company_id' => 'integer',
        'is_additional_item_description' => 'boolean',
        'is_additional_ledger_description' => 'boolean',
        'is_enable_narration' => 'boolean',
        'is_enabled_broker_details' => 'boolean',
        'is_enabled_credit_period_details' => 'boolean',
        'is_enabled_po_details_of_buyer' => 'boolean',
        'is_enabled_shipping_address' => 'boolean',
        'is_enabled_transport_details' => 'boolean',
        'consolidating_items_to_invoice' => 'boolean',
        'is_change_gst_details' => 'boolean',
        'warn_on_negative_stock' => 'boolean',
        'is_enabled_tcs_details' => 'boolean',
        'is_enabled_mrp' => 'boolean',
        'is_enabled_discount_2' => 'boolean',
        'is_enabled_dispatch_details' => 'boolean',
        'is_enabled_terms_and_conditions' => 'boolean',
        'is_enabled_tds_details' => 'boolean',
        'is_enabled_payment_details' => 'boolean',
        'is_enabled_phone_number' => 'boolean',
        'is_enabled_hsn_code' => 'boolean',
        'show_item_image' => 'boolean',
        'with_tax' => 'boolean',
    ];
}
