<?php

namespace App\Actions\IncomeEstimateQuote;

use App\Jobs\UpdateIncomeBankDetails;
use App\Models\Configuration\IncomeEstimateQuoteConfiguration;
use App\Models\Master\IncomeEstimateQuoteTransactionMaster;
use Lori<PERSON>iva\Actions\Concerns\AsAction;

class UpdateConfiguration
{
    use AsAction;

    public function handle($input)
    {
        $estimateQuoteConfigurationData = [
            'is_change_gst_details' => $input['is_change_gst_details'] ?? false,
            'is_enabled_dispatch_details' => $input['is_enabled_dispatch_details'] ?? false,
            'is_enabled_shipping_address' => $input['is_enabled_shipping_address'] ?? false,
            'is_enabled_broker_details' => $input['is_enabled_broker_details'] ?? false,
            'is_enabled_transport_details' => $input['is_enabled_transport_details'] ?? false,
            'is_enabled_po_details_of_buyer' => $input['is_enabled_po_details_of_buyer'] ?? false,
            'is_enabled_credit_period_details' => $input['is_enabled_credit_period_details'] ?? false,
            'is_additional_ledger_description' => $input['is_additional_ledger_description'] ?? false,
            'consolidating_items_to_invoice' => $input['consolidating_items_to_invoice'] ?? false,
            'warn_on_negative_stock' => $input['warn_on_negative_stock'] ?? false,
            'is_additional_item_description' => $input['is_additional_item_description'] ?? false,
            'is_enable_narration' => $input['is_enable_narration'] ?? false,
            'is_enabled_terms_and_conditions' => $input['is_enabled_terms_and_conditions'] ?? false,
            'is_enabled_tcs_details' => $input['is_enabled_tcs_details'] ?? false,
            'is_enabled_tds_details' => $input['is_enabled_tds_details'] ?? false,
            'is_enabled_mrp' => $input['is_enabled_mrp'] ?? false,
            'is_enabled_discount_2' => $input['is_enabled_discount_2'] ?? false,
            'is_enabled_phone_number' => $input['is_enabled_phone_number'] ?? false,
            'is_enabled_hsn_code' => $input['is_enabled_hsn_code'] ?? false,
        ];

        $estimateQuoteTransactionMasterData = [
            'round_off_method' => $input['round_off_method'] ?? null,
            'method_of_voucher_number' => $input['method_of_voucher_number'] ?? null,
            'start_with_number' => $input['start_with_number'] ?? null,
            'is_change_every_financial_year' => $input['is_change_every_financial_year'] ?? false,
            'prefix_method' => $input['prefix_method'] ?? null,
            'prefix' => $input['prefix'] ?? null,
            'suffix_method' => $input['suffix_method'] ?? null,
            'suffix' => $input['suffix'] ?? null,
            'default_title' => $input['default_title'] ?? null,
            'bank_id' => $input['bank_id'] ?? null,
            'term_and_condition' => $input['terms_and_conditions'] ?? null,
        ];

        $estimateQuoteConfigurationId = IncomeEstimateQuoteConfiguration::first()?->id;
        $estimateQuoteTransactionMasterId = IncomeEstimateQuoteTransactionMaster::first()?->id;

        IncomeEstimateQuoteConfiguration::updateOrCreate(
            ['id' => $estimateQuoteConfigurationId],
            $estimateQuoteConfigurationData
        );

        if (isset($input['show_item_image'])) {
            IncomeEstimateQuoteConfiguration::updateOrCreate(
                ['id' => $estimateQuoteConfigurationId],
                ['show_item_image' => $input['show_item_image']]
            );
        }

        if (isset($input['with_tax'])) {
            IncomeEstimateQuoteConfiguration::updateOrCreate(
                ['id' => $estimateQuoteConfigurationId],
                ['with_tax' => $input['with_tax']]
            );
        }

        if (isset($input['is_enabled_bank_details'])) {
            IncomeEstimateQuoteConfiguration::updateOrCreate(
                ['id' => $estimateQuoteConfigurationId],
                ['is_enabled_bank_details' => $input['is_enabled_bank_details']]
            );
        }

        IncomeEstimateQuoteTransactionMaster::updateOrCreate(
            ['id' => $estimateQuoteTransactionMasterId],
            $estimateQuoteTransactionMasterData
        );

        // Update Bank Details for All Transactions
        if (isset($input['apply_income_bank_details']) && $input['apply_income_bank_details'] == 1) {
            $bankId = $input['bank_id'];
            if (! $bankId) {
                throw new \Exception('Bank ID is required to update income transaction bank details.');
            }
            $companyId = getCurrentCompany()->id;

            UpdateIncomeBankDetails::dispatch($companyId, $bankId);
        }

        return true;
    }
}
