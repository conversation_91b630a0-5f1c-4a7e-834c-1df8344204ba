<?php

namespace App\Actions\DeliveryChallanTransaction;

use App\Models\Configuration\DeliveryChallanConfiguration;
use App\Models\DeliveryChallanTransactionMaster;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateConfiguration
{
    use AsAction;

    public function handle($input)
    {

        $deliveryChallanConfigurationData = [
            'is_enabled_dispatch_details' => $input['is_enabled_dispatch_details'] ?? false,
            'is_enabled_shipping_address' => $input['is_enabled_shipping_address'] ?? false,
            'is_enabled_broker_details' => $input['is_enabled_broker_details'] ?? false,
            'is_enabled_transport_details' => $input['is_enabled_transport_details'] ?? false,
            'is_enabled_po_details_of_buyer' => $input['is_enabled_po_details_of_buyer'] ?? false,
            'consolidating_items_to_invoice' => $input['consolidating_items_to_invoice'] ?? false,
            'warn_on_negative_stock' => $input['warn_on_negative_stock'] ?? false,
            'is_additional_item_description' => $input['is_additional_item_description'] ?? false,
            'is_enable_narration' => $input['is_enable_narration'] ?? false,
            'is_enabled_terms_and_conditions' => $input['is_enabled_terms_and_conditions'] ?? false,
            'is_enabled_phone_number' => $input['is_enabled_phone_number'] ?? false,
            'is_enabled_mrp' => $input['is_enabled_mrp'] ?? false,
            'is_change_gst_details' => $input['is_change_gst_details'] ?? false,
            'is_enabled_discount_2' => $input['is_enabled_discount_2'] ?? false,
            'is_enabled_hsn_code' => $input['is_enabled_hsn_code'] ?? false,
            'is_enabled_tcs_details' => $input['is_enabled_tcs_details'] ?? false,
        ];

        $deliveryChallanTransactionMasterData = [
            'method_of_voucher_number' => $input['method_of_voucher_number'] ?? null,
            'start_with_number' => $input['start_with_number'] ?? null,
            'is_change_every_financial_year' => $input['is_change_every_financial_year'] ?? false,
            'prefix_method' => $input['prefix_method'] ?? null,
            'prefix' => $input['prefix'] ?? null,
            'suffix_method' => $input['suffix_method'] ?? null,
            'suffix' => $input['suffix'] ?? null,
            'title_of_print' => $input['title_of_print'] ?? null,
            'terms_and_condition' => $input['terms_and_conditions'] ?? null,
            'round_off_method' => $input['round_off_method'] ?? null,
        ];

        $deliveryChallanConfigurationId = DeliveryChallanConfiguration::first()?->id;
        $deliveryChallanTransactionMasterId = DeliveryChallanTransactionMaster::first()?->id;

        DeliveryChallanConfiguration::updateOrCreate(
            ['id' => $deliveryChallanConfigurationId],
            $deliveryChallanConfigurationData
        );

        if (isset($input['show_item_image'])) {
            DeliveryChallanConfiguration::updateOrCreate(
                ['id' => $deliveryChallanConfigurationId],
                ['show_item_image' => $input['show_item_image']]
            );
        }

        if (isset($input['with_tax'])) {
            DeliveryChallanConfiguration::updateOrCreate(
                ['id' => $deliveryChallanConfigurationId],
                ['with_tax' => $input['with_tax']]
            );
        }

        DeliveryChallanTransactionMaster::updateOrCreate(
            ['id' => $deliveryChallanTransactionMasterId],
            $deliveryChallanTransactionMasterData
        );

        return true;
    }
}
