<?php

namespace App\Actions\ExpenseCreditNote;

use App\Actions\CustomFields\GetCustomFieldsAction;
use App\Models\Configuration\ExpenseCreditNote;
use App\Models\Master\ExpenseCreditNoteTransactionMaster;
use App\Models\TransactionCustomField;
use Lorisleiva\Actions\Concerns\AsAction;

class GetConfiguration
{
    use AsAction;

    public function handle()
    {
        $response = [];

        $expenseTransactionMaster = ExpenseCreditNoteTransactionMaster::first();
        $expenseCNConfiguration = ExpenseCreditNote::first();

        // TO_COMMENT =>
        $response['companyId'] = getCurrentCompany()->id ?? null;
        $response['company_id'] = getCurrentCompany()->id ?? null;
        $response['is_gst_applicable'] = getCurrentCompany()->is_gst_applicable ?? null;

        $response['document_prefix'] = [
            'method_of_voucher_number' => $expenseTransactionMaster->voucher_number ?? ExpenseCreditNoteTransactionMaster::AUTOMATIC_MANUAL_OVERRIDE,
            'change_every_financial_year' => $expenseTransactionMaster->is_change_every_financial_year ?? 0,
            'title_of_print' => $expenseTransactionMaster->default_title_of_print ?? null,
        ];

        $response['header'] = [
            'is_change_gst_details' => $expenseCNConfiguration->is_change_gst_details,
            'is_enabled_shipping_address' => $expenseCNConfiguration->is_enabled_shipping_address,
            'is_enabled_broker_details' => $expenseCNConfiguration->is_enabled_broker_details,
            'is_enabled_transport_details' => $expenseCNConfiguration->is_enabled_transport_details,
            'custom_fields' => GetCustomFieldsAction::run(TransactionCustomField::EXPENSE_CREDIT_NOTE),
        ];

        $response['item_table_configuration'] = [
            'is_additional_ledger_description' => $expenseCNConfiguration->is_additional_ledger_description,
            'is_enabled_mrp' => $expenseCNConfiguration->is_enabled_mrp,
            'consolidating_items_to_invoice' => $expenseCNConfiguration->consolidating_items_to_invoice,
            'is_additional_item_description' => $expenseCNConfiguration->is_additional_item_description,
            'is_enabled_discount_2' => $expenseCNConfiguration->is_enabled_discount_2,
            'is_enabled_hsn_code' => $expenseCNConfiguration->is_enabled_hsn_code,
            'show_item_image' => $expenseCNConfiguration->show_item_image,
            'with_tax' => $expenseCNConfiguration->with_tax,
        ];

        $response['footer'] = [
            'is_enable_narration' => $expenseCNConfiguration->is_enable_narration,
            'is_enabled_tcs_details' => $expenseCNConfiguration->is_enabled_tcs_details,
            'is_enabled_tds_details' => $expenseCNConfiguration->is_enabled_tds_details,
            'is_enabled_payment_details' => $expenseCNConfiguration->is_enabled_payment_details,
            'round_off_method' => $expenseTransactionMaster->round_off_method ?? 3,
        ];

        return $response;
    }
}
