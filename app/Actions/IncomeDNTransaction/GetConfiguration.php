<?php

namespace App\Actions\IncomeDNTransaction;

use App\Actions\CustomFields\GetCustomFieldsAction;
use App\Models\Configuration\IncomeDebitNote;
use App\Models\Master\IncomeDebitNoteTransactionMaster;
use App\Models\TransactionCustomField;
use Lorisleiva\Actions\Concerns\AsAction;

class GetConfiguration
{
    use AsAction;

    public function handle()
    {
        $incomeDebitNoteTransactionMaster = IncomeDebitNoteTransactionMaster::first();
        $incomeDebitNoteConfiguration = IncomeDebitNote::toBase()->first();

        // TO_COMMENT =>
        $response['companyId'] = getCurrentCompany()->id ?? null;
        $response['company_id'] = getCurrentCompany()->id ?? null;
        $response['is_gst_applicable'] = getCurrentCompany()->is_gst_applicable ?? null;

        $response['document_prefix'] = [
            'method_of_voucher_number' => $incomeDebitNoteTransactionMaster->method_of_voucher_number ?? IncomeDebitNoteTransactionMaster::AUTOMATIC_MANUAL_OVERRIDE,
            'start_with_number' => $incomeDebitNoteTransactionMaster->start_with_number ?? 1,
            'change_every_financial_year' => $incomeDebitNoteTransactionMaster->is_change_every_financial_year ?? 0,
            'prefix_method' => $incomeDebitNoteTransactionMaster->prefix_method ?? IncomeDebitNoteTransactionMaster::PREFIX_TEXT,
            'prefix' => $incomeDebitNoteTransactionMaster->prefix ?? null,
            'suffix_method' => $incomeDebitNoteTransactionMaster->suffix_method ?? IncomeDebitNoteTransactionMaster::PREFIX_TEXT,
            'suffix' => $incomeDebitNoteTransactionMaster->suffix ?? null,
            'bank_id' => $incomeDebitNoteTransactionMaster->bank_id ?? null,
            'title_of_print' => $incomeDebitNoteTransactionMaster->title_of_print ?? null,
            'terms_and_conditions' => $incomeDebitNoteTransactionMaster->set_alter_declaration ?? null,
        ];

        $response['header'] = [
            'is_change_gst_details' => $incomeDebitNoteConfiguration->is_change_gst_details,
            'is_enabled_dispatch_details' => $incomeDebitNoteConfiguration->is_enabled_dispatch_details,
            'is_enabled_shipping_address' => $incomeDebitNoteConfiguration->is_enabled_shipping_address,
            'is_enabled_broker_details' => $incomeDebitNoteConfiguration->is_enabled_broker_details,
            'is_enabled_transport_details' => $incomeDebitNoteConfiguration->is_enabled_transport_details,
            'is_enabled_po_details_of_buyer' => $incomeDebitNoteConfiguration->is_enabled_po_details_of_buyer,
            'is_enabled_credit_period_details' => $incomeDebitNoteConfiguration->is_enabled_credit_period_details,
            'is_enabled_phone_number' => $incomeDebitNoteConfiguration->is_enabled_phone_number,
            'custom_fields' => GetCustomFieldsAction::run(TransactionCustomField::INCOME_DEBIT_NOTE),
        ];

        $response['item_table_configuration'] = [
            'is_additional_ledger_description' => $incomeDebitNoteConfiguration->is_additional_ledger_description,
            'is_enabled_mrp' => $incomeDebitNoteConfiguration->is_enabled_mrp,
            'consolidating_items_to_invoice' => $incomeDebitNoteConfiguration->consolidating_items_to_invoice,
            'warn_on_negative_stock' => $incomeDebitNoteConfiguration->warn_on_negative_stock,
            'is_additional_item_description' => $incomeDebitNoteConfiguration->is_additional_item_description,
            'is_enabled_discount_2' => $incomeDebitNoteConfiguration->is_enabled_discount_2,
            'is_enabled_hsn_code' => $incomeDebitNoteConfiguration->is_enabled_hsn_code,
            'show_item_image' => $incomeDebitNoteConfiguration->show_item_image,
            'with_tax' => $incomeDebitNoteConfiguration->with_tax,
        ];

        $response['footer'] = [
            'is_enable_narration' => $incomeDebitNoteConfiguration->is_enable_narration,
            'is_enabled_terms_and_conditions' => $incomeDebitNoteConfiguration->is_enabled_terms_and_conditions,
            'is_enabled_tcs_details' => $incomeDebitNoteConfiguration->is_enabled_tcs_details,
            'is_enabled_tds_details' => $incomeDebitNoteConfiguration->is_enabled_tds_details,
            'is_enabled_payment_details' => $incomeDebitNoteConfiguration->is_enabled_payment_details,
            'is_enabled_bank_details' => $incomeDebitNoteConfiguration->is_enabled_bank_details,
            'round_off_method' => $incomeDebitNoteTransactionMaster->round_off_method ?? 3,
        ];

        return $response;
    }
}
