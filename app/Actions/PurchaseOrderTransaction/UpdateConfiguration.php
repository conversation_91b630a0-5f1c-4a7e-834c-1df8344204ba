<?php

namespace App\Actions\PurchaseOrderTransaction;

use App\Models\Configuration\PurchaseOrderConfiguration;
use App\Models\Master\PurchaseOrderTransactionMaster;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateConfiguration
{
    use AsAction;

    public function handle($input)
    {
        $purchaseOrderConfigurationData = [
            'is_change_gst_details' => $input['is_change_gst_details'] ?? false,
            'is_enabled_shipping_address' => $input['is_enabled_shipping_address'] ?? false,
            'is_enabled_broker_details' => $input['is_enabled_broker_details'] ?? false,
            'is_enabled_transport_details' => $input['is_enabled_transport_details'] ?? false,
            'is_additional_ledger_description' => $input['is_additional_ledger_description'] ?? false,
            'is_consolidating_items_to_invoice' => $input['consolidating_items_to_invoice'] ?? false,
            'is_additional_item_description' => $input['is_additional_item_description'] ?? false,
            'is_enable_narration' => $input['is_enable_narration'] ?? false,
            'is_enabled_discount_2' => $input['is_enabled_discount_2'] ?? false,
            'is_enabled_hsn_code' => $input['is_enabled_hsn_code'] ?? false,
        ];

        $purchaseOrderTransactionMasterData = [
            'round_off_method' => $input['round_off_method'] ?? null,
            'method_of_voucher_number' => $input['method_of_voucher_number'] ?? null,
            'start_with_number' => $input['start_with_number'] ?? null,
            'is_change_every_financial_year' => $input['is_change_every_financial_year'] ?? false,
            'prefix_method' => $input['prefix_method'] ?? null,
            'prefix' => $input['prefix'] ?? null,
            'suffix_method' => $input['suffix_method'] ?? null,
            'suffix' => $input['suffix'] ?? null,
            'default_title' => $input['title_of_print'] ?? null,
        ];

        $purchaseOrderConfigurationId = PurchaseOrderConfiguration::first()?->id;
        $purchaseOrderTransactionMasterId = PurchaseOrderTransactionMaster::first()?->id;

        PurchaseOrderConfiguration::updateOrCreate(
            ['id' => $purchaseOrderConfigurationId],
            $purchaseOrderConfigurationData
        );

        if (isset($input['show_item_image'])) {
            PurchaseOrderConfiguration::updateOrCreate(
                ['id' => $purchaseOrderConfigurationId],
                ['show_item_image' => $input['show_item_image']]
            );
        }

        if (isset($input['with_tax'])) {
            PurchaseOrderConfiguration::updateOrCreate(
                ['id' => $purchaseOrderConfigurationId],
                ['with_tax' => $input['with_tax']]
            );
        }

        PurchaseOrderTransactionMaster::updateOrCreate(
            ['id' => $purchaseOrderTransactionMasterId],
            $purchaseOrderTransactionMasterData
        );

        return true;
    }
}
