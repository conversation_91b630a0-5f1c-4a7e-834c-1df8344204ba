<?php

namespace App\Actions\IncomeEstimateQuote;

use App\Models\Configuration\IncomeEstimateQuoteConfiguration;
use App\Models\Master\IncomeEstimateQuoteTransactionMaster;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateConfigurationType
{
    use AsAction;

    public function handle($input)
    {
        $estimateQuoteConfiguration = IncomeEstimateQuoteConfiguration::first();

        if (! $estimateQuoteConfiguration) {
            throw new \Exception('Estimate Quote Configuration not found.');
        }

        if (isset($input['is_change_every_financial_year'])) {
            $estimateQuoteTransactionMaster = IncomeEstimateQuoteTransactionMaster::first();
            $estimateQuoteTransactionMaster->update([
                'is_change_every_financial_year' => $input['is_change_every_financial_year'],
            ]);

            return true;
        }

        if (isset($input['round_off_method'])) {
            $estimateQuoteTransactionMaster = IncomeEstimateQuoteTransactionMaster::first();
            $estimateQuoteTransactionMaster->update([
                'round_off_method' => $input['round_off_method'],
            ]);

            return true;
        }

        if (array_key_exists('terms_and_conditions', $input)) {
            $estimateQuoteTransactionMaster = IncomeEstimateQuoteTransactionMaster::first();
            $estimateQuoteTransactionMaster->update([
                'term_and_condition' => $input['terms_and_conditions'] ?? null,
            ]);

            return true;
        }

        if (isset($input['default_title'])) {
            $estimateQuoteTransactionMaster = IncomeEstimateQuoteTransactionMaster::first();
            $estimateQuoteTransactionMaster->update([
                'default_title' => $input['default_title'],
            ]);

            return true;
        }

        $validKeys = [
            'is_enabled_dispatch_details', 'is_enabled_shipping_address',
            'is_enabled_broker_details', 'is_enabled_transport_details', 'is_enabled_po_details_of_buyer',
            'is_enabled_credit_period_details', 'is_additional_ledger_description', 'consolidating_items_to_invoice', 'warn_on_negative_stock',
            'is_additional_item_description', 'is_enable_narration', 'is_enabled_terms_and_conditions', 'is_enabled_tcs_details',
            'is_enabled_tds_details', 'is_enabled_mrp', 'is_change_gst_details', 'is_enabled_delivery_challan', 'is_enabled_discount_2', 'is_enabled_phone_number',
            'is_enabled_hsn_code', 'show_item_image', 'is_enabled_bank_details','with_tax',
        ];

        $validInput = array_intersect_key($input, array_flip($validKeys));

        foreach ($validInput as $key => $value) {
            $estimateQuoteConfiguration->{$key} = $value;
        }

        try {
            $estimateQuoteConfiguration->save();
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return false;
        }

        return true;
    }
}
