<?php

namespace App\Actions\ExpenseDebitNoteTransaction;

use App\Models\Configuration\ExpenseDebitNote;
use App\Models\Master\ExpenseDebitNoteTransactionMaster;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateConfigurationType
{
    use AsAction;

    public function handle($input)
    {
        $expenseDebitNoteConfiguration = ExpenseDebitNote::first();

        if (! $expenseDebitNoteConfiguration) {
            throw new \Exception('Purchase Configuration not found');
        }

        if (isset($input['is_change_every_financial_year'])) {
            $expenseTransactionMaster = ExpenseDebitNoteTransactionMaster::first();
            $expenseTransactionMaster->update([
                'is_change_every_financial_year' => $input['is_change_every_financial_year'],
            ]);

            return true;
        }

        if (isset($input['round_off_method'])) {
            $expenseTransactionMaster = ExpenseDebitNoteTransactionMaster::first();
            $expenseTransactionMaster->update([
                'round_off_method' => $input['round_off_method'],
            ]);

            return true;
        }

        if (isset($input['on_pay_ledger_id'])) {
            $expenseTransactionMaster = ExpenseDebitNoteTransactionMaster::first();
            $expenseTransactionMaster->update([
                'on_pay_ledger_id' => $input['on_pay_ledger_id'],
            ]);

            return true;
        }

        $validKeys = [
            'is_change_gst_details',
            'is_enabled_shipping_address',
            'is_enabled_broker_details',
            'is_enabled_transport_details',
            'is_additional_ledger_description',
            'is_enabled_mrp',
            'consolidating_items_to_invoice',
            'is_additional_item_description',
            'is_enabled_discount_2',
            'is_enabled_hsn_code',
            'is_enable_narration',
            'is_enabled_tcs_details',
            'is_enabled_tds_details',
            'is_enabled_payment_details',
            'show_item_image',
            'with_tax',
        ];

        $validInput = array_intersect_key($input, array_flip($validKeys));

        foreach ($validInput as $key => $value) {
            $expenseDebitNoteConfiguration->{$key} = $value;
        }

        try {
            $expenseDebitNoteConfiguration->save();
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return false;
        }

        return true;
    }
}
