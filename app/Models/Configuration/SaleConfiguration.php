<?php

namespace App\Models\Configuration;

use App\Traits\HasCompany;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\Configuration\IncomeTransactionConfiguration
 *
 * @property int $id
 * @property int $company_id
 * @property int $is_enabled_shipping_address
 * @property int $is_enabled_broker_details
 * @property int $is_enabled_transport_details
 * @property int $is_enabled_credit_period_details
 * @property int $is_enabled_po_details_of_buyer
 * @property int $is_additional_item_description
 * @property int $is_additional_ledger_description
 * @property int $is_enable_narration
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 *
 * @method static Builder|SaleConfiguration newModelQuery()
 * @method static Builder|SaleConfiguration newQuery()
 * @method static Builder|SaleConfiguration query()
 * @method static Builder|SaleConfiguration whereCompanyId($value)
 * @method static Builder|SaleConfiguration whereCreatedAt($value)
 * @method static Builder|SaleConfiguration whereId($value)
 * @method static Builder|SaleConfiguration whereIsAdditionalItemDescription($value)
 * @method static Builder|SaleConfiguration whereIsAdditionalLedgerDescription($value)
 * @method static Builder|SaleConfiguration whereIsEnableNarration($value)
 * @method static Builder|SaleConfiguration whereIsEnabledBrokerDetails($value)
 * @method static Builder|SaleConfiguration whereIsEnabledCreditPeriodDetails($value)
 * @method static Builder|SaleConfiguration whereIsEnabledPoDetailsOfBuyer($value)
 * @method static Builder|SaleConfiguration whereIsEnabledShippingAddress($value)
 * @method static Builder|SaleConfiguration whereIsEnabledTransportDetails($value)
 * @method static Builder|SaleConfiguration whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property bool $consolidating_items_to_invoice
 *
 * @method static Builder|SaleConfiguration whereConsolidatingItemsToInvoice($value)
 *
 * @property bool $is_change_gst_details
 *
 * @method static Builder|SaleConfiguration whereIsChangeGstDetails($value)
 *
 * @property bool $warn_on_negative_stock
 *
 * @method static Builder|SaleConfiguration whereWarnOnNegativeStock($value)
 *
 * @property bool $is_enabled_estimate_quote
 * @property bool $is_enabled_tcs_details
 * @property bool $is_enabled_mrp
 * @property bool $is_enabled_delivery_challan
 * @property bool $is_enabled_dispatch_details
 * @property bool $is_enabled_eway_details
 * @property bool $is_enabled_terms_and_conditions
 * @property bool $is_enabled_tds_details
 * @property bool $is_enabled_payment_details
 *
 * @method static \Database\Factories\Configuration\SaleConfigurationFactory factory($count = null, $state = [])
 * @method static Builder|SaleConfiguration whereIsEnabledDeliveryChallan($value)
 * @method static Builder|SaleConfiguration whereIsEnabledDispatchDetails($value)
 * @method static Builder|SaleConfiguration whereIsEnabledEstimateQuote($value)
 * @method static Builder|SaleConfiguration whereIsEnabledEwayDetails($value)
 * @method static Builder|SaleConfiguration whereIsEnabledMrp($value)
 * @method static Builder|SaleConfiguration whereIsEnabledPaymentDetails($value)
 * @method static Builder|SaleConfiguration whereIsEnabledTcsDetails($value)
 * @method static Builder|SaleConfiguration whereIsEnabledTdsDetails($value)
 * @method static Builder|SaleConfiguration whereIsEnabledTermsAndConditions($value)
 *
 * @mixin \Eloquent
 */
class SaleConfiguration extends Model
{
    use HasCompany;
    use HasFactory;

    public $table = 'income_transaction_configurations';

    public $fillable = [
        'is_additional_item_description',
        'is_additional_ledger_description',
        'is_enable_narration',
        'is_enabled_broker_details',
        'is_enabled_credit_period_details',
        'is_enabled_po_details_of_buyer',
        'is_enabled_shipping_address',
        'is_enabled_transport_details',
        'consolidating_items_to_invoice',
        'is_change_gst_details',
        'is_enable_free_quantity',
        'warn_on_negative_stock',
        'is_enabled_tcs_details',
        'is_enabled_estimate_quote',
        'is_enabled_mrp',
        'is_enabled_delivery_challan',
        'is_enabled_dispatch_details',
        'is_enabled_eway_details',
        'is_enabled_terms_and_conditions',
        'is_enabled_tds_details',
        'is_enabled_payment_details',
        'is_enabled_discount_2',
        'is_enabled_phone_number',
        'is_enabled_hsn_code',
        'show_item_image',
        'company_id',
        'with_tax',
    ];

    public $casts = [
        'company_id' => 'integer',
        'is_additional_item_description' => 'boolean',
        'is_additional_ledger_description' => 'boolean',
        'is_enable_narration' => 'boolean',
        'is_enabled_broker_details' => 'boolean',
        'is_enabled_credit_period_details' => 'boolean',
        'is_enabled_po_details_of_buyer' => 'boolean',
        'is_enabled_shipping_address' => 'boolean',
        'is_enabled_transport_details' => 'boolean',
        'consolidating_items_to_invoice' => 'boolean',
        'is_change_gst_details' => 'boolean',
        'is_enable_free_quantity' => 'boolean',
        'warn_on_negative_stock' => 'boolean',
        'is_enabled_tcs_details' => 'boolean',
        'is_enabled_estimate_quote' => 'boolean',
        'is_enabled_mrp' => 'boolean',
        'is_enabled_delivery_challan' => 'boolean',
        'is_enabled_dispatch_details' => 'boolean',
        'is_enabled_eway_details' => 'boolean',
        'is_enabled_terms_and_conditions' => 'boolean',
        'is_enabled_tds_details' => 'boolean',
        'is_enabled_payment_details' => 'boolean',
        'is_enabled_discount_2' => 'boolean',
        'is_enabled_phone_number' => 'boolean',
        'is_enabled_hsn_code' => 'boolean',
        'show_item_image' => 'boolean',
        'with_tax' => 'boolean',
    ];
}
