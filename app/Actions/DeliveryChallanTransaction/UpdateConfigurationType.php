<?php

namespace App\Actions\DeliveryChallanTransaction;

use App\Models\Configuration\DeliveryChallanConfiguration;
use App\Models\DeliveryChallanTransactionMaster;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateConfigurationType
{
    use AsAction;

    public function handle($input)
    {
        $deliveryChallanConfiguration = DeliveryChallanConfiguration::first();

        if (! $deliveryChallanConfiguration) {
            throw new \Exception('Delivery Challan Configuration not found');
        }

        if (isset($input['is_change_every_financial_year'])) {
            $deliveryChallanTransactionMaster = DeliveryChallanTransactionMaster::first();
            $deliveryChallanTransactionMaster->update([
                'is_change_every_financial_year' => $input['is_change_every_financial_year'],
            ]);

            return true;
        }

        if (isset($input['round_off_method'])) {
            $deliveryChallanTransactionMaster = DeliveryChallanTransactionMaster::first();
            $deliveryChallanTransactionMaster->update([
                'round_off_method' => $input['round_off_method'],
            ]);

            return true;
        }

        if (array_key_exists('terms_and_conditions', $input)) {
            $deliveryChallanTransactionMaster = DeliveryChallanTransactionMaster::first();
            $deliveryChallanTransactionMaster->update([
                'terms_and_condition' => $input['terms_and_conditions'] ?? null,
            ]);

            return true;
        }

        $validKeys = [
            'is_enabled_dispatch_details', 'is_enabled_shipping_address', 'is_enabled_broker_details',
            'is_enabled_transport_details', 'is_enabled_po_details_of_buyer', 'consolidating_items_to_invoice', 'warn_on_negative_stock',
            'is_additional_item_description', 'is_enable_narration', 'is_enabled_terms_and_conditions', 'is_enabled_phone_number',
            'is_enabled_mrp', 'is_enabled_discount_2', 'is_change_gst_details', 'is_enabled_tcs_details', 'is_enabled_hsn_code', 'show_item_image','with_tax',
        ];

        $validInput = array_intersect_key($input, array_flip($validKeys));

        foreach ($validInput as $key => $value) {
            $deliveryChallanConfiguration->{$key} = $value;
        }

        try {
            $deliveryChallanConfiguration->save();
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return false;
        }

        return true;
    }
}
