<?php

namespace App\Actions\IncomeCreditNote;

use App\Models\Configuration\IncomeCreditNote;
use App\Models\Master\IncomeCreditNoteTransactionMaster;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateConfigurationType
{
    use AsAction;

    public function handle($input)
    {
        $incomeCreditNoteConfiguration = IncomeCreditNote::first();

        if (! $incomeCreditNoteConfiguration) {
            throw new \Exception('Income Credit Note Configuration not found.');
        }

        if (isset($input['is_change_every_financial_year'])) {
            $incomeCreditNoteMaster = IncomeCreditNoteTransactionMaster::first();
            $incomeCreditNoteMaster->update([
                'is_change_every_financial_year' => $input['is_change_every_financial_year'],
            ]);

            return true;
        }

        if (isset($input['round_off_method'])) {
            $incomeCreditNoteMaster = IncomeCreditNoteTransactionMaster::first();
            $incomeCreditNoteMaster->update([
                'round_off_method' => $input['round_off_method'],
            ]);

            return true;
        }

        if (array_key_exists('terms_and_conditions', $input)) {
            $incomeCreditNoteMaster = IncomeCreditNoteTransactionMaster::first();
            $incomeCreditNoteMaster->update([
                'set_alter_declaration' => $input['terms_and_conditions'] ?? null,
            ]);

            return true;
        }

        if (isset($input['on_pay_ledger_id'])) {
            $incomeCreditNoteMaster = IncomeCreditNoteTransactionMaster::first();
            $incomeCreditNoteMaster->update([
                'on_pay_ledger_id' => $input['on_pay_ledger_id'],
            ]);

            return true;
        }

        $validKeys = [
            'is_enabled_dispatch_details', 'is_enabled_shipping_address', 'is_enabled_broker_details', 'is_enabled_transport_details',
            'is_enabled_po_details_of_buyer', 'is_enabled_credit_period_details', 'is_additional_ledger_description',
            'consolidating_items_to_invoice', 'is_additional_item_description', 'is_enable_narration', 'is_enabled_terms_and_conditions',
            'is_enabled_tcs_details', 'is_enabled_tds_details', 'is_enabled_mrp', 'is_enabled_payment_details', 'is_change_gst_details',
            'is_enabled_discount_2', 'is_enabled_phone_number', 'is_enabled_hsn_code', 'show_item_image', 'is_enabled_bank_details','with_tax',
        ];

        $validInput = array_intersect_key($input, array_flip($validKeys));

        foreach ($validInput as $key => $value) {
            $incomeCreditNoteConfiguration->{$key} = $value;
        }

        try {
            $incomeCreditNoteConfiguration->save();
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return false;
        }

        return true;
    }
}
