<?php

namespace App\Models\Configuration;

use App\Traits\HasCompany;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\Configuration\ExpenseConfiguration
 *
 * @property int $id
 * @property int $company_id
 * @property int $is_enabled_shipping_address
 * @property int $is_enabled_broker_details
 * @property int $is_enabled_transport_details
 * @property int $is_enabled_credit_period_details
 * @property int $is_additional_item_description
 * @property int $is_additional_ledger_description
 * @property int $is_enable_narration
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 *
 * @method static Builder|PurchaseConfiguration newModelQuery()
 * @method static Builder|PurchaseConfiguration newQuery()
 * @method static Builder|PurchaseConfiguration query()
 * @method static Builder|PurchaseConfiguration whereCompanyId($value)
 * @method static Builder|PurchaseConfiguration whereCreatedAt($value)
 * @method static Builder|PurchaseConfiguration whereId($value)
 * @method static Builder|PurchaseConfiguration whereIsAdditionalItemDescription($value)
 * @method static Builder|PurchaseConfiguration whereIsAdditionalLedgerDescription($value)
 * @method static Builder|PurchaseConfiguration whereIsEnableNarration($value)
 * @method static Builder|PurchaseConfiguration whereIsEnabledBrokerDetails($value)
 * @method static Builder|PurchaseConfiguration whereIsEnabledCreditPeriodDetails($value)
 * @method static Builder|PurchaseConfiguration whereIsEnabledShippingAddress($value)
 * @method static Builder|PurchaseConfiguration whereIsEnabledTransportDetails($value)
 * @method static Builder|PurchaseConfiguration whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property bool $consolidating_items_to_invoice
 *
 * @method static Builder|PurchaseConfiguration whereConsolidatingItemsToInvoice($value)
 *
 * @property bool $is_change_gst_details
 *
 * @method static Builder|PurchaseConfiguration whereIsChangeGstDetails($value)
 */
class PurchaseConfiguration extends Model
{
    use HasCompany;
    use HasFactory;

    public $table = 'expense_configuration';

    public $fillable = [
        'is_enabled_shipping_address',
        'is_enabled_broker_details',
        'is_enabled_transport_details',
        'is_enabled_credit_period_details',
        'is_enabled_po_details_of_buyer',
        'is_additional_item_description',
        'is_additional_ledger_description',
        'is_enable_narration',
        'consolidating_items_to_invoice',
        'is_change_gst_details',
        'is_enable_free_quantity',
        'is_enabled_tcs_details',
        'company_id',
        'is_enabled_mrp',
        'is_enabled_tds_details',
        'is_enabled_payment_details',
        'is_enabled_discount_2',
        'is_enabled_hsn_code',
        'show_item_image',
        'with_tax',
    ];

    public $casts = [
        'company_id' => 'integer',
        'is_additional_item_description' => 'boolean',
        'is_additional_ledger_description' => 'boolean',
        'is_enable_narration' => 'boolean',
        'is_enabled_broker_details' => 'boolean',
        'is_enabled_credit_period_details' => 'boolean',
        'is_enabled_shipping_address' => 'boolean',
        'is_enabled_transport_details' => 'boolean',
        'consolidating_items_to_invoice' => 'boolean',
        'is_change_gst_details' => 'boolean',
        'is_enable_free_quantity' => 'boolean',
        'is_enabled_tcs_details' => 'boolean',
        'is_enabled_mrp' => 'boolean',
        'is_enabled_tds_details' => 'boolean',
        'is_enabled_payment_details' => 'boolean',
        'is_enabled_discount_2' => 'boolean',
        'is_enabled_hsn_code' => 'boolean',
        'show_item_image' => 'boolean',
        'with_tax' => 'boolean',
    ];
}
