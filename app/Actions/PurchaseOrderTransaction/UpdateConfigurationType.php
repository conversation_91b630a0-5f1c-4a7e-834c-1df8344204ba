<?php

namespace App\Actions\PurchaseOrderTransaction;

use App\Models\Configuration\PurchaseOrderConfiguration;
use App\Models\Master\PurchaseOrderTransactionMaster;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateConfigurationType
{
    use AsAction;

    public function handle($input)
    {
        $purchaseOrderConfiguration = PurchaseOrderConfiguration::first();

        if (! $purchaseOrderConfiguration) {
            throw new \Exception('Purchase Order Configuration not found');
        }

        if (isset($input['is_change_every_financial_year'])) {
            $purchaseOrderTransactionMaster = PurchaseOrderTransactionMaster::first();
            $purchaseOrderTransactionMaster->update([
                'is_change_every_financial_year' => $input['is_change_every_financial_year'],
            ]);

            return true;
        }

        if (isset($input['round_off_method'])) {
            $purchaseOrderTransactionMaster = PurchaseOrderTransactionMaster::first();
            $purchaseOrderTransactionMaster->update([
                'round_off_method' => $input['round_off_method'],
            ]);

            return true;
        }

        if (isset($input['title_of_print'])) {
            $purchaseOrderTransactionMaster = PurchaseOrderTransactionMaster::first();
            $purchaseOrderTransactionMaster->update([
                'default_title' => $input['title_of_print'],
            ]);

            return true;
        }

        $validKeys = [
            'is_enabled_shipping_address', 'is_enabled_broker_details', 'is_enabled_transport_details', 'is_additional_ledger_description',
            'is_consolidating_items_to_invoice', 'is_additional_item_description', 'is_enable_narration', 'is_change_gst_details',
            'is_enabled_discount_2', 'is_enabled_hsn_code', 'show_item_image','with_tax',
        ];

        $keyMappings = [
            'consolidating_items_to_invoice' => 'is_consolidating_items_to_invoice',
        ];

        $mappedInput = [];
        foreach ($input as $key => $value) {
            if (isset($keyMappings[$key])) {
                $mappedInput[$keyMappings[$key]] = $value;
            } else {
                $mappedInput[$key] = $value;
            }
        }

        $validInput = array_intersect_key($mappedInput, array_flip($validKeys));

        foreach ($validInput as $key => $value) {
            $purchaseOrderConfiguration->{$key} = $value;
        }

        try {
            $purchaseOrderConfiguration->save();
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return false;
        }

        return true;
    }
}
