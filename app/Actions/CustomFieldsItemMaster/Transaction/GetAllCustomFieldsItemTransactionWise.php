<?php

namespace App\Actions\CustomFieldsItemMaster\Transaction;

use App\Actions\CustomFieldsItemMaster\GetInputTypeWiseValue;
use App\Models\ItemCustomField;
use App\Models\ItemCustomFieldSetting;
use App\Models\ItemCustomFieldTransactionSetting;
use App\Models\ItemCustomFieldValue;
use App\Models\ItemCustomFieldFormula;
use Lorisleiva\Actions\Concerns\AsAction;

class GetAllCustomFieldsItemTransactionWise
{
    use AsAction;

    public function handle($type, $transactionId, $modelType, $itemId)
    {
        $fields = ItemCustomField::where('open_in_popup', false)->get();

        $itemMasterSetting = ItemCustomFieldSetting::where('item_id', $itemId)->get()->keyBy('custom_field_id');

        $settings = ItemCustomFieldTransactionSetting::where('transaction_type', $type)
            ->whereIn('custom_field_id', $fields->pluck('id')->toArray())->get()->keyBy('custom_field_id');

        $itemTransactionCFvalue = ItemCustomFieldValue::with('customField')->where('model_id', $transactionId)->where('model_type', $modelType)
            ->orderBy('custom_field_id')
            ->get()
            ->keyBy('custom_field_id');

        $fields = $fields->map(function ($field) use ($itemTransactionCFvalue, $itemMasterSetting, $settings) {

            $isShowInPrint = $settings->get($field->id);

            $value = $itemTransactionCFvalue->get($field->id) ? GetInputTypeWiseValue::run($itemTransactionCFvalue->get($field->id)) : null;
            $optionId = $itemTransactionCFvalue->get($field->id)->value_select_option_id ?? null;

            return [
                'custom_field_id' => $field->id,
                'label_name' => $field->label_name,
                'custom_field_type' => $field->custom_field_type,
                'open_in_popup' => $field->open_in_popup,
                'is_able_to_edit' => isset($itemMasterSetting[$field->id]),
                'value' => $value ?? null,
                'option_id' => $field->custom_field_type == ItemCustomField::CF_TYPE_DROPDOWN ? $optionId : null, // for select type only
                'is_show_in_print' => $isShowInPrint && $isShowInPrint->is_show_in_print ? true : false,
                'field_type' => $itemTransactionCFvalue->get($field->id)->field_type ?? null,
                'input_type' => ItemCustomField::CUSTOM_FIELD_ARRAY_WITH_INPUT_TYPE[$field->custom_field_type]['input_type'] ?? null,
            ];
        });

        $this->appendQuantityFormula($fields, $itemId);

        return $fields;
    }

    private function appendQuantityFormula(&$data, $itemId)
    {
        $itemDefaultFormula = ItemCustomFieldFormula::where('is_system_field', true)
            ->where('system_field_name', 'Quantity')
            ->where('item_id', $itemId)
            ->select('id', 'formula', 'custom_field_id', 'item_id', 'used_cf_ids_for_formula')
            ->first();

        if ($itemDefaultFormula) {
            $data[] = [
                'label_name' => 'Quantity',
                'status' => true,
                'local_status' => true,
                'is_system_field' => true,
                'system_field_name' => 'Quantity',
                'default_formula' => $itemDefaultFormula,
            ];
        }
    }
}
