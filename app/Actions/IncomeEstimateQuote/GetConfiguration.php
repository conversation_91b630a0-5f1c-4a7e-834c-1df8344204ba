<?php

namespace App\Actions\IncomeEstimateQuote;

use App\Actions\CustomFields\GetCustomFieldsAction;
use App\Models\Configuration\IncomeEstimateQuoteConfiguration;
use App\Models\Master\IncomeEstimateQuoteTransactionMaster;
use App\Models\TransactionCustomField;
use Lorisleiva\Actions\Concerns\AsAction;

class GetConfiguration
{
    use AsAction;

    public function handle()
    {
        $response = [];

        $estimateQuoteTransactionMaster = IncomeEstimateQuoteTransactionMaster::first();
        $estimateQuoteConfiguration = IncomeEstimateQuoteConfiguration::first();
        // TO_COMMENT =>
        $response['companyId'] = getCurrentCompany()->id ?? null;
        $response['company_id'] = getCurrentCompany()->id ?? null;
        $response['is_gst_applicable'] = getCurrentCompany()->is_gst_applicable ?? null;

        $response['document_prefix'] = [
            'method_of_voucher_number' => $estimateQuoteTransactionMaster->method_of_voucher_number ?? IncomeEstimateQuoteTransactionMaster::AUTOMATIC_MANUAL_OVERRIDE,
            'start_with_number' => $estimateQuoteTransactionMaster->start_with_number ?? 1,
            'change_every_financial_year' => $estimateQuoteTransactionMaster->is_change_every_financial_year ?? 0,
            'prefix_method' => $estimateQuoteTransactionMaster->prefix_method ?? IncomeEstimateQuoteTransactionMaster::PREFIX_TEXT,
            'prefix' => $estimateQuoteTransactionMaster->prefix ?? null,
            'suffix_method' => $estimateQuoteTransactionMaster->suffix_method ?? IncomeEstimateQuoteTransactionMaster::PREFIX_TEXT,
            'suffix' => $estimateQuoteTransactionMaster->suffix ?? null,
            'default_title' => $estimateQuoteTransactionMaster->default_title ?? null,
            'bank_id' => $estimateQuoteTransactionMaster->bank_id ?? null,
            'terms_and_conditions' => $estimateQuoteTransactionMaster->term_and_condition ?? null,
        ];

        $response['header'] = [
            'is_change_gst_details' => $estimateQuoteConfiguration->is_change_gst_details,
            'is_enabled_dispatch_details' => $estimateQuoteConfiguration->is_enabled_dispatch_details,
            'is_enabled_shipping_address' => $estimateQuoteConfiguration->is_enabled_shipping_address,
            'is_enabled_broker_details' => $estimateQuoteConfiguration->is_enabled_broker_details,
            'is_enabled_transport_details' => $estimateQuoteConfiguration->is_enabled_transport_details,
            'is_enabled_po_details_of_buyer' => $estimateQuoteConfiguration->is_enabled_po_details_of_buyer,
            'is_enabled_credit_period_details' => $estimateQuoteConfiguration->is_enabled_credit_period_details,
            'is_enabled_phone_number' => $estimateQuoteConfiguration->is_enabled_phone_number,
            'custom_fields' => GetCustomFieldsAction::run(TransactionCustomField::INCOME_ESTIMATE_QUOTE),
        ];

        $response['item_table_configuration'] = [
            'is_additional_ledger_description' => $estimateQuoteConfiguration->is_additional_ledger_description,
            'is_enabled_mrp' => $estimateQuoteConfiguration->is_enabled_mrp,
            'consolidating_items_to_invoice' => $estimateQuoteConfiguration->consolidating_items_to_invoice,
            'warn_on_negative_stock' => $estimateQuoteConfiguration->warn_on_negative_stock,
            'is_additional_item_description' => $estimateQuoteConfiguration->is_additional_item_description,
            'is_enabled_discount_2' => $estimateQuoteConfiguration->is_enabled_discount_2,
            'is_enabled_hsn_code' => $estimateQuoteConfiguration->is_enabled_hsn_code,
            'show_item_image' => $estimateQuoteConfiguration->show_item_image,
            'with_tax' => $estimateQuoteConfiguration->with_tax,
        ];

        $response['footer'] = [
            'is_enable_narration' => $estimateQuoteConfiguration->is_enable_narration,
            'is_enabled_terms_and_conditions' => $estimateQuoteConfiguration->is_enabled_terms_and_conditions,
            'is_enabled_tcs_details' => $estimateQuoteConfiguration->is_enabled_tcs_details,
            'is_enabled_tds_details' => $estimateQuoteConfiguration->is_enabled_tds_details,
            'is_enabled_bank_details' => $estimateQuoteConfiguration->is_enabled_bank_details,
            'round_off_method' => $estimateQuoteTransactionMaster->round_off_method ?? 3,
        ];

        return $response;
    }
}
