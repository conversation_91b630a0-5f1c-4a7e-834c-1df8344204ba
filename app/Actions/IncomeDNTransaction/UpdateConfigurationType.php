<?php

namespace App\Actions\IncomeDNTransaction;

use App\Models\Configuration\IncomeDebitNote;
use App\Models\Master\IncomeDebitNoteTransactionMaster;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateConfigurationType
{
    use AsAction;

    public function handle($input)
    {
        $incomeDebitNoteConfiguration = IncomeDebitNote::first();

        if (! $incomeDebitNoteConfiguration) {
            throw new \Exception('Sale Configuration not found');
        }

        if (isset($input['is_change_every_financial_year'])) {
            $incomeDebitNoteTransactionMaster = IncomeDebitNoteTransactionMaster::first();
            $incomeDebitNoteTransactionMaster->update([
                'is_change_every_financial_year' => $input['is_change_every_financial_year'],
            ]);

            return true;
        }

        if (isset($input['round_off_method'])) {
            $incomeDebitNoteTransactionMaster = IncomeDebitNoteTransactionMaster::first();
            $incomeDebitNoteTransactionMaster->update([
                'round_off_method' => $input['round_off_method'],
            ]);

            return true;
        }

        if (array_key_exists('terms_and_conditions', $input)) {
            $incomeDebitNoteTransactionMaster = IncomeDebitNoteTransactionMaster::first();
            $incomeDebitNoteTransactionMaster->update([
                'set_alter_declaration' => $input['terms_and_conditions'] ?? null,
            ]);

            return true;
        }

        if (isset($input['on_pay_ledger_id'])) {
            $incomeDebitNoteTransactionMaster = IncomeDebitNoteTransactionMaster::first();
            $incomeDebitNoteTransactionMaster->update([
                'on_pay_ledger_id' => $input['on_pay_ledger_id'],
            ]);

            return true;
        }

        $validKeys = [
            'is_enabled_dispatch_details', 'is_enabled_shipping_address',
            'is_enabled_broker_details', 'is_enabled_transport_details', 'is_enabled_po_details_of_buyer',
            'is_enabled_credit_period_details', 'is_additional_ledger_description', 'consolidating_items_to_invoice', 'warn_on_negative_stock',
            'is_additional_item_description', 'is_enable_narration', 'is_enabled_terms_and_conditions', 'is_enabled_tcs_details',
            'is_enabled_tds_details', 'is_enabled_mrp', 'is_enabled_payment_details', 'is_change_gst_details',
            'is_enabled_discount_2', 'is_enabled_phone_number', 'is_enabled_hsn_code', 'show_item_image', 'is_enabled_bank_details','with_tax',
        ];

        $validInput = array_intersect_key($input, array_flip($validKeys));

        foreach ($validInput as $key => $value) {
            $incomeDebitNoteConfiguration->{$key} = $value;
        }

        try {
            $incomeDebitNoteConfiguration->save();
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return false;
        }

        return true;
    }
}
