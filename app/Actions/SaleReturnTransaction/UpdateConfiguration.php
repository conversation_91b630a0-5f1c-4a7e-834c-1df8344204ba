<?php

namespace App\Actions\SaleReturnTransaction;

use App\Jobs\UpdateIncomeBankDetails;
use App\Models\Configuration\IncomeSalesReturn;
use App\Models\Master\IncomeReturnTransactionMaster;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateConfiguration
{
    use AsAction;

    public function handle($input)
    {
        $saleReturnConfigurationData = [
            'is_change_gst_details' => $input['is_change_gst_details'] ?? false,
            'is_enable_free_quantity' => $input['is_enable_free_quantity'] ?? false,
            'is_enabled_dispatch_details' => $input['is_enabled_dispatch_details'] ?? false,
            'is_enabled_shipping_address' => $input['is_enabled_shipping_address'] ?? false,
            'is_enabled_broker_details' => $input['is_enabled_broker_details'] ?? false,
            'is_enabled_transport_details' => $input['is_enabled_transport_details'] ?? false,
            'is_enabled_po_details_of_buyer' => $input['is_enabled_po_details_of_buyer'] ?? false,
            'is_enabled_credit_period_details' => $input['is_enabled_credit_period_details'] ?? false,
            'is_additional_ledger_description' => $input['is_additional_ledger_description'] ?? false,
            'consolidating_items_to_invoice' => $input['consolidating_items_to_invoice'] ?? false,
            'is_additional_item_description' => $input['is_additional_item_description'] ?? false,
            'is_enable_narration' => $input['is_enable_narration'] ?? false,
            'is_enabled_terms_and_conditions' => $input['is_enabled_terms_and_conditions'] ?? false,
            'is_enabled_tcs_details' => $input['is_enabled_tcs_details'] ?? false,
            'is_enabled_tds_details' => $input['is_enabled_tds_details'] ?? false,
            'is_enabled_mrp' => $input['is_enabled_mrp'] ?? false,
            'is_enabled_payment_details' => $input['is_enabled_payment_details'] ?? false,
            'is_enabled_discount_2' => $input['is_enabled_discount_2'] ?? false,
            'is_enabled_hsn_code' => $input['is_enabled_hsn_code'] ?? false,
            'is_enabled_phone_number' => $input['is_enabled_phone_number'] ?? false,
        ];

        $saleReturnTransactionMaster = IncomeReturnTransactionMaster::first();

        $saleReturnTransactionMasterData = [
            'round_off_method' => $input['round_off_method'] ?? null,
            'method_of_voucher_number' => $input['method_of_voucher_number'] ?? null,
            'start_with_number' => $input['start_with_number'] ?? null,
            'print_voucher_after_saving' => isset($saleReturnTransactionMaster->print_voucher_after_saving) ? $saleReturnTransactionMaster->print_voucher_after_saving : IncomeReturnTransactionMaster::PRINT_VOUCHER_AFTER_SAVING_YES,
            'is_change_every_financial_year' => $input['is_change_every_financial_year'] ?? null,
            'prefix_method' => $input['prefix_method'] ?? null,
            'prefix' => $input['prefix'] ?? null,
            'suffix_method' => $input['suffix_method'] ?? null,
            'suffix' => $input['suffix'] ?? null,
            'bank_id' => $input['bank_id'] ?? null,
            'title_of_print' => $input['title_of_print'] ?? null,
            'set_alter_declaration' => $input['terms_and_conditions'] ?? null,
            'on_pay_ledger_id' => $input['on_pay_ledger_id'] ?? null,
        ];

        $saleReturnConfigurationId = IncomeSalesReturn::first()?->id;

        IncomeSalesReturn::updateOrCreate(
            ['id' => $saleReturnConfigurationId],
            $saleReturnConfigurationData
        );

        if (isset($input['show_item_image'])) {
            IncomeSalesReturn::updateOrCreate(
                ['id' => $saleReturnConfigurationId],
                ['show_item_image' => $input['show_item_image']]
            );
        }

        if (isset($input['with_tax'])) {
            IncomeSalesReturn::updateOrCreate(
                ['id' => $saleReturnConfigurationId],
                ['with_tax' => $input['with_tax']]
            );
        }

        if (isset($input['is_enabled_bank_details'])) {
            IncomeSalesReturn::updateOrCreate(
                ['id' => $saleReturnConfigurationId],
                ['is_enabled_bank_details' => $input['is_enabled_bank_details']]
            );
        }

        IncomeReturnTransactionMaster::updateOrCreate(
            ['id' => $saleReturnTransactionMaster->id],
            $saleReturnTransactionMasterData
        );

        // Update Bank Details for All Transactions
        if (isset($input['apply_income_bank_details']) && $input['apply_income_bank_details'] == 1) {
            $bankId = $input['bank_id'];
            if (! $bankId) {
                throw new \Exception('Bank ID is required to update income transaction bank details.');
            }
            $companyId = getCurrentCompany()->id;

            UpdateIncomeBankDetails::dispatch($companyId, $bankId);
        }

        return true;
    }
}
