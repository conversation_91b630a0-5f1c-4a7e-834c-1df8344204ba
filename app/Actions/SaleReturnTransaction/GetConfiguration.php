<?php

namespace App\Actions\SaleReturnTransaction;

use App\Actions\CustomFields\GetCustomFieldsAction;
use App\Models\Configuration\IncomeSalesReturn;
use App\Models\Master\IncomeReturnTransactionMaster;
use App\Models\TransactionCustomField;
use Lorisleiva\Actions\Concerns\AsAction;

class GetConfiguration
{
    use AsAction;

    public function handle()
    {
        $response = [];

        $saleReturnTransactionMaster = IncomeReturnTransactionMaster::first();
        $saleReturnConfiguration = IncomeSalesReturn::first();
        // TO_COMMENT =>
        $response['companyId'] = getCurrentCompany()->id ?? null;
        $response['company_id'] = getCurrentCompany()->id ?? null;
        $response['is_gst_applicable'] = getCurrentCompany()->is_gst_applicable ?? null;

        $response['document_prefix'] = [
            'method_of_voucher_number' => $saleReturnTransactionMaster->method_of_voucher_number ?? IncomeReturnTransactionMaster::AUTOMATIC_MANUAL_OVERRIDE,
            'start_with_number' => $saleReturnTransactionMaster->start_with_number ?? 1,
            'change_every_financial_year' => $saleReturnTransactionMaster->is_change_every_financial_year ?? 0,
            'prefix_method' => $saleReturnTransactionMaster->prefix_method ?? IncomeReturnTransactionMaster::PREFIX_TEXT,
            'prefix' => $saleReturnTransactionMaster->prefix ?? null,
            'suffix_method' => $saleReturnTransactionMaster->suffix_method ?? IncomeReturnTransactionMaster::PREFIX_TEXT,
            'suffix' => $saleReturnTransactionMaster->suffix ?? null,
            'bank_id' => $saleReturnTransactionMaster->bank_id ?? null,
            'title_of_print' => $saleReturnTransactionMaster->title_of_print ?? null,
            'terms_and_conditions' => $saleReturnTransactionMaster->set_alter_declaration ?? null,
        ];

        $response['header'] = [
            'is_change_gst_details' => $saleReturnConfiguration->is_change_gst_details,
            'is_enabled_dispatch_details' => $saleReturnConfiguration->is_enabled_dispatch_details,
            'is_enabled_shipping_address' => $saleReturnConfiguration->is_enabled_shipping_address,
            'is_enabled_broker_details' => $saleReturnConfiguration->is_enabled_broker_details,
            'is_enabled_transport_details' => $saleReturnConfiguration->is_enabled_transport_details,
            'is_enabled_po_details_of_buyer' => $saleReturnConfiguration->is_enabled_po_details_of_buyer,
            'is_enabled_credit_period_details' => $saleReturnConfiguration->is_enabled_credit_period_details,
            'is_enabled_phone_number' => $saleReturnConfiguration->is_enabled_phone_number,
            'custom_fields' => GetCustomFieldsAction::run(TransactionCustomField::SALE_RETURN),
        ];

        $response['item_table_configuration'] = [
            'is_enable_free_quantity' => $saleReturnConfiguration->is_enable_free_quantity,
            'is_additional_ledger_description' => $saleReturnConfiguration->is_additional_ledger_description,
            'is_enabled_mrp' => $saleReturnConfiguration->is_enabled_mrp,
            'consolidating_items_to_invoice' => $saleReturnConfiguration->consolidating_items_to_invoice,
            'is_additional_item_description' => $saleReturnConfiguration->is_additional_item_description,
            'is_enabled_discount_2' => $saleReturnConfiguration->is_enabled_discount_2,
            'is_enabled_hsn_code' => $saleReturnConfiguration->is_enabled_hsn_code,
            'show_item_image' => $saleReturnConfiguration->show_item_image,
            'with_tax' => $saleReturnConfiguration->with_tax,
        ];

        $response['footer'] = [
            'is_enable_narration' => $saleReturnConfiguration->is_enable_narration,
            'is_enabled_terms_and_conditions' => $saleReturnConfiguration->is_enabled_terms_and_conditions,
            'is_enabled_tcs_details' => $saleReturnConfiguration->is_enabled_tcs_details,
            'is_enabled_tds_details' => $saleReturnConfiguration->is_enabled_tds_details,
            'is_enabled_payment_details' => $saleReturnConfiguration->is_enabled_payment_details,
            'is_enabled_bank_details' => $saleReturnConfiguration->is_enabled_bank_details,
            'round_off_method' => $saleReturnTransactionMaster->round_off_method ?? 3,
        ];

        return $response;
    }
}
