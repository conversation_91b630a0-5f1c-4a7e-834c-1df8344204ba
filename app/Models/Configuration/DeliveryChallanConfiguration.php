<?php

namespace App\Models\Configuration;

use App\Traits\HasCompany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Configuration\DeliveryChallanConfiguration
 *
 * @property int $id
 * @property int $company_id
 * @property bool $is_enabled_shipping_address
 * @property bool $is_enabled_transport_details
 * @property bool $is_enabled_po_details_of_buyer
 * @property bool $consolidating_items_to_invoice
 * @property bool $warn_on_negative_stock
 * @property bool $is_enabled_broker_details
 * @property bool $is_additional_item_description
 * @property bool $is_enable_narration
 * @property bool $is_enabled_dispatch_details
 * @property bool $is_enabled_terms_and_conditions
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Database\Factories\Configuration\DeliveryChallanConfigurationFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration query()
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration whereConsolidatingItemsToInvoice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration whereIsAdditionalItemDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration whereIsEnableNarration($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration whereIsEnabledBrokerDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration whereIsEnabledDispatchDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration whereIsEnabledPoDetailsOfBuyer($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration whereIsEnabledShippingAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration whereIsEnabledTermsAndConditions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration whereIsEnabledTransportDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeliveryChallanConfiguration whereWarnOnNegativeStock($value)
 *
 * @mixin \Eloquent
 */
class DeliveryChallanConfiguration extends Model
{
    use HasCompany,HasFactory;

    public $table = 'delivery_challan_configurations';

    public $fillable = [
        'is_additional_item_description',
        'is_additional_ledger_description',
        'is_enable_narration',
        'is_enabled_broker_details',
        'is_enabled_credit_period_details',
        'is_enabled_po_details_of_buyer',
        'is_enabled_shipping_address',
        'is_enabled_transport_details',
        'consolidating_items_to_invoice',
        'is_change_gst_details',
        'warn_on_negative_stock',
        'is_enabled_tcs_details',
        'is_enabled_estimate_quote',
        'is_enabled_mrp',
        'is_enabled_delivery_challan',
        'is_enabled_dispatch_details',
        'is_enabled_terms_and_conditions',
        'is_enabled_tds_details',
        'is_enabled_payment_details',
        'is_enabled_discount_2',
        'is_enabled_phone_number',
        'is_enabled_hsn_code',
        'show_item_image',
        'company_id',
        'with_tax',
    ];

    public $casts = [
        'company_id' => 'integer',
        'is_additional_item_description' => 'boolean',
        'is_additional_ledger_description' => 'boolean',
        'is_enable_narration' => 'boolean',
        'is_enabled_broker_details' => 'boolean',
        'is_enabled_credit_period_details' => 'boolean',
        'is_enabled_po_details_of_buyer' => 'boolean',
        'is_enabled_shipping_address' => 'boolean',
        'is_enabled_transport_details' => 'boolean',
        'consolidating_items_to_invoice' => 'boolean',
        'is_change_gst_details' => 'boolean',
        'warn_on_negative_stock' => 'boolean',
        'is_enabled_tcs_details' => 'boolean',
        'is_enabled_estimate_quote' => 'boolean',
        'is_enabled_mrp' => 'boolean',
        'is_enabled_delivery_challan' => 'boolean',
        'is_enabled_dispatch_details' => 'boolean',
        'is_enabled_terms_and_conditions' => 'boolean',
        'is_enabled_tds_details' => 'boolean',
        'is_enabled_payment_details' => 'boolean',
        'is_enabled_discount_2' => 'boolean',
        'is_enabled_phone_number' => 'boolean',
        'is_enabled_hsn_code' => 'boolean',
        'show_item_image' => 'boolean',
        'with_tax' => 'boolean',
    ];
}
