<?php

namespace App\Actions\PurchaseReturn;

use App\Models\Configuration\PurchaseReturnConfiguration;
use App\Models\Master\ExpenseReturnTransactionMaster;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateConfigurationType
{
    use AsAction;

    public function handle($input)
    {
        $purchaseReturnConfiguration = PurchaseReturnConfiguration::first();

        if (! $purchaseReturnConfiguration) {
            throw new \Exception('Purchase Order Configuration not found');
        }

        if (isset($input['is_change_every_financial_year'])) {
            $purchaseReturnTransactionMaster = ExpenseReturnTransactionMaster::first();
            $purchaseReturnTransactionMaster->update([
                'is_change_every_financial_year' => $input['is_change_every_financial_year'],
            ]);

            return true;
        }

        if (isset($input['round_off_method'])) {
            $purchaseReturnTransactionMaster = ExpenseReturnTransactionMaster::first();
            $purchaseReturnTransactionMaster->update([
                'round_off_method' => $input['round_off_method'],
            ]);

            return true;
        }

        if (isset($input['on_pay_ledger_id'])) {
            $purchaseReturnTransactionMaster = ExpenseReturnTransactionMaster::first();
            $purchaseReturnTransactionMaster->update([
                'on_pay_ledger_id' => $input['on_pay_ledger_id'],
            ]);

            return true;
        }

        $validKeys = [
            'is_enabled_shipping_address', 'is_enabled_broker_details', 'is_enabled_transport_details', 'is_additional_ledger_description',
            'consolidating_items_to_invoice', 'is_additional_item_description', 'is_enable_narration', 'is_change_gst_details',
            'is_enabled_discount_2', 'warn_on_negative_stock', 'is_enabled_tcs_details', 'is_enabled_tds_details', 'is_enabled_mrp',
            'is_enabled_payment_details', 'is_enabled_eway_details', 'is_enabled_hsn_code', 'is_enable_free_quantity', 'show_item_image','with_tax',
        ];

        $validInput = array_intersect_key($input, array_flip($validKeys));

        foreach ($validInput as $key => $value) {
            $purchaseReturnConfiguration->{$key} = $value;
        }

        try {
            $purchaseReturnConfiguration->save();
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return false;
        }

        return true;
    }
}
