<?php

namespace App\Actions\PurchaseReturn;

use App\Models\Configuration\PurchaseReturnConfiguration;
use App\Models\Master\ExpenseReturnTransactionMaster;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateConfiguration
{
    use AsAction;

    public function handle($input)
    {

        $purchaseReturnConfigurationData = [
            'is_change_gst_details' => $input['is_change_gst_details'] ?? false,
            'is_enable_free_quantity' => $input['is_enable_free_quantity'] ?? false,
            'is_enabled_shipping_address' => $input['is_enabled_shipping_address'] ?? false,
            'is_enabled_broker_details' => $input['is_enabled_broker_details'] ?? false,
            'is_enabled_eway_details' => $input['is_enabled_eway_details'] ?? false,
            'is_enabled_transport_details' => $input['is_enabled_transport_details'] ?? false,
            'is_additional_ledger_description' => $input['is_additional_ledger_description'] ?? false,
            'consolidating_items_to_invoice' => $input['consolidating_items_to_invoice'] ?? false,
            'is_additional_item_description' => $input['is_additional_item_description'] ?? false,
            'warn_on_negative_stock' => $input['warn_on_negative_stock'] ?? false,
            'is_enable_narration' => $input['is_enable_narration'] ?? false,
            'is_enabled_tcs_details' => $input['is_enabled_tcs_details'] ?? false,
            'is_enabled_tds_details' => $input['is_enabled_tds_details'] ?? false,
            'is_enabled_payment_details' => $input['is_enabled_payment_details'] ?? false,
            'is_enabled_discount_2' => $input['is_enabled_discount_2'] ?? false,
            'is_enabled_hsn_code' => $input['is_enabled_hsn_code'] ?? false,
            'is_enabled_mrp' => $input['is_enabled_mrp'] ?? false,
        ];

        $purchaseReturnTransactionMasterData = [
            'round_off_method' => $input['round_off_method'] ?? null,
            'voucher_number' => $input['method_of_voucher_number'] ?? null,
            'is_change_every_financial_year' => $input['is_change_every_financial_year'] ?? false,
            'default_title_of_print' => $input['title_of_print'] ?? null,
            'on_pay_ledger_id' => $input['on_pay_ledger_id'] ?? null,
        ];

        $purchaseReturnConfigurationId = PurchaseReturnConfiguration::first()?->id;
        $purchaseReturnTransactionMasterId = ExpenseReturnTransactionMaster::first()?->id;

        PurchaseReturnConfiguration::updateOrCreate(
            ['id' => $purchaseReturnConfigurationId],
            $purchaseReturnConfigurationData
        );

        if (isset($input['show_item_image'])) {
            PurchaseReturnConfiguration::updateOrCreate(
                ['id' => $purchaseReturnConfigurationId],
                ['show_item_image' => $input['show_item_image']]
            );
        }

        if (isset($input['with_tax'])) {
            PurchaseReturnConfiguration::updateOrCreate(
                ['id' => $purchaseReturnConfigurationId],
                ['with_tax' => $input['with_tax']]
            );
        }

        ExpenseReturnTransactionMaster::updateOrCreate(
            ['id' => $purchaseReturnTransactionMasterId],
            $purchaseReturnTransactionMasterData
        );

        return true;
    }
}
