<?php

namespace App\Models\Configuration;

use App\Traits\HasCompany;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\Configuration\ExpensePurchase
 *
 * @property int $id
 * @property int $company_id
 * @property bool $is_enabled_shipping_address
 * @property bool $is_enabled_broker_details
 * @property bool $is_enabled_transport_details
 * @property bool $is_additional_item_description
 * @property bool $is_additional_ledger_description
 * @property bool $is_enable_narration
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 *
 * @method static Builder|PurchaseReturnConfiguration newModelQuery()
 * @method static Builder|PurchaseReturnConfiguration newQuery()
 * @method static Builder|PurchaseReturnConfiguration query()
 * @method static Builder|PurchaseReturnConfiguration whereCompanyId($value)
 * @method static Builder|PurchaseReturnConfiguration whereCreatedAt($value)
 * @method static Builder|PurchaseReturnConfiguration whereId($value)
 * @method static Builder|PurchaseReturnConfiguration whereIsAdditionalItemDescription($value)
 * @method static Builder|PurchaseReturnConfiguration whereIsAdditionalLedgerDescription($value)
 * @method static Builder|PurchaseReturnConfiguration whereIsEnableNarration($value)
 * @method static Builder|PurchaseReturnConfiguration whereIsEnabledBrokerDetails($value)
 * @method static Builder|PurchaseReturnConfiguration whereIsEnabledShippingAddress($value)
 * @method static Builder|PurchaseReturnConfiguration whereIsEnabledTransportDetails($value)
 * @method static Builder|PurchaseReturnConfiguration whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property bool $consolidating_items_to_invoice
 *
 * @method static Builder|PurchaseReturnConfiguration whereConsolidatingItemsToInvoice($value)
 *
 * @property bool $is_change_gst_details
 *
 * @method static Builder|PurchaseReturnConfiguration whereIsChangeGstDetails($value)
 *
 * @property bool $warn_on_negative_stock
 *
 * @method static Builder|PurchaseReturnConfiguration whereWarnOnNegativeStock($value)
 */
class PurchaseReturnConfiguration extends Model
{
    use HasCompany;
    use HasFactory;

    public $table = 'expense_purchase_configuration';

    public $fillable = [
        'is_enabled_shipping_address',
        'is_enabled_broker_details',
        'is_enabled_transport_details',
        'is_enabled_credit_period_details',
        'is_enabled_po_details_of_buyer',
        'is_additional_item_description',
        'is_additional_ledger_description',
        'is_enable_narration',
        'consolidating_items_to_invoice',
        'is_change_gst_details',
        'is_enable_free_quantity',
        'warn_on_negative_stock',
        'is_enabled_tcs_details',
        'company_id',
        'is_enabled_mrp',
        'is_enabled_tds_details',
        'is_enabled_payment_details',
        'is_enabled_discount_2',
        'is_enabled_eway_details',
        'is_enabled_hsn_code',
        'show_item_image',
        'with_tax',
    ];

    public $casts = [
        'company_id' => 'integer',
        'is_additional_item_description' => 'boolean',
        'is_additional_ledger_description' => 'boolean',
        'is_enable_narration' => 'boolean',
        'is_enabled_broker_details' => 'boolean',
        'is_enabled_shipping_address' => 'boolean',
        'is_enabled_transport_details' => 'boolean',
        'consolidating_items_to_invoice' => 'boolean',
        'is_change_gst_details' => 'boolean',
        'is_enable_free_quantity' => 'boolean',
        'warn_on_negative_stock' => 'boolean',
        'is_enabled_tcs_details' => 'boolean',
        'is_enabled_mrp' => 'boolean',
        'is_enabled_tds_details' => 'boolean',
        'is_enabled_payment_details' => 'boolean',
        'is_enabled_discount_2' => 'boolean',
        'is_enabled_eway_details' => 'boolean',
        'is_enabled_hsn_code' => 'boolean',
        'show_item_image' => 'boolean',
        'with_tax' => 'boolean',
    ];
}
