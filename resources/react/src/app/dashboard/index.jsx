import React, { useEffect, useRef, useState } from "react";
import { Container } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import Toast from "../../components/ui/Toast";
import DashboardHeader from "./DashboardHeader";
import DashboardCharts from "./DashboardCharts";
import DashboardCard from "./DashboardCard";
import DashboardPerformances from "./DashboardPerformances";
import {
    fetchDashboard,
    fetchDashboardCardData,
    fetchDashboardCashFlowData,
    fetchDashboardPerformanceData,
    fetchWalkthroughStatus,
    walkthroughComplete,
    walkthroughSelect,
} from "../../store/dashboard/dashboardSlice";
import { driver } from "driver.js";
import "driver.js/dist/driver.css";
import { toast } from "react-toastify";
import { useTransactionShortcuts } from "../../shared/shortcut-keys";
import { fetchCompanyDetails } from "../../store/company/companySlice";
import CustomHelmet from "../../shared/helmet";
import TopAndLeastItems from "./TopAndLeastItems";


const Dashboard = () => {
    const [loading, setLoading] = useState(true);
    const [cardLoading, setCardLoading] = useState(true);
    const [perfomanceLoading, setPerfomanceLoading] = useState(true);
    const [cashFlowLoading, setCashFlowLoading] = useState(true);
    const [isBookDemo, setIsBookDemo] = useState(false);
    const [showOverlay, setShowOverlay] = useState(true);
    const [lastShowOverlay, setLastShowOverlay] = useState(false);
    const [walkthroughStatus, setWalkthroughStatus] = useState(null);
    const { company } = useSelector(state => state.company);
    const companyRef = useRef();

    useEffect(() => {
        companyRef.current = company;
    }, [company]);

    //shortcut-key
    useTransactionShortcuts()

    useEffect(() => {
        const el = document.getElementById("showName");
        if (el) {
            el.innerHTML = "Dashboard";
        }
    }, []);

    useEffect(() => {
        const tooltipTriggerList = [].slice.call(
            document?.querySelectorAll('[data-bs-toggle="tooltip"]')
        );
        tooltipTriggerList?.forEach(tooltipTriggerEl => {
            new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }, []);

    const dispatch = useDispatch();

    useEffect(() => {
        dispatch(fetchDashboard(setLoading));
        dispatch(fetchDashboardCardData(setCardLoading));
        dispatch(fetchDashboardPerformanceData(setPerfomanceLoading));
        dispatch(fetchDashboardCashFlowData(setCashFlowLoading));
        dispatch(fetchCompanyDetails());
    }, []);

    useEffect(() => {
        (async () => {
            if (company?.user_id) {
                const response = await fetchWalkthroughStatus(company?.user_id);
                if (response?.success) {
                    setWalkthroughStatus(response?.data);
                }
            }
        })();
    }, [company?.user_id]);

    const smoothScrollToElement = (selector) => {
        const element = document?.querySelector(selector);
        if (element) {
            element.scrollIntoView({
                behavior: "smooth",
                block: "center",
                inline: "center",
            });
        }
    };

    const startTour = () => {
        const tour = driver({
            showProgress: true,
            overlayColor: "#4F158C",
            allowClose: false,
            showButtons: ["close"],
            animate: true,
            padding: 10,

            onPopoverRender: (popover, { config, state }) => {

                const nextButton = document.createElement("button");
                nextButton.innerText = "Next";
                nextButton.classList.add("custom-next-button");

                nextButton.addEventListener("click", () => {
                    tour.moveNext();
                });

                const firstButton = document.createElement("button");
                firstButton.innerText = "Skip";
                firstButton.classList.add("skip-button");

                firstButton.addEventListener("click", () => {
                    document.body.style.overflow = "auto";
                    tour.destroy();
                });

                popover.footerButtons.appendChild(firstButton);
                popover.footerButtons.appendChild(nextButton);
            },
            onDestroyed: () => {
                document.body.style.overflow = "auto";
                setLastShowOverlay(true);
            },
            steps: [
                {
                    element: "#kt_aside",
                    popover: {
                        title: "Explore the Side Menu",
                        description:
                            "Quickly navigate to Transactions, Master, Reports to manage accounts and track Profit and Loss.",
                        position: "bottom",
                        popoverClass: "step-1",
                    },
                    onNext: () => {
                        smoothScrollToElement("#kt_aside_menu_wrapper")
                        document.body.style.overflow = "auto"; // Enable scroll on "Next" click
                        setTimeout(() => {
                            document.body.style.overflow = "hidden"; // Hide overflow when the next step starts
                        }, 100);
                    },
                },
                {
                    element: "#kt_header_buttons",
                    popover: {
                        title: "Quick Access to Transactions",
                        description:
                            "Easily add sales, purchases, and other transactions with these shortcut buttons.",
                        position: "bottom",
                        popoverClass: "step-2",
                    },
                    onNext: () => {
                        smoothScrollToElement("#kt_header_buttons")
                        document.body.style.overflow = "auto";
                        setTimeout(() => {
                            document.body.style.overflow = "hidden";
                        }, 100);
                    },
                },
                {
                    element: "#kt_header_right_menu",
                    popover: {
                        title: "Stay Updated & Get Help",
                        description:
                            "Manage notifications, support , and profile settings right from here.",
                        position: "bottom",
                        popoverClass: "step-3",
                    },
                    onNext: () => {
                        smoothScrollToElement("#kt_header_right_menu")
                        document.body.style.overflow = "auto";
                        setTimeout(() => {
                            document.body.style.overflow = "hidden";
                        }, 100);
                    },
                },
                {
                    element: "#kt_aside_logo",
                    popover: {
                        title: "Manage Multiple Businesses",
                        description:
                            "Easily switch between different businesses with just a click.",
                        position: "bottom",
                        popoverClass: "step-4",
                    },
                    onNext: () => {
                        smoothScrollToElement("#kt_aside_logo");
                        document.body.style.overflow = "auto";
                        setTimeout(() => {
                            document.body.style.overflow = "hidden";
                        }, 100);
                    }
                },
                {
                    element: "#generalMenu",
                    popover: {
                        title: "Customize Your Experience",
                        description:
                            "Set up your company profile and preferences to tailor hisabkitab to your needs.",
                        position: "top",
                        align: "start",
                        popoverClass: "step-5",
                    },
                    onNext: () => {
                        smoothScrollToElement("#generalMenu");
                        document.body.style.overflow = "auto";
                        setTimeout(() => {
                            document.body.style.overflow = "hidden";
                        }, 100);
                    },
                },
                {
                    element: "#step1",
                    popover: {
                        title: "Your Business at a Glance",
                        description:
                            "Track Sales & Purchases, Receivables & Payables, and Cash & Bank all in one place.",
                        position: "bottom",
                        popoverClass: "step-6",
                    },
                    onNext: () => {
                        smoothScrollToElement("#step1");
                        document.body.style.overflow = "auto";
                        setTimeout(() => {
                            document.body.style.overflow = "hidden";
                        }, 100);
                    },
                },
                {
                    element: "#step2",
                    popover: {
                        title: "Your Key Reports, One Click Away",
                        description:
                            "Pin your most used reports here for easy access, stay on top of what matters.",
                        position: "bottom",
                        popoverClass: "step-7",
                    },
                    onNext: () => {
                        smoothScrollToElement("#step2");
                        document.body.style.overflow = "auto";
                        setTimeout(() => {
                            document.body.style.overflow = "hidden";
                        }, 100);
                    },
                },
                {
                    element: "#step3",
                    popover: {
                        title: "Monitor Your Growth",
                        description:
                            "View performance insights for sales, purchases, expenses, and profit trends across different timeframes.",
                        position: "bottom",
                        popoverClass: "step-8",
                    },
                    onNext: () => {
                        smoothScrollToElement("#step3");
                        document.body.style.overflow = "auto";
                        setTimeout(() => {
                            document.body.style.overflow = "hidden";
                        }, 100);
                    },
                },
                {
                    element: "#step4",
                    popover: {
                        title: "Visualize Your Transactions",
                        description:
                            "Track Sales vs. Collections and Purchases vs. Payments with interactive bar graphs.",
                        position: "bottom",
                        popoverClass: "step-9",
                    },
                    onNext: () => {
                        smoothScrollToElement("#step4");
                        document.body.style.overflow = "auto";
                        setTimeout(() => {
                            document.body.style.overflow = "hidden";
                        }, 100);
                    },
                },
                {
                    element: "#step5",
                    popover: {
                        title: "Track Key Metrics for Better Decisions",
                        description: `
                            <ul style="padding-left: 20px; margin-top: 10px;">
                                <li>Cash Flow Summary: <b>Monitor your opening balance, inflows, outflows, and closing balance to stay on top of liquidity.</b></li>
                            </ul>
                        `,
                        position: "bottom",
                        popoverClass: "step-10",
                    },
                    onNext: () => {
                        smoothScrollToElement("#step5");
                        tour.destroy();
                        setLastShowOverlay(true);
                    },
                },
            ],
        });
        document.body.style.overflow = "hidden";
        // Start the tour
        tour.drive();
    };

    const selectwalkthroughTour = async type => {
        const selectedType =
            type === "take_tour"
                ? "take_tour"
                : type === "complete_profile"
                    ? "complete_profile"
                    : type === "create_sale"
                        ? "create_sale"
                        : "";

        const params = {
            selected_option: selectedType,
            user_id: company?.user_id,
        };
        try {
            const response = await walkthroughSelect(params);

            if (response?.success) {
                if (response.data?.redirect_url) {
                    setShowOverlay(false);
                    window.location.href = response.data?.redirect_url;
                } else if (response.data?.start_tour) {
                    setShowOverlay(false);
                    startTour();
                }
            }
        } catch (error) {
            console.log(error);
        }
    };
    const closeWalkthroughTour = async () => {
        try {
            const params = {
                selected_option: "close",
                user_id: company?.user_id,
            };
          const response = await walkthroughComplete(params);

            if (response?.success) {
                setShowOverlay(false);
                setLastShowOverlay(false);
            }
        } catch (error) {
            console.log(error);
            setShowOverlay(false);
            setLastShowOverlay(false);
        }
    };

    const handleMissingTourClose = async () => {
        const currentCompany = companyRef.current;
    
        const params = {
            selected_option: "close",
            user_id: currentCompany?.user_id,
        };
        await walkthroughComplete(params);
    };

    const showWalkthroughToast = () => {
        toast(
            <div className="custom-toast position-relative">
                <div className="icon-wrapper">
                    <span className="icon">⚠️</span>
                </div>
                <div className="text-content">
                    <strong>Missed the tour?</strong>
                    <p>Take a quick walkthrough to get familiar with your dashboard!</p>
                    <button className="watch-btn" onClick={startTour}>
                        Watch Walkthrough
                    </button>
                </div>
                <button
                    className="btn close-button p-0 missing_tour_close_btn "
                    onClick={handleMissingTourClose}
                    type="button"
                >
                    &times;
                </button>
            </div>,
            {
                position: "top-center",
                autoClose: 3000,
                closeOnClick: true,
                draggable: false,
                hideProgressBar: true,
                closeButton: false,
                width: 600,
            }
        );
    };

    useEffect(() => {
        if (walkthroughStatus?.show_notification) {
            showWalkthroughToast();
        }
    }, [walkthroughStatus]);

    const handleBookADemo = async () => {
        try {
            const params = {
                user_id: company?.user_id,
                selected_option: "book_demo",
            };
            const response = await walkthroughComplete(params);
            if (response?.success) {
                setIsBookDemo(true);
                setShowOverlay(false);
                setLastShowOverlay(false);
            }
        } catch {
            setIsBookDemo(true);
            setShowOverlay(false);
            setLastShowOverlay(false);
        }
    };

    const handleCreateSale = async () => {
        try {
            const params = {
                selected_option: "create_sale",
                user_id: company?.user_id,
            };

            const completeParams = {
                selected_option: "create_sale",
                user_id: company?.user_id,
            };

            const response = await walkthroughSelect(params);
            const completeResponse = await walkthroughComplete(completeParams);
            if (response?.success && completeResponse?.success) {
                window.location.href = response.data?.redirect_url;
                setShowOverlay(false);
                setLastShowOverlay(false);
            }
        } catch (error) {
            console.log(error);
            setShowOverlay(false);
            setLastShowOverlay(false);
        }
    };

    return (
        <>
            <CustomHelmet title={"Dashboard"} />
            {/* <span onClick={startTour}>start</span> */}
            <>
                {showOverlay && walkthroughStatus?.show_tour && (
                    <div className="overlay driver-end-overlay">
                        <div className="welcome-box">
                            <h3>Welcome to hisabkitab!</h3>
                            <p>Let’s get you familiar with your Dashboard!</p>
                            <div className="button-container">
                                <button
                                    className="btn-orange-overlay"
                                    onClick={() => selectwalkthroughTour("create_sale")}
                                >
                                    Create Sale
                                </button>
                                <button
                                    className="btn-orange-overlay"
                                    onClick={() => selectwalkthroughTour("complete_profile")}
                                >
                                    Complete Profile
                                </button>
                                <button
                                    className="btn-white-overlay"
                                    onClick={() => selectwalkthroughTour("take_tour")}
                                >
                                    Take Tour
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {lastShowOverlay && (
                    <div className="overlay driver-end-overlay">
                        <div className="welcome-box">
                            <h3>Wrap-Up</h3>
                            <p>
                                That’s it! You’re all set to navigate and manage your finances
                                with hisabkitab.
                            </p>
                            <div className="button-container">
                                <button
                                    className="btn-orange-overlay"
                                    onClick={() => closeWalkthroughTour()}
                                >
                                    Close
                                </button>
                                <button
                                    className="btn-orange-overlay"
                                    onClick={() => handleBookADemo()}
                                >
                                    Book A Demo
                                </button>
                                <button
                                    className="btn-white-overlay"
                                    onClick={() => handleCreateSale()}
                                >
                                    Create Sale
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                    <div className="d-flex flex-column justify-content-between h-100">
                        <div>
                            <Container fluid className="p-0">
                                <DashboardHeader loading={loading} cardLoading={cardLoading} />
                                <DashboardPerformances loading={perfomanceLoading}/>
                                <DashboardCharts loading={perfomanceLoading} cashFlowLoading={cashFlowLoading}/>
                                <TopAndLeastItems />
                                <DashboardCard
                                    isBookDemo={isBookDemo}
                                    setIsBookDemo={setIsBookDemo}
                                    setLoading={setLoading}
                                />
                            </Container>
                        </div>
                    </div>
                </>
            <Toast />
        </>
    );
};

export default Dashboard;
