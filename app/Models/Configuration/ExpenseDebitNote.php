<?php

namespace App\Models\Configuration;

use App\Traits\HasCompany;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\Configuration\ExpenseDebitNote
 *
 * @property int $id
 * @property int $company_id
 * @property int $is_enabled_shipping_address
 * @property int $is_enabled_broker_details
 * @property int $is_enabled_transport_details
 * @property int $is_enabled_credit_period_details
 * @property int $is_enabled_po_details_of_buyer
 * @property int $is_additional_item_description
 * @property int $is_additional_ledger_description
 * @property int $is_enable_narration
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 *
 * @method static Builder|ExpenseDebitNote newModelQuery()
 * @method static Builder|ExpenseDebitNote newQuery()
 * @method static Builder|ExpenseDebitNote query()
 * @method static Builder|ExpenseDebitNote whereCompanyId($value)
 * @method static Builder|ExpenseDebitNote whereCreatedAt($value)
 * @method static Builder|ExpenseDebitNote whereId($value)
 * @method static Builder|ExpenseDebitNote whereIsAdditionalItemDescription($value)
 * @method static Builder|ExpenseDebitNote whereIsAdditionalLedgerDescription($value)
 * @method static Builder|ExpenseDebitNote whereIsEnableNarration($value)
 * @method static Builder|ExpenseDebitNote whereIsEnabledBrokerDetails($value)
 * @method static Builder|ExpenseDebitNote whereIsEnabledCreditPeriodDetails($value)
 * @method static Builder|ExpenseDebitNote whereIsEnabledPoDetailsOfBuyer($value)
 * @method static Builder|ExpenseDebitNote whereIsEnabledShippingAddress($value)
 * @method static Builder|ExpenseDebitNote whereIsEnabledTransportDetails($value)
 * @method static Builder|ExpenseDebitNote whereUpdatedAt($value)
 *
 * @mixin Eloquent
 *
 * @property bool $consolidating_items_to_invoice
 *
 * @method static Builder|ExpenseDebitNote whereConsolidatingItemsToInvoice($value)
 *
 * @property bool $is_change_gst_details
 *
 * @method static Builder|ExpenseDebitNote whereIsChangeGstDetails($value)
 *
 * @property bool $warn_on_negative_stock
 *
 * @method static Builder|ExpenseDebitNote whereWarnOnNegativeStock($value)
 */
class ExpenseDebitNote extends Model
{
    use HasCompany;
    use HasFactory;

    public $table = 'expense_debit_note_configuration';

    public $fillable = [
        'is_enabled_shipping_address',
        'is_enabled_broker_details',
        'is_enabled_transport_details',
        'is_enabled_credit_period_details',
        'is_enabled_po_details_of_buyer',
        'is_additional_item_description',
        'is_additional_ledger_description',
        'is_enable_narration',
        'consolidating_items_to_invoice',
        'is_change_gst_details',
        'warn_on_negative_stock',
        'is_enabled_tcs_details',
        'company_id',
        'is_enabled_mrp',
        'is_enabled_tds_details',
        'is_enabled_payment_details',
        'is_enabled_discount_2',
        'is_enabled_hsn_code',
        'show_item_image',
        'with_tax',
    ];

    public $casts = [
        'company_id' => 'integer',
        'is_additional_item_description' => 'boolean',
        'is_additional_ledger_description' => 'boolean',
        'is_enable_narration' => 'boolean',
        'is_enabled_broker_details' => 'boolean',
        'is_enabled_credit_period_details' => 'boolean',
        'is_enabled_po_details_of_buyer' => 'boolean',
        'is_enabled_shipping_address' => 'boolean',
        'is_enabled_transport_details' => 'boolean',
        'consolidating_items_to_invoice' => 'boolean',
        'is_change_gst_details' => 'boolean',
        'warn_on_negative_stock' => 'boolean',
        'is_enabled_tcs_details' => 'boolean',
        'is_enabled_mrp' => 'boolean',
        'is_enabled_tds_details' => 'boolean',
        'is_enabled_payment_details' => 'boolean',
        'is_enabled_discount_2' => 'boolean',
        'is_enabled_hsn_code' => 'boolean',
        'show_item_image' => 'boolean',
        'with_tax' => 'boolean',
    ];
}
