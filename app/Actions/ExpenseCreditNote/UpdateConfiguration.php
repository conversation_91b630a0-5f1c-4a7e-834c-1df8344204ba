<?php

namespace App\Actions\ExpenseCreditNote;

use App\Models\Configuration\ExpenseCreditNote;
use App\Models\Master\ExpenseCreditNoteTransactionMaster;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateConfiguration
{
    use AsAction;

    public function handle($input)
    {
        $expenseCNConfigurationData = [
            'is_change_gst_details' => $input['is_change_gst_details'] ?? false,
            'is_enabled_shipping_address' => $input['is_enabled_shipping_address'] ?? false,
            'is_enabled_broker_details' => $input['is_enabled_broker_details'] ?? false,
            'is_enabled_transport_details' => $input['is_enabled_transport_details'] ?? false,
            'is_additional_ledger_description' => $input['is_additional_ledger_description'] ?? false,
            'consolidating_items_to_invoice' => $input['consolidating_items_to_invoice'] ?? false,
            'is_additional_item_description' => $input['is_additional_item_description'] ?? false,
            'is_enable_narration' => $input['is_enable_narration'] ?? false,
            'is_enabled_tcs_details' => $input['is_enabled_tcs_details'] ?? false,
            'is_enabled_tds_details' => $input['is_enabled_tds_details'] ?? false,
            'is_enabled_mrp' => $input['is_enabled_mrp'] ?? false,
            'is_enabled_payment_details' => $input['is_enabled_payment_details'] ?? false,
            'is_enabled_discount_2' => $input['is_enabled_discount_2'] ?? false,
            'is_enabled_hsn_code' => $input['is_enabled_hsn_code'] ?? false,
        ];

        $expenseCNTransactionMasterData = [
            'round_off_method' => $input['round_off_method'] ?? null,
            'voucher_number' => $input['method_of_voucher_number'] ?? null,
            'is_change_every_financial_year' => $input['is_change_every_financial_year'] ?? false,
            'default_title_of_print' => $input['title_of_print'] ?? null,
            'on_pay_ledger_id' => $input['on_pay_ledger_id'] ?? null,
        ];

        $expenseCNConfigurationId = ExpenseCreditNote::first()?->id;
        $expenseCNTransactionMasterId = ExpenseCreditNoteTransactionMaster::first()?->id;

        ExpenseCreditNote::updateOrCreate(
            ['id' => $expenseCNConfigurationId],
            $expenseCNConfigurationData
        );

        if (isset($input['show_item_image'])) {
            ExpenseCreditNote::updateOrCreate(
                ['id' => $expenseCNConfigurationId],
                ['show_item_image' => $input['show_item_image']]
            );
        }

        if (isset($input['with_tax'])) {
            ExpenseCreditNote::updateOrCreate(
                ['id' => $expenseCNConfigurationId],
                ['with_tax' => $input['with_tax']]
            );
        }

        ExpenseCreditNoteTransactionMaster::updateOrCreate(
            ['id' => $expenseCNTransactionMasterId],
            $expenseCNTransactionMasterData
        );

        return true;
    }
}
