<?php

namespace App\Actions\PurchaseOrderTransaction;

use App\Actions\CustomFields\GetCustomFieldsAction;
use App\Models\Configuration\PurchaseOrderConfiguration;
use App\Models\Master\PurchaseOrderTransactionMaster;
use App\Models\TransactionCustomField;
use Lorisle<PERSON>\Actions\Concerns\AsAction;

class GetConfiguration
{
    use AsAction;

    public function handle()
    {
        $response = [];

        $purchaseOrderTransactionMaster = PurchaseOrderTransactionMaster::first();
        $purchaseOrderConfiguration = PurchaseOrderConfiguration::first();
        // TO_COMMENT =>
        $response['companyId'] = getCurrentCompany()->id ?? null;
        $response['company_id'] = getCurrentCompany()->id ?? null;
        $response['is_gst_applicable'] = getCurrentCompany()->is_gst_applicable ?? null;

        $response['document_prefix'] = [
            'method_of_voucher_number' => $purchaseOrderTransactionMaster->method_of_voucher_number ?? PurchaseOrderTransactionMaster::AUTOMATIC,
            'start_with_number' => $purchaseOrderTransactionMaster->start_with_number ?? 1,
            'change_every_financial_year' => $purchaseOrderTransactionMaster->is_change_every_financial_year ?? 0,
            'prefix_method' => $purchaseOrderTransactionMaster->prefix_method ?? PurchaseOrderTransactionMaster::PREFIX_TEXT,
            'prefix' => $purchaseOrderTransactionMaster->prefix ?? null,
            'suffix_method' => $purchaseOrderTransactionMaster->suffix_method ?? PurchaseOrderTransactionMaster::PREFIX_TEXT,
            'suffix' => $purchaseOrderTransactionMaster->suffix ?? null,
            'title_of_print' => $purchaseOrderTransactionMaster->default_title ?? null,
        ];

        $response['header'] = [
            'is_change_gst_details' => $purchaseOrderConfiguration->is_change_gst_details,
            'is_enabled_shipping_address' => $purchaseOrderConfiguration->is_enabled_shipping_address,
            'is_enabled_broker_details' => $purchaseOrderConfiguration->is_enabled_broker_details,
            'is_enabled_transport_details' => $purchaseOrderConfiguration->is_enabled_transport_details,
            'custom_fields' => GetCustomFieldsAction::run(TransactionCustomField::PURCHASE_ORDER),
        ];

        $response['item_table_configuration'] = [
            'is_additional_ledger_description' => $purchaseOrderConfiguration->is_additional_ledger_description,
            'consolidating_items_to_invoice' => $purchaseOrderConfiguration->is_consolidating_items_to_invoice,
            'is_additional_item_description' => $purchaseOrderConfiguration->is_additional_item_description,
            'is_enabled_discount_2' => $purchaseOrderConfiguration->is_enabled_discount_2,
            'is_enabled_hsn_code' => $purchaseOrderConfiguration->is_enabled_hsn_code,
            'show_item_image' => $purchaseOrderConfiguration->show_item_image,
            'with_tax' => $purchaseOrderConfiguration->with_tax,
        ];

        $response['footer'] = [
            'is_enable_narration' => $purchaseOrderConfiguration->is_enable_narration,
            'round_off_method' => $purchaseOrderTransactionMaster->round_off_method ?? 3,
        ];

        return $response;
    }
}
