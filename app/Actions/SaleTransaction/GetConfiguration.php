<?php

namespace App\Actions\SaleTransaction;

use App\Actions\CustomFields\GetCustomFieldsAction;
use App\Models\Configuration\SaleConfiguration;
use App\Models\Master\IncomeTransactionMaster;
use App\Models\TransactionCustomField;
use Lorisleiva\Actions\Concerns\AsAction;

class GetConfiguration
{
    use AsAction;

    public function handle()
    {
        $response = [];

        $saleTransactionMaster = IncomeTransactionMaster::first();
        $saleConfiguration = SaleConfiguration::first();
        // TO_COMMENT =>
        $response['companyId'] = getCurrentCompany()->id ?? null;
        $response['company_id'] = getCurrentCompany()->id ?? null;
        $response['is_gst_applicable'] = getCurrentCompany()->is_gst_applicable ?? null;

        $response['document_prefix'] = [
            'method_of_voucher_number' => $saleTransactionMaster->method_of_voucher_number ?? IncomeTransactionMaster::AUTOMATIC_MANUAL_OVERRIDE,
            'start_with_number' => $saleTransactionMaster->start_with_number ?? 1,
            'change_every_financial_year' => $saleTransactionMaster->is_change_every_financial_year ?? 0,
            'prefix_method' => $saleTransactionMaster->prefix_method ?? IncomeTransactionMaster::PREFIX_TEXT,
            'prefix' => $saleTransactionMaster->prefix ?? null,
            'suffix_method' => $saleTransactionMaster->suffix_method ?? IncomeTransactionMaster::PREFIX_TEXT,
            'suffix' => $saleTransactionMaster->suffix ?? null,
            'bank_id' => $saleTransactionMaster->bank_id ?? null,
            'title_of_print' => $saleTransactionMaster->title_of_print ?? null,
            'terms_and_conditions' => $saleTransactionMaster->set_alter_declaration ?? null,
        ];

        $response['header'] = [
            'is_change_gst_details' => $saleConfiguration->is_change_gst_details,
            'is_enabled_estimate_quote' => $saleConfiguration->is_enabled_estimate_quote,
            'is_enabled_delivery_challan' => $saleConfiguration->is_enabled_delivery_challan,
            'is_enabled_dispatch_details' => $saleConfiguration->is_enabled_dispatch_details,
            'is_enabled_shipping_address' => $saleConfiguration->is_enabled_shipping_address,
            'is_enabled_broker_details' => $saleConfiguration->is_enabled_broker_details,
            'is_enabled_transport_details' => $saleConfiguration->is_enabled_transport_details,
            'is_enabled_eway_details' => $saleConfiguration->is_enabled_eway_details,
            'is_enabled_po_details_of_buyer' => $saleConfiguration->is_enabled_po_details_of_buyer,
            'is_enabled_credit_period_details' => $saleConfiguration->is_enabled_credit_period_details,
            'is_enabled_phone_number' => $saleConfiguration->is_enabled_phone_number,
            'custom_fields' => GetCustomFieldsAction::run(TransactionCustomField::SALE),
        ];

        $response['item_table_configuration'] = [
            'is_enable_free_quantity' => $saleConfiguration->is_enable_free_quantity,
            'is_additional_ledger_description' => $saleConfiguration->is_additional_ledger_description,
            'is_enabled_mrp' => $saleConfiguration->is_enabled_mrp,
            'consolidating_items_to_invoice' => $saleConfiguration->consolidating_items_to_invoice,
            'warn_on_negative_stock' => $saleConfiguration->warn_on_negative_stock,
            'is_additional_item_description' => $saleConfiguration->is_additional_item_description,
            'is_enabled_discount_2' => $saleConfiguration->is_enabled_discount_2,
            'is_enabled_hsn_code' => $saleConfiguration->is_enabled_hsn_code,
            'show_item_image' => $saleConfiguration->show_item_image,
            'with_tax' => $saleConfiguration->with_tax,
        ];

        $response['footer'] = [
            'is_enable_narration' => $saleConfiguration->is_enable_narration,
            'is_enabled_terms_and_conditions' => $saleConfiguration->is_enabled_terms_and_conditions,
            'is_enabled_tcs_details' => $saleConfiguration->is_enabled_tcs_details,
            'is_enabled_tds_details' => $saleConfiguration->is_enabled_tds_details,
            'is_enabled_payment_details' => $saleConfiguration->is_enabled_payment_details,
            'is_enabled_bank_details' => $saleConfiguration->is_enabled_bank_details,
            'round_off_method' => $saleTransactionMaster->round_off_method ?? 3,
        ];

        return $response;
    }
}
