<?php

namespace App\Http\Requests\ReactAPI;

use App\Models\ExpenseDebitNoteTransaction;
use App\Rules\CheckAccountingInvoiceLedgerRule;
use App\Rules\CheckAddLessLedgerRule;
use App\Rules\CheckBrokerRule;
use App\Rules\CheckCityRule;
use App\Rules\CheckCustomerAndSupplerRule;
use App\Rules\checkDateIsInFinancialYearRule;
use App\Rules\CheckGoodsAndServiceRule;
use App\Rules\CheckGstTaxesRule;
use App\Rules\CheckItemInvoiceLedgerRule;
use App\Rules\CheckLockDateRule;
use App\Rules\CheckQuantityDecimalMinRule;
use App\Rules\CheckReceiptPaymentJournalLedgerRule;
use App\Rules\CheckStateRule;
use App\Rules\CheckTaxesTCSRule;
use App\Rules\CheckTaxesTDSRule;
use App\Rules\CheckTransportRule;
use App\Rules\CheckUnitOfMeasurementRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

/**
 * Class ExpenseDebitNoteTransactionAPIRequest
 */
class ExpenseDebitNoteTransactionAPIRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return string[]
     */
    public function rules(): array
    {
        $maxFile = 15;
        $transactionId = $this->route('id');
        if (isset($transactionId)) {
            $transaction = ExpenseDebitNoteTransaction::findOrFail($transactionId);
            if ($transaction->media?->count() !== null) {
                $maxFile = 15 - $transaction->media->count();
            }
        }
        if (isCompanyGstApplicable()) {
            $debitNoteRulesArray = [
                // Basic Details
                'voucher_number' => 'required',
                'voucher_date' => ['required', 'date', 'date_format:d-m-Y', new checkDateIsInFinancialYearRule(), new CheckLockDateRule('expense')],
                'supplier_purchase_return_date' => 'nullable|date|date_format:d-m-Y',
                'original_inv_date' => 'nullable|date|date_format:d-m-Y',
                // 'supplier_id' => ['required', new CheckCustomerAndSupplerRule()],
                'is_create_party' => 'nullable|in:0,1',
                'supplier_id' => [
                    'required',
                    $this->get('is_create_party') ? '' : 'integer',
                    $this->get('is_create_party') ? '' : new CheckCustomerAndSupplerRule(),
                ],
                'gstin' => 'nullable|min:15|max:15',

                // Shipping Address
                'shipping_address_id' => 'nullable|integer', // Belongs to the address table for editing time only, selecting from multiple addresses of a party.

                // Billing Address
                'billing_address.address_1' => 'nullable|string|max:255',
                'billing_address.address_2' => 'nullable|string|max:255',
                'billing_address.country_id' => 'required|integer',
                'billing_address.state_id' => ['required', 'integer', new CheckStateRule()],
                'billing_address.city_id' => ['nullable', 'integer', new CheckCityRule()],

                // Shipping Address
                'shipping_address' => 'nullable|array',
                'shipping_address.address_name' => 'nullable|string',
                'shipping_address.shipping_name' => 'nullable|string',
                'shipping_address.shipping_gstin' => 'nullable|min:15|max:15|',
                'shipping_address.address_1' => 'nullable|string|max:255',
                'shipping_address.address_2' => 'nullable|string|max:255',
                'shipping_address.country_id' => 'required_with:shipping_address|integer',
                'shipping_address.state_id' => ['required_with:shipping_address', 'integer', new CheckStateRule()],
                'shipping_address.city_id' => ['nullable', 'integer', new CheckCityRule()],

                // Broker Details
                'broker_details.broker_id' => ['nullable', 'integer', new CheckBrokerRule()],
                'broker_details.brokerage_for_sale' => 'nullable',
                'broker_details.brokerage_on_value_type' => 'nullable|integer|in:1,2,3', // Assuming 1 or 2 are valid types

                // Transport Details
                'transport_details.transport_id' => ['nullable', 'integer', new CheckTransportRule()],
                'transport_details.transporter_document_number' => 'nullable|string',
                'transport_details.transporter_document_date' => 'nullable|date|date_format:d-m-Y',
                'transport_details.transporter_vehicle_number' => 'nullable|string',

                // Expense Debit Note Transaction Item Type
                'dn_item_type' => 'required|integer|in:1,2', // 2 for Item Invoice, 1 for Accounting Invoice

                // Main Classification Nature Type
                'main_classification_nature_type' => 'required|string',

                // Optional Fields
                'narration' => 'nullable|string|max:5000',
                'term_and_condition' => 'nullable|string|max:5000',

                // Additional Charges
                'additional_charges' => 'nullable|array',
                'additional_charges.*.ac_ledger_id' => ['required', 'integer', new CheckItemInvoiceLedgerRule()],
                'additional_charges.*.ac_gst_rate_id' => 'nullable|integer',
                'additional_charges.*.ac_type' => 'integer|required_with:additional_charges.*.ac_ledger_id',
                'additional_charges.*.ac_value' => 'numeric|required_with:additional_charges.*.ac_ledger_id',
                'additional_charges.*.ac_total_without_tax' => 'numeric|required_with:additional_charges.*.ac_ledger_id',

                // TCS Details
                'tcs_details' => 'nullable|array',
                'tcs_details.tcs_tax_id' => ['nullable', 'integer', new CheckTaxesTCSRule()],
                'tcs_details.tcs_rate' => 'nullable|required_with:tcs_details.tcs_tax_id|numeric',
                'tcs_details.tcs_amount' => 'nullable|required_with:tcs_details.tcs_tax_id|numeric',

                // GST and Taxation
                'is_gst_enabled' => 'required|boolean',
                'is_cgst_sgst_igst_calculated' => 'required|boolean',
                'is_gst_na' => 'required|boolean',
                'is_rcm_applicable' => 'required|boolean',
                'is_round_off_not_changed' => 'required|boolean',

                // Financial Details
                'taxable_value' => 'required|numeric',
                'gross_value' => 'required|numeric',
                'cgst' => 'required|numeric',
                'sgst' => 'required|numeric',
                'igst' => 'required|numeric',
                'cess' => 'required|numeric',
                'grand_total' => 'required|numeric',
                'rounding_amount' => 'nullable|numeric',

                // TDS Details
                'tds_details' => 'nullable|array',
                'tds_details.tds_tax_id' => ['nullable', 'integer', new CheckTaxesTDSRule()],
                'tds_details.tds_rate' => 'nullable|required_with:tds_details.tds_tax_id|numeric',
                'tds_details.tds_amount' => 'nullable|required_with:tds_details.tds_tax_id|numeric',

                // Add/Less
                'add_less' => 'nullable|array',
                'add_less.*.al_ledger_id' => ['required', 'integer', new CheckAddLessLedgerRule()],
                'add_less.*.al_is_show_in_print' => 'required|boolean',
                'add_less.*.al_type' => 'required_with:add_less.*.al_ledger_id|integer',
                'add_less.*.al_value' => 'required_with:add_less.*.al_ledger_id|numeric',
                'add_less.*.al_total' => 'required_with:add_less.*.al_ledger_id|numeric',

                // Payment Details
                'payment_details' => 'nullable|array',
                'payment_details.*.pd_ledger_id' => ['required', 'integer', new CheckReceiptPaymentJournalLedgerRule()],
                'payment_details.*.pd_date' => 'required_with:payment_details.*.pd_ledger_id|date|date_format:d-m-Y',
                'payment_details.*.pd_amount' => 'required_with:payment_details.*.pd_ledger_id|numeric',
                'payment_details.*.pd_mode' => 'nullable|integer',
                'payment_details.*.pd_reference_number' => 'nullable|string',

                'custom_fields' => 'nullable|array',
                'custom_fields.*.custom_field_id' => ['required', 'integer', 'exists:transaction_custom_field,id'],
                'custom_fields.*.value' => 'required_with:custom_fields.*.custom_field_id',
            ];

            // For Item Invoice (items)
            if ($this->get('dn_item_type') == ExpenseDebitNoteTransaction::ITEM_INVOICE) {
                $debitNotItemsRules = [
                    'items' => 'required|array|min:1',
                    'items.*.item_id' => ['required', 'integer', new CheckGoodsAndServiceRule()],
                    'items.*.additional_description' => 'nullable|string|max:5000',
                    'items.*.rpu' => 'required|numeric',
                    'items.*.mrp' => 'nullable|numeric',
                    // 'items.*.quantity' => 'required|numeric|min:1',
                    'items.*.ledger_id' => ['required', new CheckItemInvoiceLedgerRule()],
                    'items.*.discount_type' => 'required|integer|in:1,2', // Assuming 1 for fixed, 2 for percentage
                    'items.*.discount_value' => 'nullable|numeric',
                    'items.*.discount_type_2' => 'nullable|integer|in:1,2', // Assuming 1 or 2 are valid types
                    'items.*.discount_value_2' => 'nullable|numeric',
                    'items.*.gst_id' => ['required', 'integer', new CheckGstTaxesRule()],
                    'items.*.total' => 'required|numeric',
                    'items.*.cess' => 'nullable|numeric',
                    'items.*.custom_fields' => 'nullable|array',
                    'items.*.custom_fields.*.custom_field_id' => ['required', 'integer', 'exists:item_custom_field,id'],
                    'items.*.custom_fields.*.value' => 'required_with:items.*.custom_fields.*.custom_field_id',

                    // custom field inventory validation
                    'items.*.custom_field_inventory' => 'nullable|array',
                    'items.*.custom_field_inventory.*' => 'array',
                    'items.*.custom_field_inventory.*.*.custom_field_id' => ['required', 'integer', 'exists:item_custom_field,id'],
                    'items.*.custom_field_inventory.*.*.quantity' => 'required|numeric|min:1',
                    'items.*.custom_field_inventory.*.*.value' => 'required|string',
                    'items.*.custom_field_inventory.*.*.purchase_rate' => 'nullable|numeric',
                    'items.*.custom_field_inventory.*.*.purchase_date' => 'nullable|date|date_format:d-m-Y',
                ];

                foreach ($this->input('items', []) as $key => $item) {
                    $debitNotItemsRules["items.$key.unit_id"] = ['required', 'integer', new CheckUnitOfMeasurementRule($item['item_id'])];
                }
                foreach ($this->input('items', []) as $key => $item) {
                    $debitNotItemsRules["items.$key.quantity"] = ['required', 'numeric', new CheckQuantityDecimalMinRule($item['item_id'])];
                }

                $debitNoteRulesArray = array_merge($debitNoteRulesArray, $debitNotItemsRules);
            }

            // For Accounting Invoice (ledgers)
            if ($this->get('dn_item_type') == ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE) {
                $debitNotLedgersRules = [
                    'ledgers' => 'required|array|min:1',
                    'ledgers.*.ledger_id' => ['required', 'integer', new CheckAccountingInvoiceLedgerRule()],
                    'ledgers.*.additional_description' => 'nullable|string|max:5000',
                    'ledgers.*.rpu' => 'required|numeric',
                    'ledgers.*.with_tax' => 'required|boolean',
                    'ledgers.*.discount_type' => 'required|integer|in:1,2', // Assuming 1 or 2 are valid types
                    'ledgers.*.discount_value' => 'nullable|numeric',
                    'ledgers.*.discount_type_2' => 'nullable|integer|in:1,2', // Assuming 1 or 2 are valid types
                    'ledgers.*.discount_value_2' => 'nullable|numeric',
                    'ledgers.*.gst_id' => ['required', 'integer', new CheckGstTaxesRule()],
                    'ledgers.*.total' => 'required|numeric',
                ];

                $debitNoteRulesArray = array_merge($debitNoteRulesArray, $debitNotLedgersRules);
            }
        } else {
            $debitNoteRulesArray = [
                // Basic Details
                'voucher_number' => 'required',
                'voucher_date' => ['required', 'date', 'date_format:d-m-Y', new checkDateIsInFinancialYearRule(), new CheckLockDateRule('expense')],
                'supplier_purchase_return_date' => 'nullable|date|date_format:d-m-Y',
                'original_inv_date' => 'nullable|date|date_format:d-m-Y',
                // 'supplier_id' => ['required', new CheckCustomerAndSupplerRule()],
                'is_create_party' => 'nullable|in:0,1',
                'supplier_id' => [
                    'required',
                    $this->get('is_create_party') ? '' : 'integer',
                    $this->get('is_create_party') ? '' : new CheckCustomerAndSupplerRule(),
                ],

                // Shipping Address
                'shipping_address_id' => 'nullable|integer', // Belongs to the address table for editing time only, selecting from multiple addresses of a party.

                // Billing Address
                'billing_address.address_1' => 'nullable|string|max:255',
                'billing_address.address_2' => 'nullable|string|max:255',
                'billing_address.country_id' => 'nullable|integer',
                'billing_address.state_id' => ['nullable', 'integer', new CheckStateRule()],
                'billing_address.city_id' => ['nullable', 'integer', new CheckCityRule()],

                // Shipping Address
                'shipping_address' => 'nullable|array',
                'shipping_address.address_name' => 'nullable|string',
                'shipping_address.address_1' => 'nullable|string|max:255',
                'shipping_address.address_2' => 'nullable|string|max:255',
                'shipping_address.shipping_name' => 'nullable|string',
                'shipping_address.country_id' => 'nullable|integer',
                'shipping_address.state_id' => ['nullable', 'integer', new CheckStateRule()],
                'shipping_address.city_id' => ['nullable', 'integer', new CheckCityRule()],

                // Broker Details
                'broker_details.broker_id' => ['nullable', 'integer', new CheckBrokerRule()],
                'broker_details.brokerage_for_sale' => 'nullable',
                'broker_details.brokerage_on_value_type' => 'nullable|integer|in:1,2,3', // Assuming 1 or 2 are valid types

                // Transport Details
                'transport_details.transport_id' => ['nullable', 'integer', new CheckTransportRule()],
                'transport_details.transporter_document_number' => 'nullable|string',
                'transport_details.transporter_document_date' => 'nullable|date|date_format:d-m-Y',
                'transport_details.transporter_vehicle_number' => 'nullable|string',

                // Expense Debit Note Transaction Item Type
                'dn_item_type' => 'required|integer|in:1,2', // 2 for Item Invoice, 1 for Accounting Invoice

                // Optional Fields
                'narration' => 'nullable|string|max:5000',
                'term_and_condition' => 'nullable|string|max:5000',

                // Additional Charges
                'additional_charges' => 'nullable|array',
                'additional_charges.*.ac_ledger_id' => ['required', 'integer', new CheckItemInvoiceLedgerRule()],
                'additional_charges.*.ac_type' => 'integer|required_with:additional_charges.*.ac_ledger_id',
                'additional_charges.*.ac_value' => 'numeric|required_with:additional_charges.*.ac_ledger_id',
                'additional_charges.*.ac_total_without_tax' => 'numeric|required_with:additional_charges.*.ac_ledger_id',

                // TCS Details
                'tcs_details' => 'nullable|array',
                'tcs_details.tcs_tax_id' => ['nullable', 'integer', new CheckTaxesTCSRule()],
                'tcs_details.tcs_rate' => 'nullable|required_with:tcs_details.tcs_tax_id|numeric',
                'tcs_details.tcs_amount' => 'nullable|required_with:tcs_details.tcs_tax_id|numeric',

                // GST and Taxation
                'is_gst_enabled' => 'required|boolean',
                'is_cgst_sgst_igst_calculated' => 'required|boolean',
                'is_gst_na' => 'required|boolean',
                'is_round_off_not_changed' => 'required|boolean',

                // Financial Details
                'taxable_value' => 'required|numeric',
                'gross_value' => 'required|numeric',
                'grand_total' => 'required|numeric',
                'rounding_amount' => 'nullable|numeric',

                // TDS Details
                'tds_details' => 'nullable|array',
                'tds_details.tds_tax_id' => ['nullable', 'integer', new CheckTaxesTDSRule()],
                'tds_details.tds_rate' => 'nullable|required_with:tds_details.tds_tax_id|numeric',
                'tds_details.tds_amount' => 'nullable|required_with:tds_details.tds_tax_id|numeric',

                // Add/Less
                'add_less' => 'nullable|array',
                'add_less.*.al_ledger_id' => ['required', 'integer', new CheckAddLessLedgerRule()],
                'add_less.*.al_is_show_in_print' => 'required|boolean',
                'add_less.*.al_type' => 'required_with:add_less.*.al_ledger_id|integer',
                'add_less.*.al_value' => 'required_with:add_less.*.al_ledger_id|numeric',
                'add_less.*.al_total' => 'required_with:add_less.*.al_ledger_id|numeric',

                // Payment Details
                'payment_details' => 'nullable|array',
                'payment_details.*.pd_ledger_id' => ['required', 'integer', new CheckReceiptPaymentJournalLedgerRule()],
                'payment_details.*.pd_date' => 'required_with:payment_details.*.pd_ledger_id|date|date_format:d-m-Y',
                'payment_details.*.pd_amount' => 'required_with:payment_details.*.pd_ledger_id|numeric',
                'payment_details.*.pd_mode' => 'nullable|integer',
                'payment_details.*.pd_reference_number' => 'nullable|string',

                'custom_fields' => 'nullable|array',
                'custom_fields.*.custom_field_id' => ['required', 'integer', 'exists:transaction_custom_field,id'],
                'custom_fields.*.value' => 'required_with:custom_fields.*.custom_field_id',
            ];

            // For Item Invoice (items)
            if ($this->get('dn_item_type') == ExpenseDebitNoteTransaction::ITEM_INVOICE) {
                $debitNotItemsRules = [
                    'items' => 'required|array|min:1',
                    'items.*.item_id' => ['required', 'integer', new CheckGoodsAndServiceRule()],
                    'items.*.additional_description' => 'nullable|string|max:5000',
                    'items.*.rpu' => 'required|numeric',
                    'items.*.mrp' => 'nullable|numeric',
                    // 'items.*.quantity' => 'required|numeric|min:1',
                    'items.*.discount_type' => 'required|integer|in:1,2', // Assuming 1 for fixed, 2 for percentage
                    'items.*.discount_value' => 'nullable|numeric',
                    'items.*.discount_type_2' => 'nullable|integer|in:1,2', // Assuming 1 or 2 are valid types
                    'items.*.discount_value_2' => 'nullable|numeric',
                    'items.*.total' => 'required|numeric',
                    'items.*.ledger_id' => ['required', new CheckItemInvoiceLedgerRule()],
                    'items.*.custom_fields' => 'nullable|array',
                    'items.*.custom_fields.*.custom_field_id' => ['required', 'integer', 'exists:item_custom_field,id'],
                    'items.*.custom_fields.*.value' => 'required_with:items.*.custom_fields.*.custom_field_id',

                    // custom field inventory validation
                    'items.*.custom_field_inventory' => 'nullable|array',
                    'items.*.custom_field_inventory.*' => 'array',
                    'items.*.custom_field_inventory.*.*.custom_field_id' => ['required', 'integer', 'exists:item_custom_field,id'],
                    'items.*.custom_field_inventory.*.*.quantity' => 'required|numeric|min:1',
                    'items.*.custom_field_inventory.*.*.value' => 'required|string',
                    'items.*.custom_field_inventory.*.*.purchase_rate' => 'nullable|numeric',
                    'items.*.custom_field_inventory.*.*.purchase_date' => 'nullable|date|date_format:d-m-Y',
                ];

                foreach ($this->input('items', []) as $key => $item) {
                    $debitNotItemsRules["items.$key.unit_id"] = ['required', 'integer', new CheckUnitOfMeasurementRule($item['item_id'])];
                }
                foreach ($this->input('items', []) as $key => $item) {
                    $debitNotItemsRules["items.$key.quantity"] = ['required', 'numeric', new CheckQuantityDecimalMinRule($item['item_id'])];
                }

                $debitNoteRulesArray = array_merge($debitNoteRulesArray, $debitNotItemsRules);
            }

            // For Accounting Invoice (ledgers)
            if ($this->get('dn_item_type') == ExpenseDebitNoteTransaction::ACCOUNTING_INVOICE) {
                $debitNotLedgersRules = [
                    'ledgers' => 'required|array|min:1',
                    'ledgers.*.ledger_id' => ['required', 'integer', new CheckAccountingInvoiceLedgerRule()],
                    'ledgers.*.additional_description' => 'nullable|string|max:5000',
                    'ledgers.*.rpu' => 'required|numeric',
                    'ledgers.*.discount_type' => 'required|integer|in:1,2', // Assuming 1 or 2 are valid types
                    'ledgers.*.discount_value' => 'nullable|numeric',
                    'ledgers.*.discount_type_2' => 'nullable|integer|in:1,2', // Assuming 1 or 2 are valid types
                    'ledgers.*.discount_value_2' => 'nullable|numeric',
                    'ledgers.*.total' => 'required|numeric',
                ];

                $debitNoteRulesArray = array_merge($debitNoteRulesArray, $debitNotLedgersRules);
            }
        }

        if ($this->get('is_gst_na') && isset($debitNoteRulesArray['main_classification_nature_type'])) {
            unset($debitNoteRulesArray['main_classification_nature_type']);
        }

        //For Document Upload
        $debitNoteRulesArray['expense_debit_note_document.*'] = 'nullable|mimes:jpg,jpeg,png,pdf,xlsx,docx|max:2048';
        $debitNoteRulesArray['expense_debit_note_document'] = 'nullable|max:' . $maxFile;

        return $debitNoteRulesArray;
    }

    /**
     * @return string[]
     */
    public function messages(): array
    {
        $message = [
            // Basic Details
            'voucher_number.required' => 'The voucher number is required.',
            'voucher_date.required' => 'The voucher date is required.',
            'voucher_date.date' => 'The voucher date must be a valid date.',
            'voucher_date.date_format' => 'The voucher date format should be d-m-Y.',
            'supplier_id.required' => 'The supplier ID is required.',
            'gstin.min' => 'The GSTIN must be exactly 15 characters.',
            'gstin.max' => 'The GSTIN must be exactly 15 characters.',
            'gstin.regex' => 'The GSTIN format is invalid.',

            // Billing Address
            'billing_address.country_id.required' => 'The billing country is required.',
            'billing_address.state_id.required' => 'The billing state is required.',
            'billing_address.city_id.required' => 'The billing city is required.',

            // Shipping Address
            'shipping_address.shipping_name.string' => 'The shipping name must be a valid string.',
            'shipping_address.shipping_gstin.regex' => 'The shipping GSTIN format is invalid.',
            'shipping_address.country_id.required' => 'The shipping country is required.',
            'shipping_address.state_id.required' => 'The shipping state is required.',
            'shipping_address.city_id.required' => 'The shipping city is required.',

            // Broker Details
            'broker_details.broker_id.integer' => 'The broker ID must be a valid integer.',
            'broker_details.brokerage_on_value_type.in' => 'The brokerage on value type must be either 1, 2 or 3.',

            // Transport Details
            'transport_details.transport_id.integer' => 'The transport ID must be a valid integer.',
            'transport_details.transporter_document_date.date' => 'The transporter document date must be a valid date.',
            'transport_details.transporter_document_date.date_format' => 'The transporter document date format should be d-m-Y.',

            // Expense Debit Note Transaction Item Type
            'dn_item_type.required' => 'The debit note item type is required.',
            'dn_item_type.in' => 'The debit note item type must be either 1 or 2.',

            // Main Classification Nature Type
            'main_classification_nature_type.required' => 'The main classification nature type is required.',

            //For Document Upload
            'expense_debit_note_document.*.mimes' => 'The expense debit note document must be a file of type: jpg, jpeg, png, pdf, xlsx, docx.',
            'expense_debit_note_document.*.max' => 'Expense debit note document size should be less than 2 MB.',
            'expense_debit_note_document.max' => 'The upload expense debit note document must not be greater than 15 files.',

            // TCS Details
            'tcs_details.tcs_tax_id.integer' => 'The TCS tax ID must be a valid integer.',
            'tcs_details.tcs_rate.numeric' => 'The TCS rate must be a numeric value.',
            'tcs_details.tcs_amount.numeric' => 'The TCS amount must be a numeric value.',

            // GST and Taxation
            'is_gst_enabled.required' => 'Please specify if GST is enabled.',
            'is_cgst_sgst_igst_calculated.required' => 'Please specify if CGST/SGST/IGST is calculated.',
            'is_gst_na.required' => 'Please specify if GST is not applicable.',

            // Financial Details
            'grand_total.required' => 'The grand total is required.',
            'grand_total.numeric' => 'The grand total must be a numeric value.',
            'rounding_amount.numeric' => 'The rounding amount must be a numeric value.',

            // Items (for Item Invoice)
            'items.required' => 'At least one item is required.',
            'items.*.item_id.required' => 'The item ID is required for all items.',
            'items.*.rpu.required' => 'The RPU is required for all items.',
            'items.*.rpu.numeric' => 'The RPU must be a numeric value for all items.',
            'items.*.rpu.max' => 'The RPU not exceed 10 characters for all items.',
            'items.*.mrp.max' => 'The MRP not exceed 10 characters for all items.',
            'items.*.discount_value.max' => 'The Discount 1 not exceed 10 characters for all items.',
            'items.*.discount_value_2.max' => 'The Discount 2 not exceed 10 characters for all items.',
            'items.*.with_tax.required' => 'The tax applicability is required for all items.',
            'items.*.quantity.required' => 'The quantity is required for all items.',
            'items.*.quantity.numeric' => 'The quantity must be a numeric value for all items.',
            'items.*.quantity.min' => 'The quantity must be at least 1 for all items.',
            'items.*.additional_description.max' => 'The additional description must not exceed 5000 characters.',
            'items.*.custom_fields.*.custom_field_id.required' => 'The custom field item ID is required.',
            'items.*.custom_fields.*.custom_field_id.integer' => 'The custom field item ID must be a valid integer.',
            'items.*.custom_fields.*.custom_field_id.exists' => 'The selected custom field item ID is invalid.',
            'items.*.custom_fields.*.value.required_with' => 'The value is required when a custom field item ID is provided.',

            'items.*.custom_field_inventory.*.*.custom_field_id.required' => 'Please select custom field id in custom field inventory.',
            'items.*.custom_field_inventory.*.*.custom_field_id.exists' => 'Please select valid custom field id in custom field inventory.',
            'items.*.custom_field_inventory.*.*.quantity.required' => 'Please enter quantity in custom field inventory.',
            'items.*.custom_field_inventory.*.*.quantity.numeric' => 'Please enter valid quantity in custom field inventory.',
            'items.*.custom_field_inventory.*.*.quantity.min' => 'Please enter atleast 1 quantity in custom field inventory.',
            'items.*.custom_field_inventory.*.*.value.required' => 'Please enter value in custom field inventory.',
            'items.*.custom_field_inventory.*.*.purchase_rate.numeric' => 'Please enter valid purchase rate in custom field inventory.',
            'items.*.custom_field_inventory.*.*.purchase_date.date' => 'Please enter valid purchase date in custom field inventory.',

            // Ledgers (for Accounting Invoice)
            'ledgers.required' => 'At least one ledger entry is required.',
            'ledgers.*.ledger_id.required' => 'The ledger ID is required for all ledger entries.',
            'ledgers.*.rpu.required' => 'The RPU is required for all ledger entries.',
            'ledgers.*.rpu.numeric' => 'The RPU must be a numeric value for all ledger entries.',
            'ledgers.*.rpu.max' => 'The RPU not exceed 10 characters for all ledger entries.',
            'ledgers.*.discount_value.max' => 'The Discount 1 not exceed 10 characters for all ledger entries.',
            'ledgers.*.discount_value_2.max' => 'The Discount 2 not exceed 10 characters for all ledger entries.',
            'ledgers.*.additional_description.max' => 'The additional description must not exceed 5000 characters.',
            'ledgers.*.with_tax.required' => 'The tax applicability is required for all ledger entries.',

            // Custom Rule Messages (if any rules require specific messaging)
            'voucher_date.check_date_is_in_financial_year' => 'The voucher date must be within the current financial year.',
            'supplier_id.check_customer_and_supplier' => 'The supplier ID is invalid.',

            // Additional Charges
            'additional_charges.*.ac_ledger_id.required' => 'The additional charge ledger ID is required.',
            'additional_charges.*.ac_ledger_id.integer' => 'The additional charge ledger ID must be an integer.',
            'additional_charges.*.ac_type.required_with' => 'The additional charge type is required.',
            'additional_charges.*.ac_type.integer' => 'The additional charge type must be an integer.',
            'additional_charges.*.ac_value.required_with' => 'The additional charge value is required.',
            'additional_charges.*.ac_value.numeric' => 'The additional charge value must be a number.',
            'additional_charges.*.ac_gst_rate_id.required' => 'The additional charge GST rate ID is required.',
            'additional_charges.*.ac_gst_rate_id.integer' => 'The additional charge GST rate ID must be an integer.',
            'additional_charges.*.ac_total_without_tax.required_with' => 'The additional charge total is required.',
            'additional_charges.*.ac_total_without_tax.numeric' => 'The additional charge total must be a number.',

            // Add/Less
            'add_less.*.al_ledger_id.required' => 'The add/less ledger ID is required.',
            'add_less.*.al_ledger_id.integer' => 'The add/less ledger ID must be an integer.',
            'add_less.*.al_is_show_in_print.required' => 'The add/less show in print field is required.',
            'add_less.*.al_is_show_in_print.boolean' => 'The add/less show in print field must be true or false.',
            'add_less.*.al_type.required_with' => 'The add/less type is required.',
            'add_less.*.al_type.integer' => 'The add/less type must be an integer.',
            'add_less.*.al_value.required_with' => 'The add/less value is required.',
            'add_less.*.al_value.numeric' => 'The add/less value must be a number.',
            'add_less.*.al_total.required_with' => 'The add/less total is required.',
            'add_less.*.al_total.numeric' => 'The add/less total must be a number.',

            // Payment Details
            'payment_details.*.pd_ledger_id.required' => 'The payment ledger ID is required.',
            'payment_details.*.pd_ledger_id.integer' => 'The payment ledger ID must be an integer.',
            'payment_details.*.pd_date.required_with' => 'The payment date is required.',
            'payment_details.*.pd_date.date' => 'The payment date must be a valid date.',
            'payment_details.*.pd_date.date_format' => 'The payment date format must be d-m-Y.',
            'payment_details.*.pd_amount.required_with' => 'The payment amount is required.',
            'payment_details.*.pd_amount.numeric' => 'The payment amount must be a number.',
            'payment_details.*.pd_mode.required' => 'The payment mode is required.',
            'payment_details.*.pd_mode.integer' => 'The payment mode must be an integer.',

            'custom_fields.*.custom_field_id.required' => 'The custom field is required.',
            'custom_fields.*.custom_field_id.integer' => 'The custom field must be an integer.',
            'custom_fields.*.custom_field_id.exists' => 'The custom field does not exist.',
            'custom_fields.*.value.required_with' => 'The custom field value is required.',
        ];

        return $message;
    }
}
