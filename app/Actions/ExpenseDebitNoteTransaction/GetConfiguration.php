<?php

namespace App\Actions\ExpenseDebitNoteTransaction;

use App\Actions\CustomFields\GetCustomFieldsAction;
use App\Models\Configuration\ExpenseDebitNote;
use App\Models\Master\ExpenseDebitNoteTransactionMaster;
use App\Models\TransactionCustomField;
use Lorisleiva\Actions\Concerns\AsAction;

class GetConfiguration
{
    use AsAction;

    public function handle()
    {
        $response = [];

        $expenseTransactionMaster = ExpenseDebitNoteTransactionMaster::first();
        $expenseDebitNoteConfiguration = ExpenseDebitNote::first();
        // TO_COMMENT =>
        $response['companyId'] = getCurrentCompany()->id ?? null;
        $response['company_id'] = getCurrentCompany()->id ?? null;
        $response['is_gst_applicable'] = getCurrentCompany()->is_gst_applicable ?? null;

        $response['document_prefix'] = [
            'method_of_voucher_number' => $expenseTransactionMaster->voucher_number ?? ExpenseDebitNoteTransactionMaster::AUTOMATIC_MANUAL_OVERRIDE,
            'change_every_financial_year' => $expenseTransactionMaster->is_change_every_financial_year ?? 0,
            'title_of_print' => $expenseTransactionMaster->default_title_of_print ?? null,
        ];

        $response['header'] = [
            'is_change_gst_details' => $expenseDebitNoteConfiguration->is_change_gst_details,
            'is_enabled_shipping_address' => $expenseDebitNoteConfiguration->is_enabled_shipping_address,
            'is_enabled_broker_details' => $expenseDebitNoteConfiguration->is_enabled_broker_details,
            'is_enabled_transport_details' => $expenseDebitNoteConfiguration->is_enabled_transport_details,
            'custom_fields' => GetCustomFieldsAction::run(TransactionCustomField::EXPENSE_DEBIT_NOTE),
        ];

        $response['item_table_configuration'] = [
            'is_additional_ledger_description' => $expenseDebitNoteConfiguration->is_additional_ledger_description,
            'is_enabled_mrp' => $expenseDebitNoteConfiguration->is_enabled_mrp,
            'consolidating_items_to_invoice' => $expenseDebitNoteConfiguration->consolidating_items_to_invoice,
            'is_additional_item_description' => $expenseDebitNoteConfiguration->is_additional_item_description,
            'is_enabled_discount_2' => $expenseDebitNoteConfiguration->is_enabled_discount_2,
            'is_enabled_hsn_code' => $expenseDebitNoteConfiguration->is_enabled_hsn_code,
            'show_item_image' => $expenseDebitNoteConfiguration->show_item_image,
            'with_tax' => $expenseDebitNoteConfiguration->with_tax,
        ];

        $response['footer'] = [
            'is_enable_narration' => $expenseDebitNoteConfiguration->is_enable_narration,
            'is_enabled_tcs_details' => $expenseDebitNoteConfiguration->is_enabled_tcs_details,
            'is_enabled_tds_details' => $expenseDebitNoteConfiguration->is_enabled_tds_details,
            'is_enabled_payment_details' => $expenseDebitNoteConfiguration->is_enabled_payment_details,
            'round_off_method' => $expenseTransactionMaster->round_off_method ?? 3,
        ];

        return $response;
    }
}
