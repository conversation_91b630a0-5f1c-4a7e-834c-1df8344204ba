import { useState, useMemo, useEffect } from 'react'
import { <PERSON><PERSON>, Modal } from 'react-bootstrap';
import Close from '../../assets/images/svg/close';
import { FormInput } from '../../components/ui/Input';
import Datepicker from '../../components/ui/DatePicker';
import { formattedDate } from '../../shared/calculation';
import { toastType } from '../../constants';
import { useDispatch } from 'react-redux';
import { errorToast } from '../../store/actions/toastAction';

const rowsPerPage = 20;

const ItemCustomFieldOpeningStock = (props) => {
    const {
        show,
        handleCloseModel,
        customFieldDetail,
        formik
    } = props;
    const dispatch = useDispatch();
    const [customFieldLength, setCustomFieldLength] = useState(20);
    const [tempFieldValues, setTempFieldValues] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);

    // Filter customFieldDetail to only show fields with custom_field_type: 2 and both status and local_status are true
    const filteredCustomFieldDetail = useMemo(() => {
        return customFieldDetail?.filter(field =>
            field.custom_field_type === 2 &&
            field.status === true &&
            field.local_status === true
        ) || [];
    }, [customFieldDetail]);

    // Check if all type 2 custom fields are disabled
    const allType2FieldsDisabled = useMemo(() => {
        const type2Fields = customFieldDetail?.filter(field => field.custom_field_type === 2) || [];
        return type2Fields.length > 0 && type2Fields.every(field => !field.status || !field.local_status);
    }, [customFieldDetail]);

    // Function to populate tempFieldValues from existing Formik custom_field_inventory
    const populateFromFormik = () => {
        const existingInventory = formik.values.custom_field_inventory || [];
        if (existingInventory.length === 0) return;

        const populatedValues = [];

        existingInventory.forEach((rowArray, rowIndex) => {
            const rowData = [];

            // Initialize row with empty objects for all columns
            const totalColumns = filteredCustomFieldDetail.length + 3; // +3 for quantity, purchase_date and purchase_rate
            for (let i = 0; i < totalColumns; i++) {
                rowData.push({});
            }

            // Fill custom field values
            rowArray.forEach(item => {
                const fieldIndex = filteredCustomFieldDetail.findIndex(field => field.id === item.custom_field_id);
                if (fieldIndex !== -1) {
                    rowData[fieldIndex] = {
                        custom_field_id: item.custom_field_id,
                        value: item.value
                    };

                    // Set quantity, purchase_date and purchase_rate for this row
                    rowData[filteredCustomFieldDetail.length] = {
                        quantity: item.quantity || 1
                    };
                    rowData[filteredCustomFieldDetail.length + 1] = {
                        purchase_date: item.purchase_date
                    };
                    rowData[filteredCustomFieldDetail.length + 2] = {
                        purchase_rate: item.purchase_rate
                    };
                }
            });

            populatedValues[rowIndex] = rowData;
        });

        setTempFieldValues(populatedValues);

        // Update customFieldLength to show existing rows + minimal extra rows
        const minLength = Math.max(existingInventory.length, 20);
        setCustomFieldLength(minLength);
    };

    // Function to calculate total quantity and update Formik
    const calculateTotalQuantity = (tempValues = tempFieldValues) => {
        let totalQuantity = 0;

        tempValues.forEach(row => {
            if (row && row.length > 0) {
                // Check if row has any custom field values
                const hasCustomFieldValues = filteredCustomFieldDetail.some((_, colIndex) => {
                    return row[colIndex]?.value && row[colIndex].value.toString().trim();
                });

                if (hasCustomFieldValues) {
                    const quantityColIndex = filteredCustomFieldDetail.length;
                    const quantity = row[quantityColIndex]?.quantity || 0;
                    totalQuantity += parseFloat(quantity) || 0;
                }
            }
        });

        // Update Formik quantity value
        formik.setFieldValue('quantity', totalQuantity);
    };

    // Populate values when modal opens or filteredCustomFieldDetail changes
    useEffect(() => {
        if (show && filteredCustomFieldDetail.length > 0) {
            populateFromFormik();
        }
    }, [show, filteredCustomFieldDetail, formik.values.custom_field_inventory]);

    // Calculate total quantity when tempFieldValues changes
    useEffect(() => {
        calculateTotalQuantity();
    }, [tempFieldValues]);

    const handleChange = (e, rowIndex, fieldId, colIndex, type = 'value') => {
        const value = type === 'purchase_date' ? e : e.target.value; // Handle datepicker differently
        const updatedTemp = [...tempFieldValues];

        while (updatedTemp.length <= rowIndex) updatedTemp.push([]);
        while ((updatedTemp[rowIndex] || []).length <= colIndex)
            updatedTemp[rowIndex].push({});

        const existingCell = updatedTemp[rowIndex][colIndex] || {};
        if (type === 'value') {
            // Prevent adding spaces when value is blank/empty
            // If current value is empty/blank and user tries to add space, ignore it
            const currentValue = existingCell?.value || '';
            const trimmedCurrentValue = currentValue.toString().trim();
            const trimmedNewValue = value.toString().trim();

            // If current value is empty and new value is only spaces, don't update
            if (trimmedCurrentValue === '' && value !== trimmedNewValue) {
                return; // Don't update if trying to add spaces to empty field
            }

            updatedTemp[rowIndex][colIndex] = {
                ...existingCell,
                custom_field_id: fieldId,
                value,
            };

            const quantityColIndex = filteredCustomFieldDetail.length;
            while (updatedTemp[rowIndex].length <= quantityColIndex) {
                updatedTemp[rowIndex].push({});
            }

            // Auto-enable quantity field and set default value when custom field is filled
            if (value && value.toString().trim()) {
                if (!updatedTemp[rowIndex][quantityColIndex]?.quantity) {
                    updatedTemp[rowIndex][quantityColIndex] = {
                        ...updatedTemp[rowIndex][quantityColIndex],
                        quantity: 1
                    };
                }
            } else {
                // Check if all custom fields in this row are empty
                const hasAnyCustomFieldValue = filteredCustomFieldDetail.some((_, idx) => {
                    if (idx === colIndex) return false; // Skip current field being cleared
                    return updatedTemp[rowIndex][idx]?.value && updatedTemp[rowIndex][idx].value.toString().trim();
                });

                // If no custom field values exist, clear the quantity
                if (!hasAnyCustomFieldValue) {
                    updatedTemp[rowIndex][quantityColIndex] = {
                        ...updatedTemp[rowIndex][quantityColIndex],
                        quantity: ""
                    };
                }
            }
        } else if (type === 'purchase_date') {
            // Format date to dd-mm-yyyy format
            const isValidDate = value && !isNaN(new Date(value).getTime());
            const formattedDateValue = isValidDate ? formattedDate(new Date(value)) : "";
            updatedTemp[rowIndex][colIndex] = {
                ...existingCell,
                purchase_date: formattedDateValue,
            };
        } else if (type === 'purchase_rate') {
            updatedTemp[rowIndex][colIndex] = {
                ...existingCell,
                purchase_rate: value,
            };
        } else if (type === 'quantity') {
            updatedTemp[rowIndex][colIndex] = {
                ...existingCell,
                quantity: parseFloat(value) || 0,
            };
        }

        setTempFieldValues(updatedTemp);

        // Calculate and update total quantity in Formik
        calculateTotalQuantity(updatedTemp);
    };

    // Calculate paginated rows
    const paginatedRows = useMemo(() => {
        return Array.from({ length: customFieldLength })
            .slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage)
            .map((_, indexOffset) => (currentPage - 1) * rowsPerPage + indexOffset);
    }, [customFieldLength, currentPage]);

    const handleSubmit = (e) => {
        e.preventDefault();
        const data = tempFieldValues;
        for (let i = 0; i < data.length - 1; i++) {
            const isRowIEmpty = data[i].every(cell => !cell.value?.toString().trim());
            if (isRowIEmpty) continue;

            for (let j = i + 1; j < data.length; j++) {
                const isRowJEmpty = data[j].every(cell => !cell.value?.toString().trim());
                if (isRowJEmpty) continue;

                const isDuplicate = data[i].every((cell, colIndex) => {
                    const valA = cell.value?.toString().trim();
                    const valB = data[j][colIndex]?.value?.toString().trim();
                    return valA === valB;
                });

                if (isDuplicate) {
                    const duplicateVal = data[i][0]?.value || "N/A";
                    return dispatch(
                        errorToast({
                            text: `Duplicate row found (Row ${i + 1} and Row ${
                                j + 1
                            }) with value "${duplicateVal}"`,
                            type: toastType.ERROR,
                        })
                    );
                }
            }
        }

        // Validation: Check if custom fields have values but quantities are missing (or vice versa)
        for (let i = 0; i < data.length; i++) {
            const row = data[i];
            if (!row || row.length === 0) continue;

            // Check if row has any custom field values
            const hasCustomFieldValues = filteredCustomFieldDetail.some((_, colIndex) => {
                return row[colIndex]?.value && row[colIndex].value.toString().trim() !== "";
            });

            // Check if row has quantity
            const quantityColIndex = filteredCustomFieldDetail.length;
            const hasQuantity = row[quantityColIndex]?.quantity && parseFloat(row[quantityColIndex].quantity) > 0;

            // If custom field values exist but no quantity, show error
            if (hasCustomFieldValues && !hasQuantity) {
                return dispatch(
                    errorToast({
                        text: `Row ${i + 1}: Please add quantity for the custom field values entered.`,
                        type: toastType.ERROR,
                    })
                );
            }

            // If quantity exists but no custom field values, show error
            if (hasQuantity && !hasCustomFieldValues) {
                return dispatch(
                    errorToast({
                        text: `Row ${i + 1}: Please add custom field values for the quantity entered.`,
                        type: toastType.ERROR,
                    })
                );
            }
        }

        // Transform tempFieldValues into the required custom_field_inventory format
        const custom_field_inventory = [];

        // Process each row
        tempFieldValues.forEach((row) => {
            if (!row || row.length === 0) return;

            const rowData = [];

            // Process custom field values
            filteredCustomFieldDetail.forEach((field, colIndex) => {
                const cellData = row[colIndex];
                if (cellData && cellData.value && cellData.value.toString().trim()) {
                    // Get quantity, purchase_date and purchase_rate from the same row
                    const quantityCell = row[filteredCustomFieldDetail.length];
                    const purchaseDateCell = row[filteredCustomFieldDetail.length + 1];
                    const purchaseRateCell = row[filteredCustomFieldDetail.length + 2];

                    rowData.push({
                        custom_field_id: field.id,
                        quantity: quantityCell?.quantity ? parseFloat(quantityCell.quantity) : 1,
                        value: cellData.value.toString().trim(),
                        purchase_rate: purchaseRateCell?.purchase_rate ? parseFloat(purchaseRateCell.purchase_rate) : 0,
                        purchase_date: purchaseDateCell?.purchase_date || ""
                    });
                }
            });

            // Only add row if it has custom field data
            if (rowData.length > 0) {
                custom_field_inventory.push(rowData);
            }
        });

        // Store the custom_field_inventory in Formik
        formik.setFieldValue('custom_field_inventory', custom_field_inventory);

        // Calculate total purchase rate ÷ total qty and store in formik.values.rate
        let totalPurchaseRate = 0;
        let totalQuantity = 0;

        custom_field_inventory.forEach(row => {
            row.forEach(item => {
                const purchaseRate = parseFloat(item.purchase_rate) || 0;
                const quantity = parseFloat(item.quantity) || 0;

                totalPurchaseRate += (purchaseRate * quantity); // Weighted total
                totalQuantity += quantity;
            });
        });

        // Calculate average rate and store in formik if data exists
        if (totalQuantity > 0 && totalPurchaseRate > 0) {
            const averageRate = totalPurchaseRate / totalQuantity;
            formik.setFieldValue('rate', averageRate.toFixed(2));
        }

        handleCloseModel(); // Close the modal after submission
    }
  return (
      <>
          <Modal
              show={show}
              onHide={handleCloseModel}
              centered
              className="custom-table-field-test-serial-modal"
          >
              <div className="modal-header py-1 ps-4 pe-3">
                  <button
                      className="ms-auto btn btn-icon btn-sm btn-active-light-primary ms-2 close-btn"
                      onClick={handleCloseModel}
                  >
                      <Close />
                  </button>
              </div>
              <Modal.Body className="px-4 py-2">
                  <form onSubmit={handleSubmit}>
                      <div className="w-100 overflow-auto">
                          {allType2FieldsDisabled || filteredCustomFieldDetail.length === 0 ? (
                              <div className="text-center py-4">
                                  <p className="mb-0 text-muted">No active custom fields available. All type 2 custom fields are disabled.</p>
                              </div>
                          ) : (
                              <>
                              <div className="custom-field-table-modal overflow-auto custom-table-field-test-serial mb-1">
                                  <table className="w-100">
                                      <thead>
                                          <tr>
                                              <th className="w-30px">Sr No.</th>
                                              {filteredCustomFieldDetail.map(field => (
                                                  <th key={field.id}>{field.label_name}</th>
                                              ))}
                                              <th>Qty</th>
                                              <th>Purchase Date</th>
                                              <th>Purchase Rate</th>
                                          </tr>
                                      </thead>
                                      <tbody>
                                          {paginatedRows.map(rowIndex => {
                                              return (
                                                  <tr key={rowIndex}>
                                                      <td className="text-start w-30px">
                                                          {rowIndex + 1}
                                                      </td>
                                                      {filteredCustomFieldDetail.map((field, colIndex) => {
                                                          return (
                                                              <td key={field.id} className='custom-input-p-0'>
                                                                  <FormInput
                                                                      type="text"
                                                                      value={
                                                                          tempFieldValues?.[rowIndex]?.[colIndex]?.value || ""
                                                                      }
                                                                      placeholder={field.label_name}
                                                                      onChange={e =>
                                                                          handleChange(
                                                                              e,
                                                                              rowIndex,
                                                                              field.id,
                                                                              colIndex,
                                                                              "value"
                                                                          )
                                                                      }
                                                                  />
                                                              </td>
                                                          );
                                                      })}
                                                      <td className='custom-input-p-0'>
                                                          <FormInput
                                                              type="number"
                                                              step="any"
                                                              min="1"
                                                              value={
                                                                  tempFieldValues?.[rowIndex]?.[filteredCustomFieldDetail.length]?.quantity || ""
                                                              }
                                                              onChange={e =>
                                                                  handleChange(
                                                                      e,
                                                                      rowIndex,
                                                                      'quantity',
                                                                      filteredCustomFieldDetail.length,
                                                                      "quantity"
                                                                  )
                                                              }

                                                              onKeyDown={e => {
                                                                  if (e.key === "-" || e.key === "e") {
                                                                      e.preventDefault();
                                                                  }
                                                              }}
                                                          />
                                                      </td>
                                                      <td className="datepicker-font custom-input-p-0">
                                                          <Datepicker
                                                            value={tempFieldValues?.[rowIndex]?.[filteredCustomFieldDetail.length + 1]?.purchase_date}
                                                            onChange={e =>
                                                                handleChange(e, rowIndex, 'purchase_date', filteredCustomFieldDetail.length + 1, "purchase_date")
                                                            }
                                                            isCustomStyle={true}
                                                          />
                                                      </td>
                                                      <td className='custom-input-p-0'>
                                                          <FormInput
                                                              type="number"
                                                              step="any"
                                                              min="0"
                                                              value={
                                                                  tempFieldValues?.[rowIndex]?.[filteredCustomFieldDetail.length + 2]?.purchase_rate || ""
                                                              }
                                                              onChange={e =>
                                                                  handleChange(
                                                                      e,
                                                                      rowIndex,
                                                                      'purchase_rate',
                                                                      filteredCustomFieldDetail.length + 2,
                                                                      "purchase_rate"
                                                                  )
                                                              }
                                                              onKeyDown={e => {
                                                                  if (e.key === "-" || e.key === "e") {
                                                                      e.preventDefault();
                                                                  }
                                                              }}
                                                          />
                                                      </td>
                                                  </tr>
                                              );
                                          })}
                                      </tbody>
                                  </table>
                              </div>
                              <button
                                  className="btn btn-primary btn-sm mt-2 mb-1"
                                  type="button"
                                  onClick={() => setCustomFieldLength(prev => prev + 1)}
                                  style={{ padding: "4px 14px 4px 10px", fontSize: "11px" }}
                              >
                                  ADD <span className="font-semibold fs-4 lh-1">+</span>
                              </button>

                          <div className="overflow-auto justify-content-center d-flex mb-2">
                              <nav>
                                  <ul className="pagination cursor-pointer">
                                      <li
                                          className={`page-item ${
                                              currentPage === 1 ? "disabled" : ""
                                          }`}
                                          onClick={() =>
                                              currentPage > 1 && setCurrentPage(p => p - 1)
                                          }
                                      >
                                          <span className="page-link">&lsaquo;</span>
                                      </li>
                                      {Array.from({
                                          length: Math.ceil(
                                              customFieldLength / rowsPerPage
                                          ),
                                      }).map((_, idx) => (
                                          <li
                                              key={idx}
                                              className={`page-item ${
                                                  currentPage === idx + 1 ? "active" : ""
                                              }`}
                                              onClick={() => setCurrentPage(idx + 1)}
                                          >
                                              <span className="page-link">{idx + 1}</span>
                                          </li>
                                      ))}
                                      <li
                                          className={`page-item ${
                                              currentPage ===
                                              Math.ceil(
                                                  customFieldLength /
                                                      rowsPerPage
                                              )
                                                  ? "disabled"
                                                  : ""
                                          }`}
                                          onClick={() =>
                                              currentPage <
                                                  Math.ceil(
                                                      customFieldLength /
                                                          rowsPerPage
                                                  ) && setCurrentPage(p => p + 1)
                                          }
                                      >
                                          <span className="page-link">&rsaquo;</span>
                                      </li>
                                  </ul>
                              </nav>
                          </div>
                          </>
                          )}
                      </div>

                      <div className="d-flex gap-3">
                          <Button variant="primary" type="submit" className="btn-sm fs-13">
                              Save
                          </Button>
                          <Button
                              variant="secondary"
                              onClick={handleCloseModel}
                              className="btn-sm fs-13"
                          >
                              Close
                          </Button>
                      </div>
                  </form>
              </Modal.Body>
          </Modal>
      </>
  );
}

export default ItemCustomFieldOpeningStock
