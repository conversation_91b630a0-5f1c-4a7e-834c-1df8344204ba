Base styles for the floating label .floating-label-container {
    position: relative;
    margin-top: 20px;
}

a.disabled {
  pointer-events: none;
  cursor: default;
}

.floating-label {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    transition: all 0.2s ease;
    pointer-events: none;
    color: #999;
}

.label-float {
    top: -10px;
    font-size: 12px;
    color: #000;
}

/* Target all react-select components */
.react-select__control {
    padding-top: 15px;
    padding-bottom: 5px;
    border-radius: 4px;
}

.react-select__control:focus-within .react-select__placeholder {
    color: transparent;
}

.react-select__value-container {
    padding: 0;
}

/* .input-group .css-1nmdiq5-menu {
    width: 144% !important;
} */

hr {
    margin: 0px !important;
}

.form-group,
.form-group-select {
    margin-bottom: 16px;
    position: relative;
}

.form-group input {
    position: relative;
    display: block;
    width: 100%;
    border: 1px solid #4f158c;
    border-radius: 10px;
    background-color: transparent;
    margin: 0px auto;
    padding: 6px 4px 4px 14px;
    height: 40px;
    outline: none !important;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
}

.modal .form-group input {
    height: auto !important;
}

.form-group label {
    position: absolute;
    left: 16.5px;
    text-align: left;
    display: inline-block;
    padding: 0 4px;
    height: 14px;
    line-height: 14px;
    font-size: 14px;
    font-weight: 400;
    background: white;
    color: #4f158c !important;
    margin: 0px auto;
    cursor: text;
}

.form-group-select label {
    position: absolute;
    left: 8.5px;
    text-align: left;
    display: inline-block;
    padding: 0 4px;
    height: 14px;
    line-height: 14px;
    font-size: 14px;
    font-weight: 400;
    background: white;
    color: #4f158c !important;
    margin: 0px auto;
    cursor: text;
    top: -6px;
    z-index: 1;
}

.form-check-input:checked {
    background-color: #4f158c !important;
}

.custom-date-input {
    padding: 0.4rem 0.8rem;
    color: #181c32;
    border: 1px solid #6f6f6f;
    border-radius: 6px;
    font-size: 1.1rem;
}

.custom-date-input:focus {
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08), 0 0 0 0.2rem rgba(79, 21, 140, 0.3) !important;
    border-color: rgba(79, 21, 140, 0.1) !important;
}
.custom-date-input:focus-visible {
    outline: none !important;
}
.custom-date-input.selected {
    color: #000 !important;
    /* Change this to your desired color */
}

/* .custom-date-input::placeholder {
    color: #fff;
} */

h5 {
    color: #4f158c;
    margin-bottom: 11px;
}

.color-label {
    color: #4f158c;
    padding: 6px 0;
}

.text-primary {
    color: #4f158c;
}

.dispatch-address-bg {
    background: #f5f8fa;
    border-radius: 7px;
}

.custom-paragraph {
    color: black;
    font-weight: 500;
}

.edit-icon {
    width: 13px;
    height: 40px;
    right: 45px;
    top: 2px;
    bottom: 0;
    margin: auto;
}

.h-22{
    height: 22px !important;
}

.h-40px {
    height: 34px !important;
}

.h-40 {
    height: 40px !important;
}

.add-item-transactions .form-floating-group .form-label {
    top: 8px;
}

.table-header {
    background-color: #4f158c;
    color: white;
    border: 1px solid #aabbaa;
}

.table-header th {
    padding: 8px;
    vertical-align: top;
}

.sales-table-container {
    border-radius: 7px 7px 0 0;
    overflow-x: auto;
    overflow-y: hidden;
}

.items-details-card {
    table {
        th {
            width: auto !important;
        }
    }
}

.payment-details-card table thead,
.items-details-card table tbody,
.items-details-card table tfoot {
    border: 1px solid #9fa9a7 !important;
}

.items-details-card table tfoot td {
    background-color: #f5f8fa;
    padding-top: 8px !important;
    padding-bottom: 6px !important;
    font-weight: 500;
}

.whitespace-nowrap {
    white-space: nowrap;
}

.select-item-input {
    width: 300px;
    min-width: 250px;
}

.select-ledger-input {
    width: auto;
    min-width: 180px;
}

.select-ledger-input div {
    min-width: auto !important;
}

.select-item-input .input-group .input-group-text,
.select-ledger-input .input-group .input-group-text {
    border: none;
    outline: none !important;
}

.customize-format-company-detail{
    border: none;
    outline: none !important;
}

.additional-info {
    flex: 1;
}

@media (min-width: 1200px) {
    .additional-info {
        min-width: 300px;
    }
}

.sales-table .form-control {
    min-width: 60px;
}

.sales-table .form-control::placeholder {
    color: #88888a;
}

.sales-table td {
    padding: 4px !important;
}

/* .sales-table .form-control:focus {
    box-shadow: none !important;
    border: none !important;
} */
.additional_discount .form-control:focus {
    box-shadow: none !important;
    border: none !important;
}

.additional_discount input {
    height: 32px !important;
}

.additional_discount .discount,
.sales-table_discount .discount {
    position: absolute;
    left: 1px;
    top: 1px;
    border-radius: 8px !important;
    width: 50px;
    z-index: 2;
}

.additional_discount .discount:focus-within,
.sales-table_discount .discount:focus-within {
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08), 0 0 0 0.2rem rgba(79, 21, 140, 0.3) !important;
    border-color: rgba(79, 21, 140, 0.1) !important;
}

.additional_discount .form-floating-group>.floating-label-input:focus~.form-label,
.additional_discount .form-floating-group>.floating-label-input:not(:placeholder-shown)~.form-label {
    background-image: linear-gradient(0deg, #ffffff 50%, #f5f8fa 50%);
    background-color: transparent;
}

.sales-table_discount {
    div {
        box-shadow: none !important;
        outline: none !important;

        &:focus {
            outline: none !important;
            box-shadow: none !important;
        }
    }
}

.sales-table .custom-group-text {
    right: 1px !important;
    height: 32px !important;
    top: 1px !important;
    bottom: 1px !important;
    border: none !important;
}

.sales-table .custom-group-text:focus {
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08), 0 0 0 0.2rem rgba(79, 21, 140, 0.3) !important;
    border-color: rgba(79, 21, 140, 0.1) !important;
}

.sales-table .custom-group-text-inr {
    left: 1px !important;
    height: 32px !important;
    top: 1px !important;
    bottom: 1px !important;
    border: none !important;
}

.delete-item {
    width: 30px;
}

.unit-price {
    min-width: 100px;
}

.unit-price .form-switch {
    padding-left: 55px !important;
}

.unit-price .form-switch .form-check-input {
    margin-left: -3.5rem !important;
}

.unit-price .form-switch .form-check-input:checked {
    background-color: #f76601 !important;
    border-color: #000000 !important;
}

.unit-price-dropdown {
    border: 1px solid white;
    border-radius: 4px;
    background-color: white;
    overflow: hidden;
    padding-left: 3px;
    text-align: start;
}

.unit-price-dropdown div {
    padding: 0;
    margin: 0;
    font-size: 12px;
    top: 0;
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
}

.unit-price-dropdown i.fa-chevron-down {
    font-size: 10px;
    color: #ffffff !important;
    padding: 4px 3px;
    background-color: #4f158c;
    border-radius: 0 4px 4px 0;
}

.min-w-60px {
    min-width: 60px;
}

.min-w-140px {
    min-width: 140px;
}

.min-w-110px {
    min-width: 110px;
}

.min-w-120px {
    min-width: 120px;
}

.min-w-180px {
    min-width: 140px;
}

.min-w-124px {
    min-width: 124px;
}

.max-w-130px {
    max-width: 130px;
}

.max-w-100px {
    max-width: 100px;
}

.ps-40px {
    padding-left: 40px;
}

.max-w-130px {
    max-width: 130px;
}

.content-wrapper {
    background-color: #ffffff;
    border: 1px solid #9f73ce;
    border-radius: 7px;
    padding: 40px;
    overflow: hidden;

    /* .col-xl-2 {
        @media (min-width: 1200px) {
            width: 18%;
        }
    } */
    .custom-group-text {
        right: 0;
    }

    .custom-group-text-inr {
        left: 0;
    }

    .css-1jqq78o-placeholder {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

.content-wrapper-invoice {
    background-color: #ffffff;
    border: 1px solid #9f73ce;
    border-radius: 7px 0 7px 7px;
    padding: 40px;
    overflow: hidden;

    /* .col-xl-2 {
        @media (min-width: 1200px) {
            width: 18%;
        }
    } */
    .custom-group-text {
        right: 0;
    }

    .custom-group-text-inr {
        left: 0;
    }

    .css-1jqq78o-placeholder {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

.content-wrapper.border-0 {
    border: none !important;
}

.lh-unset {
    line-height: unset !important;
}

.nav-tabs {
    gap: 10px;
    border-bottom: 0;
    .nav-item {
        .nav-link {
            background-color: #ffffff;
            color: #181c32;
            border: 1px solid #9f73ce;
            padding: 9px;
            min-width: 140px;
            height: 40px;
            &.active {
                background-color: #4f158c;
                color: #ffffff;
            }
        }
    }
}

.custom-nav-tabs {
    position: absolute;
    top: 0;
    right: 30px;

    .nav-item {
        .nav-link {
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 60px;
            padding: 7px;
        }
    }

    .arrow-btn {
        width: 35px;
        height: 35px;
        min-width: 35px;
        background-color: #4f158c;
        border: 1px solid #4f158c !important;
        border-radius: 50%;
    }

    .arrow-btn i {
        color: #eff2f5;
        font-size: 16px;
    }

    .arrow-btn:hover {
        background-color: #eff2f5;
        color: #4f158c;
    }

    .arrow-btn:hover i {
        color: #4f158c;
    }

    .arrow-btn.disabled {
        pointer-events: none;
        opacity: 0.6;
    }
}

.fw-500 {
    font-weight: 500 !important;
}

.additional-charges {
    min-width: 500px;
    background-color: #f5f8fa;
    border-radius: 10px;
    padding: 30px;
}

@media (max-width: 1199px) {
    .additional-charges {
        min-width: 0;
        width: 100%;
    }
}

.min-w-100px {
    min-width: 100px;
}

.border-transparent {
    border: 1px solid transparent !important;
    border-radius: 8px !important;

    &:hover {
        border: 1px solid #4f158c !important;
    }
}

.accordion-item {
    border: none;
}

.accordion-button {
    padding: 0 !important;
    background-color: transparent !important;
    width: fit-content;
    color: #4f158c;
    font-size: 16px;
    font-weight: 500;
    gap: 10px;
}

.accordion-body {
    padding: 0;
}

.fixed-bottom-section {
    position: sticky;
    bottom: 0;
    margin-bottom: -30px;
    z-index: 99;
}

.fixed-buttons {
    background-color: white;
    border-radius: 10px 10px 0 0;
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.25);
    padding: 15px 30px;
}
@media (max-width: 459px) {
    .fixed-bottom-section .fixed-buttons .btn {
        width: calc(50% - 5px);
    }
}
/* .css-1nmdiq5-menu {
    z-index: 11 !important;
} */
.w-100px {
    width: 100px;
}
.w-120px {
    width: 120px;
}
.w-135px {
    width: 135px;
    min-width: 135px;
}
.w-160px {
    width: 160px;
}

.min-w-100px {
    min-width: 100px;
}

.min-w-80px {
    min-width: 80px;
}

.w-240px {
    width: 240px;
}

.min-w-180px {
    min-width: 180px;
}

.w-35px {
    width: 35px;
}

.pe-36px {
    padding-right: 36px;
}
.pe-28px {
    padding-right: 28px;
}

.custom-company-button {
    right: 0 !important;
    width: 34px !important;
    height: 34px;
    min-width: 34px;
    border-radius: 8px !important;
    padding: 0 !important;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #4f158c;
}
.custom-company-button:focus {
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08), 0 0 0 0.2rem rgba(79, 21, 140, 0.3) !important;
    border: none !important;
}
.custom-group-text {
    right: 0 !important;
    width: 37px;
    border-radius: 0 8px 8px 0 !important;
    padding: 0 !important;
    display: flex;
    justify-content: center;
    align-items: center;
}

.custom-group-text:focus-visible {
    /* border : none !important; */
    outline: none !important;
}

.custom-group-text-inr {
    left: 0 !important;
    width: 37px;
    border-radius: 8px 0px 0px 8px !important;
}

.custom-group-text:focus,
.custom-group-text-inr:focus {
    outline: none !important;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08), 0 0 0 0.2rem rgba(79, 21, 140, 0.3) !important;
    border-color: rgba(79, 21, 140, 0.1) !important;
}

.custom-group-text.active {
    background-color: #000;
    color: #fff;
}

.input-group
    > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(
        .invalid-tooltip
    ):not(.invalid-feedback) {
    margin-left: 0;
}

/* .input-group:focus-within {
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08),
        0 0 0 0.2rem rgba(79, 21, 140, 0.3);
    border-radius: 8px;
} */
.input-group div {
    /* box-shadow: none !important; */
    outline: none !important;
    cursor: pointer !important;
}

.input-group div :focus {
    outline: none !important;
    /* box-shadow: none !important; */
}

.delivery-challan div {
    max-height: unset !important;
}

.configuration-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 60px;
    height: 40px;
    padding: 7px !important;
    background-color: #ffffff;
    color: #181c32;
    border: 1px solid #9f73ce !important;
    cursor: pointer;
    margin-bottom: -1px;
    border-radius: 0;
    border-top-left-radius: 0.475rem;
    border-top-right-radius: 0.475rem;
}

.custom-offcanvas-modal {
    position: fixed;
    top: 0;
    left: auto;
    right: -560px;
    width: 560px;
    height: 100%;
    background-color: #ffffff;
    transition: right 0.3s ease-in-out;
    z-index: 999999 !important;
    display: flex;
    flex-direction: column;
    border-radius: 10px 0 0 10px;
}

.custom-offcanvas-modal.show {
    right: 0;
}

.offcanvas-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 25px;
    color: #000000;
}

.offcanvas-body {
    padding: 25px 30px;
    overflow-y: auto;
    flex-grow: 1;
}
.desc-main-box {
    margin: 0px 20px 20px;
}
.desc-box {
    padding: 20px;
    background-color: #ffffff;
    border-radius: 10px;
}

.desc-box ::placeholder {
    color: #73757d !important;
}

.close-button {
    background: none;
    border: none;
    font-size: 30px;
    line-height: 1;
    color: #8e92a1 !important;
    cursor: pointer;
}

.cursor-pointer {
    cursor: pointer !important;
}

.backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.25);
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    z-index: 99999;
    display: none;
    cursor: pointer;
}

.backdrop.show {
    display: block;
    opacity: 1;
}

body.enable-scroll {
    overflow: hidden;
}

body:not(.enable-scroll) {
    overflow: auto;
}

.modal-backdrop {
    z-index: 99999;
}

.switch-mobile-app-modal {
    z-index: 9999999 !important;
}

.modal {
    z-index: 999999;
}

/* comment for not show model in dashboard page */
/* .modal-backdrop {
    z-index: 999999;
} */
.rearrange-item-modal .modal-content {
    background-color: #f5f8fa;
}

.add-custom-feild-modal .modal-dialog {
    max-width: 340px;
    margin: auto;
}

.min-w-92px {
    min-width: 92px;
}

.header {
    outline: 2px solid #4f158c;
}

.header-fixed .header {
    z-index: 99999 !important;
}

.drawer {
    z-index: 999999;
}

.details .detail {
    margin-bottom: 0 !important;
    color: #434343;
    font-size: 12px;
}

.details .address {
    font-size: 12px;
    color: #434343;
    margin-bottom: 0;
}

.details .label,
.details .detail,
.details .address {
    color: #000;
}

.details {
    border-bottom: 1px #000 !important;
}

.sale-configuration-modal {
    background-color: #f5f8fa;
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.25);
}

.sale-configuration-modal .modal-dialog {
    margin: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
}

.sale-configuration-modal .modal-content {
    background-color: #f5f8fa;
}

.sale-configuration-modal .offcanvas-body {
    padding: 1rem 22px;
    background-color: transparent;
    max-height: calc(100vh - 127px);
}

.sale-configuration-modal .offcanvas-header {
    padding: 12px 22px;
}

.sale-configuration-modal .offcanvas-footer .fixed-buttons {
    padding: 15px 22px;
}

.header-border-bottom {
    border-bottom: 1px solid #9f73ce !important;
}

/* .btn-icon {
    padding: 5px 27px 4px 10px;
} */

/* .btn-icon-primary::after {
    right: 7px;
    top: 13%;
} */

.slick-prev:before,
.slick-next:before {
    opacity: 1 !important;
    color: #4f158c !important;
}

.slick-prev {
    left: -40px !important;
}

.slick-next {
    right: -40px !important;
}

.pdf-template * {
    border-color: #a9a9a9 !important;
}

.border-left-blur {
    box-shadow: -7px 1px 9px -4px #f3e9ff;
}

.item-desc-table th:last-child,
.item-desc-table td:last-child {
    padding-right: 0.75rem !important;
}

.item-desc-table th:first-child,
.item-desc-table td:first-child {
    padding-left: 0.75rem !important;
}

.item-desc-table tfoot {
    background-color: #4f158c1a;
}

.signature,
.signature img {
    width: 100%;
    max-width: 132px;
    height: 100px;
    object-fit: contain;
}

.qr-code {
    width: 120px;
    min-width: 120px;
    height: 120px;
}

.qr-code img {
    width: 100%;
}

.company-logo {
    height: 80px;
    max-width: 85px;
    width: 100%;
}

.floating-label-input {
    position: relative;
    border-radius: 8px !important;
}

.form-control:focus {
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08), 0 0 0 0.2rem rgba(79, 21, 140, 0.3) !important;
    /* border-color: #4f158c !important; */
    /* box-shadow: none !important; */
    /* border color when input is not empty */
    /* border-color: #4f158c !important; */
}

.form-control::placeholder {
    color: #181c32;
}

.form-floating-group .form-label {
    pointer-events: none;
    background-color: #fff;
    color: #73757d;
    font-size: 14px !important;
    height: auto;
    left: 9px;
    margin-bottom: 0 !important;
    padding: 0 4px 0 4px !important;
    position: absolute;
    top: 6px;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: top 0.3s ease-in-out, font-size 0.3s ease-in-out, color 0.3s ease-in-out;
}

.item-master-label .form-floating-group .form-label {
    pointer-events: none;
    background-color: #fff;
    color: #73757d;
    font-size: 14px !important;
    height: auto;
    left: 9px;
    margin-bottom: 0 !important;
    padding: 0 4px 0 4px !important;
    position: absolute;
    top: 9px !important;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: top 0.3s ease-in-out, font-size 0.3s ease-in-out, color 0.3s ease-in-out;
}

.form-floating-group:has(input:disabled) .form-label {
    background-image: none;
    background-color: transparent !important;
}

.form-floating-group:has(input:disabled), .form-floating-group:has(input:read-only)
    > .floating-label-input:not(:placeholder-shown)
    ~ .form-label {
    background-image: linear-gradient(0deg, #eff2f5 45%, #ffffff 50%);
    background-color: transparent;
}

.form-floating-group > .floating-label-input:focus ~ .form-label,
.form-floating-group > .floating-label-input:not(:placeholder-shown) ~ .form-label {
    background-color: #fff !important;
    color: #4f158c !important;
    opacity: 1;
    top: -5px !important;
    transform: translateY(-0.5rem) translateX(0.15rem);
    width: fit-content;
}

.form-floating-group > .floating-label-input-2:focus ~ .form-label,
.form-floating-group > .floating-label-input-2:not(:placeholder-shown) ~ .form-label {
    background: linear-gradient(to bottom, #f5f8fa 50%, white 50%);
    color: #4f158c !important;
    opacity: 1;
    top: -5px !important;
    transform: translateY(-0.5rem) translateX(0.15rem);
    width: fit-content;
}

.add-item {
    background: linear-gradient(to bottom, #f5f8fa 50%, white 50%);
}

.upload-document {
    background: conic-gradient(
        at 70%,
        #ffffff 90deg,
        #ffffff 180deg,
        #f5f8fa 180deg,
        #f5f8fa 270deg,
        #ffffff 270deg
    );
}

.floating-label-input:not(:placeholder-shown) {
    /* border color when input is not empty */
    /* border-color: #4f158c !important; */
}

.pdf-container {
    width: min-content;
    margin: 0 auto;
    min-width: 850px;
}

.toast-card__toast-message {
    color: #495057;
    font-size: 14px;
    margin-bottom: 0 !important;
}

.col-spacing {
    margin-right: 25px;
}

.datepicker-container {
    position: relative;
}

.floating-label {
    position: absolute;
    top: 12px;
    left: 12px;
    font-size: 16px;
    color: #5e6278;
    transition: top 0.2s, font-size 0.2s;
}

.floating-label.label-up {
    top: -10px;
    font-size: 12px;
    color: #4f158c;
    background-color: white;
    padding: 0 4px;
}

.w-fit-content {
    width: fit-content;
    min-width: fit-content;
}

.border-right-primary {
    border-right: 1px solid #4f158c;
}

.border-left-primary {
    border-left: 1px solid #4f158c;
}

.react-tel-input .form-control {
    border-color: #6f6f6f !important;
    border-radius: 8px !important;
}

.react-tel-input .flag-dropdown {
    border: none !important;
    border-radius: 8px 0 0 8px !important;
}

.react-tel-input .selected-flag,
.react-tel-input .flag-dropdown.open .selected-flag {
    border-radius: 8px 0 0 8px !important;
    border: 1px solid #6f6f6f !important;
}

.react-tel-input .selected-flag:focus,
.react-tel-input .flag-dropdown.open .selected-flag:focus {
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08), 0 0 0 0.2rem rgba(79, 21, 140, 0.3) !important;
    border-color: rgba(79, 21, 140, 0.1) !important;
}

.credit-dropdown {
    right: 1px;
    top: 1px;
    min-width: 104px;
    border-left: 1px solid black;
    border-radius: 0 8px 8px 0;
}

.credit-dropdown div {
    border: none !important;
    box-shadow: none !important;
}

.credit-dropdown-select {
    right: 1px;
    top: 1px;
    min-width: 112px;
    border-left: 1px solid black;
    border-radius: 0 8px 8px 0;
    padding: 0 7px !important;
}

.credit-dropdown-select div {
    border: none !important;
    box-shadow: none !important;
    box-shadow: none !important;
    max-height: unset;
    padding: 0 !important;
    margin: 0;
    top: 0;
    font-size: 13px;
    line-height: normal;
}

.credit-dropdown-select.focus-shadow,
.credit-dropdown.focus-shadow {
    border-radius: 0 8px 8px 0 !important;
    border: none !important;
    border-left: 1px solid #6f6f6f !important;
}

.dropdown-menu-cost {
    color: #fff !important;
}

.form-control .end-date {
    border-radius: 0 8px 8px 0;
    height: 32px;
}

.form-control .start-date {
    border-radius: 8px 0 0 8px;
    height: 32px;
}

.invoice-number.form-control:focus {
    box-shadow: none !important;
}

.h-34px {
    height: 34px;
}
.h-26px {
    height: 26px;
}

.amount {
    max-width: calc(100% - 15px) !important;
    top: 5px !important;
    left: 54px !important;
}

.form-floating-group:has(input:disabled) .form-label {
    background-image: linear-gradient(0deg, #eff2f5 50%, #ffffff 50%);
    background-color: transparent !important;
}

.setting-btn {
    height: 40px;
    padding: 6px 20px !important;
    border: 1px solid transparent !important;
    color: #000000 !important;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.setting-btn:hover {
    background-color: #4f158c;
    border: 1px solid #4f158c !important;
    color: #ffffff !important;
}

.items-details-card .discount,
.additional-charges .discount {
    min-width: 50px;
}

.items-details-card .discount div,
.additional-charges .discount div {
    min-width: 50px;
    border: none !important;
    box-shadow: none !important;
}

.items-details-card .discount div i,
.additional-charges .discount div i {
    position: absolute;
    top: 9px;
    right: 8px;
    font-size: 13px;
    color: #747685 !important;
}

i.fa-chevron-down {
    font-size: 13px;
    color: #747685 !important;
}

.tcs-input div {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.radius-r-0,
.radius-r-0 div {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.form-control.shadow {
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08), 0 0 0 0.2rem rgba(79, 21, 140, 0.3) !important;
}

.shadow-none:focus {
    box-shadow: none !important;
}

/* .border-radius-0{
    border-radius: 0 !important;
} */
.main-img-container {
    width: 54px;
    min-width: 54px;
    height: 70px;
    background-color: #f5f5f5;
    border: 1px solid #dadada;
    border-radius: 5px;
    padding: 6px;
    position: relative;
    transition: all 0.3s ease-in-out;
}

.main-img-container:hover {
    box-shadow: 0 0 25px rgba(0, 0, 0, 0.3);
}

.delete-image-icon {
    border-radius: 50%;
    padding: 0;
    min-width: unset;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 1.75rem;
    width: 1.75rem;
    position: absolute;
    border: 2px solid #fff;
    top: 0;
    left: 100% !important;
    transform: translateX(-50%) translateY(-50%) !important;
}

.item-ledger-stock {
    color: #026c02;
}

.mfp-bg,
.mfp-wrap {
    z-index: 99999999;
}

.focus-shadow {
    border: 1px solid #6f6f6f;
    border-radius: 8px !important;
}

.focus-shadow:focus-within {
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08), 0 0 0 0.2rem rgba(79, 21, 140, 0.3) !important;
    border-color: rgba(79, 21, 140, 0.1) !important;
}

.border-light {
    border-color: rgba(79, 21, 140, 0.1) !important;
}

.focus-shadow div {
    border: none !important;
    box-shadow: none !important;
}

.success-btn {
    color: white;
    background-color: rgb(132, 180, 35);
}

.ledger-tabs .nav-link {
    min-width: auto !important;
    height: auto !important;
    background-color: transparent !important;
}

.border-transparent {
    border: 1px solid transparent !important;
}

.barcode-print-item-icon:focus,
.barcode-print-item-icon:focus-visible {
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08), 0 0 0 0.2rem rgba(79, 21, 140, 0.3) !important;
    border-color: rgba(79, 21, 140, 0.1) !important;
    outline: none !important;
}

.change-profile-btn:focus,
.change-profile-btn:focus-within {
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08), 0 0 0 0.2rem rgba(79, 21, 140, 0.3) !important;
}

.focus-icon-btn {
    border-radius: 4px;
    border: none;
    outline: none !important;
    line-height: normal;
    height: fit-content;
}

.focus-icon-btn i {
    height: fit-content;
}

.focus-icon-btn:focus,
.focus-icon-btn:focus-within {
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08), 0 0 0 0.2rem rgba(79, 21, 140, 0.3) !important;
}

.custom-min-height {
    min-height: calc(100vh - 135px);
}

.bg-img-none {
    background-image: none !important;
}

.fw-7 {
    font-weight: 700 !important;
}

.content-box {
    border-radius: 7px;
    box-shadow: 0 0 5px 0 #00000040;
    background-color: white;
}

.get-started-btn {
    text-transform: capitalize;
    font-size: 14px;
    font-weight: 700;
    border-radius: 8px;
    padding: 5px !important;
    color: white;
}

.get-started-btn.gradient-primary {
    background-image: linear-gradient(to right, #7b01e7, #d58dff);
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 19px !important;
    right: 24px !important;
    z-index: 2;
    padding: 4px !important;
    opacity: 0;
}

.details {
    background-color: #f5f8fa;
    border-radius: 8px;
    padding: 8px 16px;
}

.details .icon {
    width: 20px;
    height: 20px;
}

.details .text-success {
    color: #018a17 !important;
}

.details .text-danger {
    color: #cc0000 !important;
}
.performance-table {
    font-size: 12px !important;
}
.performance-table tr th:first-child,
.performance-table tr td:first-child {
    border-radius: 7px 0 0 7px;
}

.performance-table tr th:last-child,
.performance-table tr td:last-child {
    border-radius: 0 7px 7px 0;
}

.performance-table thead tr {
    background-color: #4f158c;
}

.performance-table tr:nth-child(even) {
    background-color: #f5f8fa;
}

.performance-table tr th,
.performance-table tr td {
    padding: 7px 10px !important;
}

.add-img {
    width: 82px;
    height: 75px;
}

.social-icon-box {
    min-height: 80px;
}

.social-icon {
    width: 26px;
    min-width: 26px;
    height: 26px;
}

@media (max-width: 1499px) {
    .social-icon {
        width: 24px;
        min-width: 24px;
        height: 24px;
    }
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
}

.read-more-link {
    display: flex;
    width: fit-content;
    justify-content: flex-end;
    margin-left: auto;
    color: #d65907 !important;
}

.fs-12 {
    font-size: 12px;
}

canvas {
    min-height: 256px;
}

@media (max-width: 1440px) {
    canvas {
        min-height: 200px;
    }
}

@media (max-width: 1199px) {
    canvas {
        min-height: 1px;
    }
}

.whatsapp-link {
    max-height: 400px;
}

.css-9jq23d {
    white-space: normal !important;
    word-break: break-all !important;
}

.closing-stock-details-table {
    border-bottom: 1px solid #e8eaed;
}

.closing-stock-details-table th,
.closing-stock-details-table td {
    border: 1px solid #e8eaed;
}

.closing-stock-details-table th:first-of-type,
.closing-stock-details-table td:first-of-type {
    border-left: none;
}

.closing-stock-details-table th:last-of-type,
.closing-stock-details-table td:last-of-type {
    border-right: none;
}

.flatpickr-calendar {
    z-index: 9999999 !important;
    /* Match your modal's z-index or set higher */
}

/* print setting css */

.print-settings-container {
    background-color: #f9f9f9;
    border: 1px solid #9f73ce;
    border-radius: 10px;
}

.print-settings-container .print-settings-tab {
    border-bottom: 1px solid #9f73ce;
    /* border-radius: 10px; */
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
}

.print-settings-container .form-control,
.print-settings-container .form-check-input {
    margin-bottom: 10px;
}

.print-settings-tab .nav-item .nav-link {
    font-size: 14px;
    font-weight: 600;
    padding: 8px 15px !important;
}

.print-settings-cheque {
    max-width: 1000px;
    height: 350px;
    background-color: #eaf3f7;
    border: 1px solid #ccc;
    border-radius: 2px;
    margin-top: 50px;
    overflow: hidden;
    position: relative;
    margin: 0 auto;
}

@media (min-width: 991px) {
    .border-lg-right {
        border-right: 1px solid #dee2e6;
    }
}

.pdf3-view {
    box-shadow: 0px 0px 13.82px 0px #00000026;
    padding: 19px 43px 21px 43px;
}

.main-table {
    border: 1px solid #a9a9a9;
}

.fs-13 {
    font-size: 13px;
}

.fs-14 {
    font-size: 14px;
}

.fs-12 {
    font-size: 12px;
}

.fw-bold {
    font-weight: 700;
}

.fw-medium {
    font-weight: 500 !important;
}

.fw-semibold {
    font-weight: 600;
}

.fw-normal {
    font-weight: 400;
}

.pdf-title {
    padding-top: 6px;
}

.pdf-title h6 {
    letter-spacing: 0.2px;
}

.original-text {
    letter-spacing: 0.2px;
    text-transform: uppercase;
}

.logo-box {
    margin-left: 6px;
    column-gap: 28px;
}

.logo-img {
    max-width: 88px;
    max-height: 83px;
    margin-left: 19px;
}

.hisabkitab-title {
    color: #f76600;
    font-size: 28px;
    font-weight: 700;
}

.hisabkitab-title span {
    color: #4f158c;
}

.phone-link {
    color: #181c32 !important;
}

.qr-blur-img {
    max-width: 141px;
    max-height: 141px;
}

.qr-blur-box {
    margin-right: 15px;
    column-gap: 13px;
}

.pdf-logo-title {
    margin-bottom: 32px;
}

.email-settings-tab .nav-item .nav-link {
    font-size: 15px;
    font-weight: 600;
    color: #000000;
    background-color: #f1f4f6;
    border: 1px solid #dde0e4;
    padding: 8px 15px !important;
}

.email-settings-tab .nav-item .nav-link.active {
    border: 1px solid #4f158c !important;
}

.email-settings-tab {
    border-bottom: 1px solid #dde0e4;
}

.print-header .offcanvas-header,
.print-table .offcanvas-header {
    padding: 12px 25px;
    border-bottom: 1px solid #9f73ce;
    border-radius: 0.475rem 0.475rem 0 0;
    z-index: 10 !important;
}

.print-header .btn-close,
.print-table .btn-close {
    color: #8e92a1;
    opacity: 1 !important;
    background-image: url("../images/close.svg");
    background-position: center;
    background-repeat: no-repeat;
}

.print-header .btn-close:focus,
.print-table .btn-close:focus {
    box-shadow: none !important;
}

.print-header .save-btn,
.print-table .save-btn {
    padding: 11px 20px !important;
}

.print-header .offcanvas-footer,
.print-total .offcanvas-footer {
    padding: 10px 20px;
    box-shadow: 0px 0px 20px 0px #00000040;
    border-radius: 0 0 10px 10px;
    gap: 10px;
    z-index: 4 !important;
}

.offcanvas-footer button {
    font-weight: 600 !important;
}

.print-header .offcanvas-body,
.print-table .offcanvas-body {
    padding: 25px 25px 88px 25px !important;
    position: relative;
    flex: 1 1 auto;
}

.print-header .form-switch,
.print-table .form-switch {
    max-width: 39px !important;
}

.print-header .edit-icon {
    cursor: pointer;
    width: 14px;
    height: 14px;
}

/* .modal-dialog {
    @media (min-width: 575px) {
        max-width: 560px !important;
    }
} */
.custom-check-input {
    right: 10px;
    top: 10px;
    width: 18px;
    height: 18px;
}

.custom-checkbox-orange input[type="checkbox"]:checked {
    background-color: #f76600 !important;
    border-color: #f76600 !important;
}

/* For Firefox */
.custom-checkbox-orange input[type="checkbox"]:checked::before {
    background-color: #f76600 !important;
}

/* Optional: Change the label color if needed */
.custom-checkbox-orange .form-check-label {
    color: #fff; /* or orange if you want the label in orange too */
}

/* form-check-input:checked[type=checkbox] { */

.fs-15 {
    font-size: 15px;
}

.ocr-pdf {
    border: 2px solid #cfb9e6;
    border-radius: 10px;
}

.ocr-details {
    border: 2px solid #cfb9e6;
    border-radius: 10px;
}

.delete_button:hover .remove_button {
    display: block !important;
}

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #fff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.dropdown-toggle .arrow {
    margin-left: 10px;
}

.dropdown-content {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    width: 250px;
}

.dropdown-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.dropdown-item:hover {
    background-color: #f9f9f9;
}

.date-range-picker-container {
    padding: 10px;
}

.dropdown-item:hover {
    background-color: #f9f9f9;
}

.date-range-picker-container {
    padding: 10px;
}

.reports-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.custom-dashboard-btn {
    background-color: #ff6600;
    /* Orange button color */
    color: white;
    font-weight: bold;
    border: none;
    border-radius: 5px;
    padding: 4px 4px 4px 4px !important;
    margin: 2px 0px 2px 0px !important;
    cursor: pointer;
    text-decoration: none;
    font-size: 12px !important;
}

.subreport-dropdown {
    position: absolute;
    top: 120px;
    left: -208px;
    background: white;
    border-radius: 10px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
    width: 220px;
    padding: 10px;
    z-index: 99999;
}

.subreport-dropdown a {
    display: block;
    padding: 10px;
    color: #6a1b9a;
    /* Purple text */
    font-weight: 500;
    text-decoration: none;
}

.subreport-dropdown a:hover {
    background: rgba(106, 27, 154, 0.1);
    /* Light purple hover effect */
    border-radius: 5px;
}

.book-demo {
    border-radius: 12px !important;
    background-color: white !important;
}

.get-book-btn {
    color: white !important;
    text-transform: capitalize;
    font-size: 14px;
    font-weight: 700;
    border-radius: 20px;
    padding: 8px !important;
    background-color: #d65907;
    padding-right: 20px !important;
    padding-left: 20px !important;
}

.accounting_demo {
    font-weight: 700;
}

.first-invoice h5 {
    font-size: 23px !important;
    font-weight: 700 !important;
    color: black !important;
}

.first-invoice p {
    font-size: 20px !important;
    font-weight: 400 !important;
}

.get-first-invoice-btn {
    text-transform: capitalize;
    font-size: 14px;
    font-weight: 700;
    border-radius: 20px;
    padding: 8px !important;
    color: white;
    padding-right: 20px !important;
    padding-left: 20px !important;
}

.get-first-invoice-btn.gradient-primary {
    background-image: linear-gradient(to right, #7b01e7, #d58dff);
}

.first-invoice-container {
    position: relative;
    height: 180px;
    margin-top: 16px !important;
}

.first-invoice-img {
    width: 101.5% !important;
    border-radius: 24px !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    position: absolute;
    right: -9px;
}

/* Main Container */
.suggestions-container {
    text-align: center;
    margin-top: 50px;
}

/* Clickable Image */
.suggestions-image-wrapper {
    display: flex;
    justify-content: center;
}

.suggestions-image {
    cursor: pointer;
    max-height: 200px;
    transition: transform 0.2s ease-in-out;
}

.suggestions-image:hover {
    transform: scale(1.05);
}

/* Modal Customization */
.suggestions-modal .modal-header-custom {
    border-bottom: none;
    border-bottom: 1px solid #9f73ce !important;
    padding-top: 16px !important;
    padding-bottom: 16px !important;
}

.modal-title-custom {
    font-weight: bold;
    font-size: 20px;
}

/* Form Labels */
.form-label-custom {
    font-weight: 600;
    color: #5c2d91;
    display: block;
    margin-bottom: 5px;
}

/* Form Fields */
.form-control-custom {
    border: 2px solid #9f73ce;
    border-radius: 6px;
    padding: 10px;
    width: 100%;
}

/* Modal Footer */
.modal-footer-custom {
    justify-content: start;
    border-top: none;
    padding-top: 16px !important;
}

/* Save Button */
.save-button {
    background-color: #5c2d91;
    border-color: #9f73ce;
    border-radius: 6px;
    font-weight: bold;
    padding: 10px;
}

.upload-document-suggestions {
    border-color: #9f73ce;
}

.save-button:hover {
    background-color: #4a2175;
}

/* Bottom Suggestion Form Button */
.bottom-button-wrapper {
    margin-top: 20px;
}

.save-button:hover {
    background-color: #4a2175;
}

/* Bottom Suggestion Form Button */
.bottom-button-wrapper {
    margin-top: 20px;
}

.upload-image-modal {
    width: 200px !important;
}

.upload-image-modal-body {
    /* width: fit-content !important; */
}

.image-plus {
    width: 120px !important;
    height: 120px !important;
    border-radius: 10px !important;
    border: 1px solid #dbdce2 !important;
    background-color: #f6f6f6 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.image-plus img {
    max-width: 120px !important;
    max-height: 120px !important;
}

.mobile_app_card {
    background: linear-gradient(135deg, #481f65, #183055);
    border-radius: 12px;
}

.mobile_app_card_demo {
    font-weight: 700;
    font-size: 17px;
    color: white;
    text-wrap: wrap;
    text-align: center;
}

.mobile_app_card_img {
    width: 84px;
    height: 84px;
    padding-right: 5px;
}

@media (max-width: 1380px) {
    .mobile_app_card_demo {
        font-size: 14px !important;
    }
}

@media (max-width: 1350px) {
    .mobile_app_card_mobileApp {
        width: 180px !important;
    }
}

@media (max-width: 1252px) {
    .mobile_app_card_mobileApp {
        width: 165px !important;
    }
}

.follow_us_heading {
    font-weight: 600 !important;
}

.follow_us_icon {
    width: 28px;
    height: 28px;
}

.get_started {
    margin-bottom: 15px !important;
}

.whats-new-container {
    /* padding: 50px 0; */
    width: 100%;
    background-color: white;
}

.whats-new-header {
    margin-bottom: 40px;
    /* // uncomment */
    height: 550px !important;
    display: flex !important;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    /* margin: -40px -30px 40px -30px; */
}

.whats-new-header h2 {
    font-size: 50px;
    font-weight: 700;
    color: #181c32;
}

.whats-new-header h3 {
    font-size: 40px;
    font-weight: 700;
    color: #181c32;
}

.whats-new-header .highlight-hisab {
    color: #4f158c;
    font-size: 40px;
    font-weight: 700;
}

.whats-new-header .highlight-kitab {
    color: #d65908;
    font-size: 40px;
    font-weight: 700;
}

.whats-new-header p {
    max-width: 900px;
    margin: 0 auto;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    color: #181c32;
}

.whats-new-content {
    text-align: center;
    margin-bottom: 40px;
}

.whats-new-intro {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 4px;
    padding: 0px 55px;
    margin-bottom: 50px;
}

.whats-new-content h4 {
    font-size: 16px;
    font-weight: 700;
    color: #181c32;
}

.whats-new-content p {
    color: #181c32;
    font-size: 16px;
    margin: 0 auto 20px;
    text-align: start;
}

.update-card {
    padding: 20px;
    border-radius: 7px;
    box-shadow: 0 0 9px 0 #00000017;
}

.mb-30px {
    margin-bottom: 30px !important;
}

.update-card-body {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 2px;
}

.update-card-subtitle {
    font-size: 14px;
    font-weight: 500;
    color: #181c32;
}

.update-card-title {
    font-size: 16px;
    font-weight: 600;
    color: #181c32;
    padding-left: 40px;
}

.update-card-text {
    font-size: 14px;
    font-weight: 500;
    color: #181c32;
}

.swiper-container {
    width: 100%;
    height: 100%;
}

.swiper-slide {
    display: flex;
    justify-content: center;
    align-items: center;
}

.rdrDayToday .rdrDayNumber span:after {
    background: #4f158c !important;
}

.book_demo {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 20px !important;
    padding: 25px 50px !important;
    position: relative !important;
}

.book_demo h4 {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #181c32 !important;
    letter-spacing: 0.09px !important;
}

.book_demo span {
    font-size: 15px !important;
    font-weight: 500 !important;
    color: #73757d !important;
    letter-spacing: 0.09px !important;
    text-align: center !important;
}

.book_demo_btn {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 10px !important;
}

.book_demo_confirm {
    padding: 10px 20px !important;
    border-radius: 7px !important;
    background-color: #4f158c !important;
    color: white !important;
    font-size: 15px !important;
    font-weight: 600 !important;
    border: none !important;
}

.book_demo_cancel {
    padding: 10px 20px !important;
    border-radius: 7px !important;
    background-color: #dde0e4 !important;
    color: #3f4254 !important;
    font-size: 15px !important;
    font-weight: 600 !important;
    border: none !important;
}

.book_demo_close {
    position: absolute !important;
    top: -25px !important;
    right: -6px !important;
    cursor: pointer !important;
    font-size: 28px !important;
    color: #3f4254 !important;
    /* border-radius: 50% !important; */
    padding: 1px !important;
    background: transparent !important;
    border: none !important;
}

.upload_doc_close {
    position: absolute !important;
    top: -8px !important;
    right: 10px !important;
    cursor: pointer !important;
    font-size: 28px !important;
    color: #3f4254 !important;
    /* border-radius: 50% !important; */
    padding: 1px !important;
    background: transparent !important;
    border: none !important;
}

.driver-popover {
    background-color: transparent !important;
    box-shadow: none !important;
    max-width: 465px !important;
    min-width: 465px !important;
    padding-left: 50px !important;
    width: 100%;
}

.driver-popover-title {
    font-size: 24px !important;
    font-weight: 900 !important;
    color: #ffffff !important;
    margin-bottom: 10px;
}

.driver-popover-description {
    font-weight: 800 !important;
    font-size: 20px !important;
    color: #ffffff !important;
}

/* .driver-popover-arrow {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 10px 10px 0 10px;
    border-color: rgba(255, 255, 255, 0.9) transparent transparent transparent;
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
} */

.driver-popover-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.driver-popover-next-btn {
    background-color: #ffffff !important;
    color: #181c32 !important;
    border: none;
    padding: 5px 15px !important;
    border-radius: 7px !important;
    cursor: pointer;
    font-size: 18px !important;
    font-weight: 700 !important;
}

/*
.driver-popover-footer button:hover {
    background-color: #3a0f6b;
} */
.skip-button {
    background-color: #4f158c !important;
    border: none !important;
    padding: 5px 18px !important;
    border-radius: 7px !important;
    cursor: pointer;
    line-height: 1.5 !important;
    font-family: Inter !important;
    font-size: 18px !important;
    font-weight: 500 !important;
    color: white !important;
}

.driver-popover-navigation-btns {
    display: flex;
    flex-grow: 1;
    gap: 80px;
    align-items: center;
    justify-content: start !important;
}

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #4f158cf2;
    /* Dark semi-transparent overlay */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99999;
}

.welcome-box {
    /* background: white; */
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    /* max-width: 400px; */
    width: 100%;
    /* box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1); */
}

.welcome-box h3 {
    color: #ffffff !important;
    font-size: 20px !important;
    font-weight: 900 !important;
}

.welcome-box p {
    color: #ffffff !important;
    font-size: 20px !important;
    font-weight: 900 !important;
}

.button-container {
    display: flex;
    gap: 30px;
    justify-content: center;
    margin-top: 25px;
}

.button-container button {
    min-width: 140px;
    font-size: 15px;
}

.btn-orange-overlay {
    background-color: #f76601;
    border: none;
    padding: 10px;
    font-weight: 800;
    color: white;
    border-radius: 7px;
}

.btn-white-overlay {
    background-color: white;
    border: none;
    padding: 10px;
    font-weight: 800;
    color: #181c32;
    border-radius: 7px;
}

/* tour toaster */

.custom-toast {
    background: #ffffff;
    color: #181c32 !important;
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    width: 400px;
}

.icon-wrapper {
    background: #ffc107;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-right: 10px;
}

.text-content {
    flex: 1;
}

.text-content strong {
    font-size: 17px !important;
    font-weight: 600 !important;
}

.text-content p {
    font-size: 13px !important;
    font-weight: 400 !important;
}

.watch-btn {
    background: none;
    border: none;
    color: #ffd426;
    font-weight: 600;
    cursor: pointer;
    padding: 5px;
    font-size: 14px !important;
}

.driver-overlay {
    z-index: 99999 !important;
    top: 0px !important;
    left: 0px !important;
    width: 100% !important;
    height: 100% !important;
}

.driver-overlay path {
    opacity: 0.9 !important;
}

.driver-popover-close-btn,
.driver-popover-progress-text {
    display: none !important;
    opacity: 0 !important;
    font-size: 0px !important;
}

.driver-popover.step-9 {
    max-width: 560px !important;
    min-width: 560px !important;
}

.driver-popover-arrow-side-left.driver-popover-arrow-align-start,
.driver-popover-arrow-side-right.driver-popover-arrow-align-start {
    border: none !important;
    background-image: url("../images/take-tour-arrow.png");
    width: 184px;
    height: 167px;
    margin-bottom: 20px;
    position: absolute !important;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.driver-popover-arrow.driver-popover-arrow-side-top,
.driver-popover-arrow.driver-popover-arrow-side-bottom,
.driver-popover-arrow-none {
    border: none !important;
    display: block !important;
    background-image: url("../images/take-tour-bottom-arrow.png") !important;
    width: 184px;
    height: 167px;
    margin-bottom: 20px;
    position: absolute !important;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.step-7 .driver-popover-arrow.driver-popover-arrow-side-left {
    border: none !important;
    background-image: url("../images/step-5-arrow.png") !important;
    width: 184px;
    height: 167px;
    margin-top: 20px;
    position: absolute !important;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.post .Toastify__toast-container {
    padding: 0 !important;
}

.post .Toastify__toast-container .Toastify__toast {
    padding: 12px 16px !important;
    box-shadow: 0px 8px 10px 0px #00000033 !important;
}

.post .Toastify__toast-body,
.post .custom-toast {
    padding: 0 !important;
    width: 100% !important;
}

.post .text-content strong {
    color: #ffffff !important;
    line-height: 1.3 !important;
}

.post .text-content p {
    color: #c8c5c5 !important;
    margin-bottom: 2px !important;
    letter-spacing: -0.08px !important;
    line-height: 1.4 !important;
}

.post .text-content .watch-btn {
    padding: 0 !important;
    line-height: 1.1 !important;
    letter-spacing: -0.32px !important;
}

.post .icon-wrapper {
    width: 32px !important;
    height: 32px !important;
    border: 3px solid #303746 !important;
    border-radius: 50% !important;
    background-color: #face08 !important;
}

.driver-popover.step-2,
.driver-popover.step-3,
.driver-popover.step-4 {
    margin-top: 167px !important;
}

.driver-popover.step-1 {
    margin-top: 250px !important;
}

.step-1 .driver-popover-arrow {
    top: -167px !important;
    left: 0px;
}

.step-2 .driver-popover-arrow {
    top: -167px !important;
    right: 0;
    background-image: url("../images/step-2-arrow.png");
    margin-left: auto;
}

@media (max-width: 1319px) {
    .driver-popover .step-2 {
        margin-left: -390px !important;
    }

    .step-2 .driver-popover-arrow {
        transform: rotateY(0deg) !important;
        right: -167px !important;
        top: 0 !important;
    }
}

.step-3 .driver-popover-arrow {
    top: -167px !important;
    right: 0 !important;
    background-image: url("../images/step-2-arrow.png") !important;
    margin-left: auto;
}

.step-6 .driver-popover-title,
.step-7 .driver-popover-title,
.step-8 .driver-popover-title,
.step-9 .driver-popover-title {
    text-align: end !important;
}

.step-6 .driver-popover-description,
.step-7 .driver-popover-description,
.step-8 .driver-popover-description,
.step-9 .driver-popover-description {
    text-align: end !important;
}

.step-6 .driver-popover-navigation-btns,
.step-7 .driver-popover-navigation-btns,
.step-8 .driver-popover-navigation-btns,
.step-9 .driver-popover-navigation-btns {
    justify-content: end !important;
}

.step-4 .driver-popover-arrow-side-right {
    left: 0 !important;
    top: -167px !important;
}

.step-6 .driver-popover-arrow.driver-popover-arrow-side-bottom,
.step-8 .driver-popover-arrow.driver-popover-arrow-side-bottom,
.step-9 .driver-popover-arrow.driver-popover-arrow-side-bottom {
    left: 100%;
    top: 0;
}

.step-6 .driver-popover-arrow.driver-popover-arrow-side-top,
.step-8 .driver-popover-arrow.driver-popover-arrow-side-top,
.step-9 .driver-popover-arrow.driver-popover-arrow-side-top {
    right: 85%;
    left: auto;
    transform: rotate(150deg);
    top: 40%;
}

.step-7 .driver-popover-arrow-side-bottom {
    right: auto;
    left: 100%;
    top: 0;
}

.step-7 .driver-popover-arrow-side-left {
    right: 0;
    top: 100% !important;
    bottom: 0 !important;
    left: 65%;
    transform: rotateY(180deg);
}

.step-9 .driver-popover-arrow-none {
    transform: rotateY(180deg);
    left: -120px;
    top: 0;
}

.step-10.driver-popover {
    max-width: 620px !important;
    min-width: 620px !important;
    left: 40% !important;
}

.step-10 .driver-popover-arrow.driver-popover-arrow-side-bottom,
.step-10 .driver-popover-arrow-none {
    right: 100%;
    left: auto;
    top: 0;
    transform: rotateY(180deg);
    display: block !important;
}

.step-10 .driver-popover-arrow.driver-popover-arrow-side-top {
    right: 96%;
    left: auto;
    transform: rotate(180deg);
    top: 25%;
}

.step-5 .driver-popover-arrow-side-right.driver-popover-arrow-align-end {
    border: none !important;
    background-image: url("../images/take-tour-arrow.png");
    width: 184px;
    height: 167px;
    margin-bottom: 20px;
    position: absolute !important;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.whatsapp-link {
    scrollbar-width: unset !important;
    scrollbar-color: #446687 transparent !important;
    overflow-y: hidden;
}

.whatsapp-link::-webkit-scrollbar {
    width: 20px !important;
    height: 270px !important;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
}

.whatsapp-link::-webkit-scrollbar-thumb {
    background: #000000 !important;
    min-height: 270px !important;
}

.flatpickr-wrapper {
    width: 100% !important;
}

.settings-modal {
    background-color: #f5f8fa;
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.25);
}

.settings-modal .modal-dialog {
    margin: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
}

.settings-modal .modal-content {
    background-color: #f5f8fa;
}

.settings-modal .offcanvas-body {
    padding: 1rem 22px;
    max-height: calc(100vh - 141px);
    min-height: calc(100vh - 141px);
}
.custom-field-modal .offcanvas-body {
    padding: 1rem 22px;
    max-height: calc(100vh - 142px) !important;
    min-height: calc(100vh - 142px) !important;
}

.settings-modal .offcanvas-header,
.invoice-modal .offcanvas-header {
    padding: 12px 22px;
    border-bottom: 1px solid #9f73ce !important;
}

.settings-modal .offcanvas-footer .fixed-buttons {
    padding: 15px 22px;
}

.modal-content-box {
    background-color: #f5f8fa;
    border-radius: 10px;
}

.modal-content-box .add-box {
    padding: 6px 13px;
    border-radius: 7px;
    max-width: 275px;
}

.email-table tr td,
.email-table tr th {
    border: 1px solid #dde0e4;
    color: #181c32;
    font-size: 13px;
    font-style: normal;
    font-weight: 500;
    line-height: 25px;
    max-width: 400px;
    min-width: 63px;
    padding: 7px;
}

.email-table tr td:first-child,
.email-table tr th:first-child {
    padding-left: 0;
    border-left: none;
}

.email-table tr td:last-child,
.email-table tr th:last-child {
    padding-right: 0;
    border-right: none;
}

.email-table {
    border-bottom: 1px solid #dde0e4;
}

.radio-box {
    background-color: #eff2f5;
    border-radius: 12px;
}

.ws-radio {
    width: 1.75rem;
    min-width: 1.75rem;
    height: 1.75rem;
}

.scroll-color {
    scrollbar-width: auto !important;
    scrollbar-color: #4f158c transparent !important;
}

.invoice-modal .modal-dialog {
    border-radius: 10px;
    overflow: hidden;
    margin-top: 30px;
    margin-bottom: 30px;
    max-width: 1320px !important;
}

.invoice-modal .modal-dialog .modal-content {
    height: calc(100vh - 60px);
}

.invoice-modal .offcanvas-body {
    padding: 25px 15px;
    max-height: calc(100vh - 188px);
    min-height: calc(100vh - 188px);
    scrollbar-color: #4f158d transparent !important;
}

.invoice-modal .offcanvas-body .invoice-template,
.invoice-modal .offcanvas-body .invoice-template-full {
    padding: 10px;
    background-color: white;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    max-width: 1140px;
    margin: 0 auto;
}

.invoice-template img {
    width: 350px !important;
    height: 530px !important;
    aspect-ratio: 0.71;
    border-radius: 10px;
}

.invoice-template.selected,
.invoice-template-full.selected {
    background-color: #4f158c !important;
}

.invoice-template-full img {
    width: 100% !important;
    height: 530px !important;
    aspect-ratio: 0.71;
    border-radius: 10px;
}

.offcanvas-body .form-floating-group .form-check {
    position: absolute;
    top: 6px;
    right: 0;
}

.edit-modal .offcanvas-header {
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 0.475rem;
    border-top-right-radius: 0.475rem;
}

.edit-modal .modal-content {
    border-radius: 8px;
    overflow: hidden;
}

.edit-modal .close-button {
    color: white !important;
    font-size: 24px !important;
}

@media (min-width: 576px) {
    .edit-modal .modal-sm {
        max-width: 300px !important;
    }
}

.list-item-name {
    max-height: 60px !important;
    overflow: auto !important;
}

.company-dashboard-report-btn-bg {
    width: 45px;
    height: 45px;
    background-color: white;
    border-radius: 5px;
    position: fixed;
    top: 35%;
    right: 0;
    z-index: 9;
}

.company-dashboard-report-button:hover i {
    color: #eff2f5 !important;
}

.report-buttons-modal {
    overflow: visible !important;
}

.offcanvas-reportButton-body {
    padding: 25px 30px;
    flex-grow: 1;
}

.book-img {
    max-width: 536px;
    max-height: 151px;
    border-radius: 8px;
    cursor: pointer;
    aspect-ratio: 536/151;
}

.book-demo-content {
    row-gap: 15px;
}

.company-dashboard-report-button {
    background-color: #eff2f5;
    border-radius: 5px;
    height: 32px;
    width: 32px;
}

.company-dashboard-report-button i {
    font-size: 20px;
    padding-right: 0;
    color: #4f158c;
}

.company-dashboard-report-button:hover {
    background-color: #4f158c !important;
}

.report-buttons-modal {
    width: 280px !important;
}

.payment-amount::placeholder {
    color: #73757d;
    opacity: 1;
}

.input-group-cashflow {
    align-items: end !important;
    justify-content: end !important;
}

.custom-toast {
    width: 451px !important;
    background-color: #242c32 !important;
}

.Toastify__toast-container--top-center > div:first-child {
    width: 451px !important;
    background-color: #242c32 !important;
}

.itemNotExists {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 5px;
    width: 100%;
}

.invoice-template img {
    width: 100%;
    aspect-ratio: 0.71;
}

.invoice-template.selected {
    background-color: #4f158c !important;
}

.offcanvas-body .form-floating-group .form-check {
    position: absolute;
    top: 6px;
    right: 0;
}

.edit-modal .offcanvas-header {
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 0.475rem;
    border-top-right-radius: 0.475rem;
}

.edit-modal .modal-content {
    border-radius: 8px;
    overflow: hidden;
}

.edit-modal .close-button {
    color: white !important;
    font-size: 24px !important;
}

@media (min-width: 576px) {
    .edit-modal .modal-sm {
        max-width: 300px !important;
    }
}

.skeleton {
    background: #e1e1e1;
    height: 59px;
    position: relative;
    overflow: hidden;
    margin: 3% 0 0 0;
    border-radius: 8px;
}

.skeleton-filter {
    background: #e1e1e1;
    width: 150px;
    height: 30px;
    position: relative;
    overflow: hidden;
    margin: 3% 0 0 0;
    border-radius: 8px;
}

.skeleton-table {
    background: #e1e1e1;
    height: 49px;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.skeleton::before,
.skeleton-table::before,
.skeleton-filter::before {
    content: "";
    display: block;
    position: absolute;
    left: -150px;
    top: 0;
    height: 100%;
    width: 150px;
    background: linear-gradient(to right, transparent 0%, #e8e8e8 50%, transparent 100%);
    animation: load 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

@keyframes load {
    from {
        left: -150px;
    }

    to {
        left: 100%;
    }
}

.skeleton-vertical-chart {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    /* Align bars to the bottom */
    height: 275px;
    /* Height of the entire chart */
    padding: 10px;
    background-color: #f0f0f0;
    border-radius: 8px;
}

.skeleton-bar {
    width: 20px;
    /* Width of each bar */
    background-color: #e0e0e0;
    border-radius: 4px;
    animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

.skeleton-row {
    background-color: transparent !important;
}

.skeleton-cashflow-table {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px;
    background-color: #f0f0f0;
    border-radius: 8px;
}

.skeleton-cashflow-row {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.skeleton-cell {
    height: 20px;
    flex: 1;
    background-color: #e0e0e0;
    border-radius: 4px;
    animation: pulse 1.5s infinite ease-in-out;
}

.skeleton-header {
    height: 24px;
    background-color: #d0d0d0;
}

.form-floating-group:has(input:disabled) .form-label {
    background-image: none;
    background-color: transparent !important;
}

.devices-modal-close {
    font-size: 24px !important;
    color: white !important;
    background: transparent !important;
    border: none !important;
    cursor: pointer !important;
    font-weight: 600 !important;
}

.device-modal {
    display: flex !important;
    flex-direction: column !important;
    align-items: start !important;
    justify-content: center !important;
    gap: 20px !important;
    /* padding: 25px 50px !important; */
    position: relative !important;
}

.Add_company {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 20px !important;
    padding: 25px 50px !important;
    position: relative !important;
}

.Add_company h4 {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #181c32 !important;
    letter-spacing: 0.09px !important;
}

.Add_company_content span {
    font-size: 15px !important;
    font-weight: 500 !important;
    color: #73757d !important;
    letter-spacing: 0.09px !important;
    text-align: center !important;
}

.Add_company_btn {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 10px !important;
}

.Add_company_confirm {
    padding: 10px 20px !important;
    border-radius: 7px !important;
    background-color: #4f158c !important;
    color: white !important;
    font-size: 15px !important;
    font-weight: 600 !important;
    border: none !important;
}

.Add_company_cancel {
    padding: 10px 20px !important;
    border-radius: 7px !important;
    background-color: #dde0e4 !important;
    color: #3f4254 !important;
    font-size: 15px !important;
    font-weight: 600 !important;
    border: none !important;
}

.Add_company_close {
    position: absolute !important;
    top: -25px !important;
    right: -6px !important;
    cursor: pointer !important;
    font-size: 28px !important;
    color: #3f4254 !important;
    /* border-radius: 50% !important; */
    padding: 1px !important;
    background: transparent !important;
    border: none !important;
}

.Add_company_content {
    width: 600px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.logo-modal {
    min-width: 294px !important;
    max-width: 294px !important;
}

.logo-modal .modal-body {
    padding: 25px 25px 17px 25px !important;
    border-radius: 7px !important;
}

.logo-modal h5 {
    color: #181c32 !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    line-height: 1.5 !important;
}

.logo-modal .logo-save-btn {
    font-weight: 600 !important;
    font-size: 16px !important;
    margin-top: 16px !important;
}

.logo-modal h6 {
    margin-bottom: 2px !important;
    font-family: 14px !important;
    line-height: 1.5 !important;
}

.or_code_notes {
    font-size: 14px;
    font-weight: 500;
    text-align: center;
}

.qr_code_container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 20px;
}

.disabled-button {
    cursor: not-allowed !important;
}

.pdf-auto {
    scrollbar-width: auto !important;
    scrollbar-color: #4f158c transparent !important;
    overflow: auto;
    padding-bottom: 8px;
}

.w-990px {
    width: min-content;
    margin: 0 auto;
    min-width: 900px;
}

.custom-next-button {
    background-color: #ffffff !important;
    color: #181c32 !important;
    border: none;
    padding: 5px 15px !important;
    border-radius: 7px !important;
    cursor: pointer;
    font-size: 18px !important;
    font-weight: 500 !important;
}

/* Smooth transitions for the popover */
.driver-popover {
    transition: opacity 0.8s ease, transform 0.8s ease;
    opacity: 0;
    transform: translateY(20px);
}

.driver-popover.driver-popover-next {
    opacity: 0;
    transform: translateY(20px);
}

.driver-popover.driver-popover-active {
    opacity: 1;
    transform: translateY(0);
}

.driver-overlay {
    transition: opacity 0.8s ease;
}

.aside,
.header {
    position: relative;
}

.aside::after {
    position: absolute;
    top: 0;
    right: -10px;
    content: "";
    background-color: #4f158ce6;
    width: 15px;
    height: 100%;
    opacity: 0;
}

.header-after::after {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    content: "";
    background-color: #4f158ce6 !important;
    width: 220px !important;
    height: 100% !important;
    opacity: 1 !important;
}

.header::after {
    position: absolute;
    bottom: 0;
    left: 0;
    content: "";
    background-color: #4f158ce6;
    width: 240px;
    height: 10px;
    border-radius: 0 3px 0 0;
    opacity: 0;
}

.aside.driver-active-element::after,
.header.driver-active-element::after,
.general-menu-after::after {
    opacity: 1;
    pointer-events: none !important;
}

.driver-active .aside {
    box-shadow: 0 0 30px 0 #000000a6;
    pointer-events: none !important;
}

.driver-active-element .aside-menu *,
.driver-active-element.header-button *,
.driver-active-element.header-right-menu *,
.driver-active-element.aside-logo *,
.driver-active-element.general-menu *,
.driver-active-element.business-glance *,
.driver-active-element.key-reports *,
.driver-active-element.monitor-growth *,
.driver-active-element.payment-key *,
.driver-active-element.track-key * {
    pointer-events: none !important;
}

.header-after .aside-logo {
    position: relative;
    z-index: 99;
    justify-content: center;
    border-radius: 10px;
    height: 46px !important;
    min-height: 46px !important;
    align-self: center;
}

.general-menu-after {
    position: relative;
}

.general-menu-after::after {
    position: absolute !important;
    right: -10px !important;
    top: 496px !important;
    content: "";
    background-color: #4f158ce6 !important;
    width: 10px !important;
    height: 198px !important;
    opacity: 1 !important;
    border-radius: 0 5px 4px 0;
}

.driver-popover {
    animation: fadeIn1 0.3s ease-in-out !important;
}

.driver-active-element.business-glance {
    margin: 45px 0 !important;
}

.driver-active-element .business-mb-0 {
    margin-bottom: 0 !important;
}

.payment-key.driver-active-element {
    margin-top: 10px !important;
}

.payment-key.driver-active-element .col-md-6 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

.payment-key.driver-active-element + .track-key {
    margin-top: 20px !important;
}

.payment-key-mb {
    margin-bottom: 20px !important;
}

.step-4-mb-above-content {
    margin-bottom: 30px !important;
}

.step-5.driver-popover .driver-popover-arrow {
    background-image: url("../images/step-5-arrow.png") !important;
    bottom: -180px !important;
    top: auto !important;
    left: -39px !important;
}

.step-5.driver-popover {
    margin-top: -270px !important;
    padding: 12px !important;
}

.step-7.driver-popover {
    margin-top: -140px !important;
    padding: 12px !important;
}

.step-1.driver-popover,
.step-4.driver-popover,
.step-5.driver-popover {
    animation: fadeIn2 0.3s ease-in-out !important;
}

@keyframes fadeIn1 {
    from {
        opacity: 0;
        transform: translatex(-20px);
    }

    to {
        opacity: 1;
        transform: translatex(0);
    }
}

@keyframes fadeIn2 {
    from {
        opacity: 0;
        transform: translatex(20px);
    }

    to {
        opacity: 1;
        transform: translatex(0);
    }
}

@media (min-width: 767px) {
    .payment-key-main {
        margin-bottom: 1.25rem !important;
    }
}

.overflow-text-input [data-value] {
    overflow: auto;
    scrollbar-width: none;
}

.overflow-text-input:has(.edit-icon) [data-value] {
    margin-right: 20px;
}

.logo-modal-container {
    min-width: 294px !important;
    max-width: 294px !important;
}

.logo-modal-container .modal-body {
    padding: 25px 25px 17px 25px !important;
    border-radius: 7px !important;
}

@media (max-width: 1199px) {
    .shortcut-aside-menu .btn.btn-icon {
        height: 100% !important;
    }

    .shortcut-aside-menu {
        display: flex !important;
    }

    .header-right-menu {
        margin-left: 0 !important;
    }

    .search-text {
        top: 0 !important;
    }

    .mobile-btn-shortcut-view {
        padding: 0px 10px !important;
        margin: 0 !important;
    }
}
.ocr-nav-tabs {
    position: absolute;
    top: -29px !important;
    right: 53px !important;
}

.party-items-not-exist {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 5px;
    max-width: 200px;
    max-height: 70px;
    overflow: auto;
}

.party-items-not-exist li {
    overflow-wrap: anywhere;
}

.customer-report-page {
    padding-left: 17px;
    padding-right: 22px;
}

.customer-report-main {
    border: 1px solid #4f158c;
    padding: 20px;
    border-radius: 7px;
}

.search-icon-box {
    border-radius: 7px;
    border: 1px solid #747685;
    max-width: 290px;
    padding: 9px 12px;
}

.search-icon-svg {
    width: 15px;
    height: 15px;
}

.search-icon-box input {
    /* color: #ABAAAA !important; */
    border: none !important;
    font-size: 13px;
    font-weight: 600;
}

.search-icon-box input:focus {
    outline: none !important;
}

.search-icon-box input::placeholder {
    /* color: #ABAAAA !important; */
}

.customer-report-table td,
.customer-report-table th {
    border: 1px solid #abaaba !important;
    background-color: white !important;
    vertical-align: middle !important;
}

.customer-report-table thead tr th {
    border-top: none !important;
    border-bottom: none !important;
    position: relative;
}

.customer-report-table thead tr th:first-child,
.customer-report-table tbody tr td:first-child {
    border-left: none !important;
}

.customer-report-table thead tr th:last-child,
.customer-report-table tbody tr td:last-child {
    border-right: none !important;
}

.flipped.border-box {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #abaaba;
}

.customer-report-table thead tr:first-child th {
    background-color: #4f158c !important;
    color: white !important;
    font-weight: 700;
    padding: 10px 5px 10px 15px !important;
    /* min-width: 170px !important; */
    font-size: 12px;
    white-space: nowrap;
}

.customer-report-table thead th:nth-child(4),
.customer-report-table thead th:nth-child(5) {
    min-width: 220px !important;
}

.customer-report-table thead tr:first-child th:last-child {
    min-width: 90px !important;
    padding: 10px 20px 10px 15px !important;
    text-align: end !important;
}

.customer-report-table tbody td:last-child {
    padding: 5px 20px 5px 15px !important;
    text-align: end !important;
}

.customer-report-table .fixed-header th div {
    border-bottom: none !important;
    padding: 0 !important;
}

.customer-report-table .fixed-header th {
    border-left: 1px solid #abaaba !important;
    border-right: 1px solid #abaaba !important;
}

.customer-report-table tbody tr td {
    padding: 5px 5px 5px 15px;
    white-space: normal;
    font-size: 12px !important;
    line-height: 1.5 !important;
}

.customer-report-table input::placeholder,
.customer-report-table input {
    /* color: #ABAAAA !important; */
    font-size: 12px !important;
    font-weight: 600 !important;
}

.remove-close::-webkit-search-cancel-button {
    display: none;
    -webkit-appearance: none;
    appearance: none;
}

.customer-report-table thead tr:last-child th {
    padding: 10px 8px 10px 13px !important;
}

/* .customer-report-table thead tr th:last-child , .customer-report-table tbody tr td:last-child{
    position: sticky !important;
    right: 0;
} */
.customer-report-table tbody tr td:first-child {
    color: #4f158c !important;
}

/* .customer-report-table tbody tr td a {
    color: #181c32 !important;
} */

.gap-10px {
    gap: 10px;
}

.mb-10px {
    margin-bottom: 10px;
}

.pagination-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    padding: 10px;
    background: #f8f8f8;
    border-radius: 8px;
}

.items-per-page {
    display: flex;
    align-items: center;
    gap: 10px;
}

.items-per-page select {
    padding: 5px;
    border-radius: 5px;
    border: 1px solid #ccc;
    margin-left: 5px;
    width: 50px;
}

.pagination-controls button {
    margin: 0 5px;
    padding: 5px 10px;
    border: none;
    cursor: pointer;
    border-radius: 5px;
    background: #ddd;
}

.pagination-controls button.active {
    background: #4f158c;
    color: white;
    font-weight: bold;
}

.pagination-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    width: 250px;
}

.columns_label {
    font-weight: 600 !important;
    color: #181c32 !important;
    text-transform: capitalize !important;
}

.actions_button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
}
.actions_button_fully_invoiced {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 3px;
    padding-right: 7px;
}

.transaction-dropdown-menu {
    display: block !important;
}

.actions_items {
    position: absolute;
    top: -190px;
    right: 0;
    display: block;
    z-index: 9999;
    list-style-type: none;
    background: white !important;
    text-align: start;
    padding: 5px;
    width: 200px !important;
}

.fixed-header {
    z-index: 0 !important;
}

.actions_dropdown_item {
    display: block;
    width: 100%;
    padding: 0.25rem 1rem;
    clear: both;
    font-weight: 400;
    color: #181c32;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    cursor: pointer;
}

.offcanvas-body .react-tel-input .country-list {
    position: fixed;
    width: 250px;
}

.margin-svg {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 1;
}

.print-details-container {
    border: 1px solid #9f73ce;
    border-radius: 10px;
    padding: 35px 50px 40px 50px;
}

.print-drop {
    right: 19px;
    z-index: 1;
}

.print-details-container .form-floating-group {
    /* min-width: 285px;
    max-width: 285px; */
}

.print-details .text-secondary {
    color: #9e9e9e !important;
}

.barcode-section {
    border: 1px solid #9f73ce;
    border-radius: 10px;
    padding: 10px 40px 40px 40px;
    width: 100%;
    max-width: fit-content;
}
.barcode-img {
    width: 100%;
    max-width: 185px;
}

.barcode-length65 {
    width: 38mm !important;
    height: 21mm !important;
}
.barcode-length48 {
    width: 48mm !important;
    height: 23mm !important;
}
.barcode-length24 {
    width: 64mm !important;
    height: 34mm !important;
}

.barcode-box {
    row-gap: 30px !important;
    width: min-content !important;
    margin: auto !important;
}
.label-details-table thead th {
    color: white;
    background-color: #4f158c;
    vertical-align: top;
}

.label-details-table .form-check-input {
    border: 1px solid #abaaaa !important;
    width: 17px !important;
    height: 17px !important;
    border-radius: 5px;
}

.label-details-table .form-check-input:checked {
    background-color: #f76600 !important;
}

.label-details-table td,
.label-details-table th {
    border: 1px solid #747685 !important;
    padding: 6px;
}

.label-details-table thead th:last-child,
.label-details-table tbody td:last-child {
    width: 36px;
}

.label-details-table .dropdown-item:hover {
    background-color: #cfb7e9;
}

.label-details-table .barcode-img1 {
    width: 160px;
    height: 26px;
}

.label-details-table .min-w-145px {
    min-width: 145px !important;
}

.print-details-textarea {
    min-height: 124px;
    border: 1px solid #747685 !important;
    border-radius: 7px;
}

.youtube-icons {
    top: 0;
    right: 10px;
    border: 1px solid #9f73ce;
    border-bottom: none !important;
    border-radius: 7px 7px 0px 0px;
    z-index: 1;
}

.print-details .card {
    min-height: calc(100vh - 195px);
}

.bacode-2 {
    opacity: 0.3;
}

.bacode-2 .fs-20 {
    font-size: 20px;
}

.barcode-card {
    border: 1px solid #9c9c9c;
    border-radius: 6px;
    /* padding: 7px 10px; */
    /* width: 100% !important;
    height: 100% !important;
    min-width: 268px;
    min-height: 176px; */
}

.barcode-card-print {
    padding: 1px 1px;
}
.barcode-print-border {
    border: 1px solid #9c9c9c;
    border-radius: 6px;
}
.barcode-card-print-without-border {
    border: none !important;
    border-radius: 6px;
    padding: 1px 1px;
}

.barcode-content-print {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center !important;
}

.barcode-content-print .barcode-number {
    letter-spacing: 0.3px;
}

@media (max-width: 1280px) {
    .barcode-box .barcode-card:first-child::before {
        left: -40px;
    }
}

.barcode-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.barcode_image {
    width: 100%;
}

.barcode_header {
    font-size: 12px;
    font-weight: 700;
    max-width: 98%;
    white-space: wrap;
    text-wrap: wrap;
    /* overflow: hidden; */
    /* text-emphasis: ellipsis; */
}

.barcode_sku {
    font-size: 14px;
    font-weight: 500;
    max-width: 90%;
    white-space: nowrap;
    /* overflow: hidden; */
    text-emphasis: ellipsis;
    margin-bottom: 0px !important;
}

.barcode-content .barcode-number {
    letter-spacing: 0.3px;
}
.barcode-img-2 {
    width: 157px;
    height: 23px;
}

@media (max-width: 576px) {
    .print-details-container {
        padding: 30px 16px;
    }

    .print-details-container .form-floating-group {
        min-width: auto !important;
    }

    .youtube-icons {
        top: -2px;
    }
}

.printview-modal .modal-header {
    padding: 15px 32px;
    box-shadow: 0px 0px 30px 0px #00000040;
    z-index: 2;
}

.printview-modal .barcode-card {
    min-width: 207px !important;
}

.printview-modal .modal-content {
    border-radius: 0 !important;
    height: 85vh;
}

.printview-modal .modal-body {
    padding: 47px 20px 20px 20px;
    /* min-height: 657px;
    max-height: 657px; */
}

.printview-modal .modal-header p {
    font-size: 20px;
}

@media (min-width: 768px) {
    .printview-modal .modal-dialog {
        max-width: 630px;
    }
    .min-w-md-630 {
        max-width: 795px;
        min-width: 630px;
    }
}
@media (min-width: 991px) {
    .printview-modal .modal-dialog {
        max-width: 900px;
    }
    .border-lg-right {
        border-right: 1px solid #dee2e6;
    }
    .min-w-lg-730 {
        min-width: 730px;
    }
}
@media (min-width: 1200px) {
    .min-w-xl-730 {
        min-width: 730px;
    }
    .w-xl-min-content .barcode-section {
        width: 100%;
    }
}
@media (max-width: 1199px) and (min-width: 992px) {
    .w-xl-min-content .barcode-section {
        width: min-content;
    }
}
@media (max-width: 768px) {
    .w-xl-min-content .barcode-section {
        width: min-content;
    }
}

.thermal-page-size-button .btn-check:checked + label {
    background-color: #e7e7f8 !important;
}

.cursor_pointer {
    cursor: pointer !important;
}

.custom-nav-tabs-purchase-ocr {
    position: absolute;
    top: 0;
    right: 20px;

    .nav-item {
        .nav-link {
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 60px;
            padding: 7px;
        }
    }
}

.recurring-master-page {
    padding-left: 17px;
    padding-right: 22px;
    border: 1px solid #4f158c;
    border-radius: 7px;
    margin-top: 16px;
    padding-top: 20px;
    background: white;
}

.recurring-tabs {
    border-bottom: 1px solid #b5b5c3;
}

.search-icon-box {
    border-radius: 7px;
    border: 1px solid #747685;
    max-width: 290px;
    padding: 9px 12px;
}

.search-icon-svg {
    width: 15px;
    height: 15px;
}

.search-icon-box input {
    /* color: #181c32 !important; */
    border: none !important;
    font-size: 13px;
    font-weight: 600;
}

.search-icon-box input:focus {
    outline: none !important;
}

/* recurring table css  */

.recurring-master-main {
    border-radius: 7px;
}

@media (max-width: 1165px) {
    .recurring-master-main .sticky-action-column .recurring-master-table tbody tr td:last-child {
        border-top: 2px solid #abaaba !important;
    }
}
.recurring-master-main .custom-livewire-table .recurring-master-table tbody tr:last-child {
    border-bottom: 0 !important;
}
.recurring-invoice-table
    .recurring-master-main
    .sticky-action-column
    table
    tbody
    tr
    td:nth-child(3) {
    min-width: 249px;
}
@media (max-width: 1165px) and (min-width: 576px) {
    .pending-for-approval-table tbody tr td:nth-last-child(2) {
        border-top: 2px solid #abaaba !important;
    }
}
@media (min-width: 576px) {
    .pending-for-approval-table tbody tr {
        height: 45.5px !important;
    }
    .pending-for-approval-table thead tr th:nth-last-child(2),
    .pending-for-approval-table tbody tr td:nth-last-child(2) {
        position: sticky;
        right: 90px;
    }
    .pending-for-approval-table thead tr th:nth-last-child(2)::after,
    .pending-for-approval-table tbody tr td:nth-last-child(2)::after {
        position: absolute;
        width: 1px;
        height: 100%;
        left: -1px;
        top: 0;
        content: "";
        border-left: 1px solid #abaaba !important;
    }
}
.rejected-invoice-table
    .recurring-master-main
    .sticky-action-column
    table
    tbody
    tr
    td:nth-child(2) {
    min-width: 154px !important;
}

.recurring-master-main .sticky-action-column table tbody tr td:last-child:after,
.recurring-master-main .sticky-action-column table thead tr th:last-child:after {
    border-left: 1px solid #abaaba !important;
}
.recurring-master-table thead tr th:first-child,
.recurring-master-table tbody tr td:first-child {
    border-left: none !important;
}

.recurring-master-table thead tr th:last-child,
.recurring-master-table tbody tr td:last-child {
    border-right: none !important;
}

.flipped.border-box {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #abaaba;
}

.recurring-master-table thead tr:first-child th {
    background-color: #4f158c !important;
    color: white !important;
    font-weight: 700;
    padding: 10px 8px !important;
    /* min-width: 170px !important; */
    font-size: 12px;
    white-space: nowrap;
}

.recurring-master-header {
    min-width: min-content !important;
}
.recurring-master-header-srNo {
    text-align: center !important;
}
.recurring-master-header-accept_reject {
    min-width: 188px !important;
    text-align: center !important;
}
.recurring-master-table tbody td:nth-child(1) {
    text-align: center !important;
}
.recurring-master-table thead th:nth-child(2) {
    min-width: 127px !important;
    word-break: break-all !important;
}
.recurring-master-table thead th:nth-child(4),
.recurring-master-table thead th:nth-child(5) {
    min-width: min-content !important;
}

.recurring-master-table thead tr:first-child th:last-child {
    min-width: 90px !important;
    padding: 10px 8px !important;
    text-align: center !important;
}

.recurring-master-table tbody td:last-child {
    padding: 10px 8px !important;
    text-align: center !important;
}

.recurring-master-table .fixed-header th div {
    border-bottom: none !important;
    padding: 0 !important;
}

.recurring-master-table .fixed-header th {
    border-left: 1px solid #abaaba !important;
    border-right: 1px solid #abaaba !important;
}

.recurring-master-table tbody tr td {
    padding: 5px 8px 5px 8px !important;
    white-space: normal;
    font-size: 12px !important;
    line-height: 1.5 !important;
}

.recurring-master-table input::placeholder,
.recurring-master-table input {
    /* color: #181c32  !important; */
    font-size: 12px !important;
    font-weight: 600 !important;
}

.recurring-master-table thead tr:last-child th {
    padding: 10px 8px !important;
}
/* .recurring-master-table thead tr th:last-child , .recurring-master-table tbody tr td:last-child{
    position: sticky !important;
    right: 0;
} */
.recurring-master-table tbody tr td:first-child {
    color: #4f158c !important;
}

.recurring-master-table tbody tr td a {
    color: #181c32 !important;
}

.pagination-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    padding: 10px;
    background: #f8f8f8;
    border-radius: 8px;
}

.items-per-page {
    display: flex;
    align-items: center;
    gap: 10px;
}
.items-per-page select {
    padding: 5px;
    border-radius: 5px;
    border: 1px solid #ccc;
    margin-left: 5px;
    width: 50px;
}

.pagination-controls button {
    margin: 0 5px;
    padding: 5px 10px;
    border: none;
    cursor: pointer;
    border-radius: 5px;
    background: #ddd;
}

.pagination-controls button.active {
    background: #4f158c;
    color: white;
    font-weight: bold;
}

.pagination-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    width: 250px;
}

.approved {
    background-color: #4bd600;
    border-radius: 7px;
    padding: 8px;
    color: white;
    border: none;
    font-weight: 700;
    font-size: 12px;
}
.rejected {
    background-color: #f50000;
    border-radius: 7px;
    padding: 8px;
    color: white;
    border: none;
    font-weight: 700;
    font-size: 12px;
}

.accept_reject {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.panding_recurring_actions_button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.undo_rejection_button {
    background-color: #f76600;
    border-radius: 7px;
    padding: 8px;
    color: white;
    border: none;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-size: 12px;
    white-space: nowrap;
    margin: 0 auto;
}

.approve_recurring_modal_container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 10px;
    padding-bottom: 20px;
}
.approve_recurring_modal_content {
    display: flex;
    flex-direction: column;
    gap: 5px;
    align-items: center;
    justify-content: center;
    width: 266px;
}
.approve_recurring_modal_img {
    width: 230px;
    height: 220px;
}
.approve_recurring_modal_title {
    color: #e70c0c;
    font-size: 14px;
    font-weight: 500;
}
.approve_recurring_modal_discription {
    font-size: 12px;
    color: #181c32;
    font-weight: 500;
    text-align: center;
}
.approve_recurring_btn_approve {
    box-shadow: 2px 2px 5px 0px #00000040;
    padding: 10px;
    border: 1px solid #dddddd;
    border-radius: 7px;
    background: #ffffff;
    color: #181c32;
}
.approve_recurring_btn_view {
    box-shadow: 2px 2px 5px 0px #00000040;
    padding: 10px;
    border: 1px solid #dddddd;
    border-radius: 7px;
    background: #ffffff;
    color: #181c32;
}
.approve_recurring_btn_cancel {
    box-shadow: 2px 2px 5px 0px #00000040;
    padding: 10px;
    border: 1px solid #747685;
    border-radius: 7px;
    background: #4f158c;
    color: #ffffff;
}
.approve_recurring_modal_btn {
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: center;
}
.approve_recurring_modal_close {
    position: absolute;
    top: 15px;
    right: 20px;
    cursor: pointer;
}

.recurring_Invoice_Details {
    border: 1px solid #4f158c;
    padding: 20px;
    background: #ffffff;
    border-top-left-radius: 7px;
    border-bottom-left-radius: 7px;
    border-bottom-right-radius: 7px;
}

@media (min-width: 1200px) {
    .mr-27px {
        margin-right: -27px !important;
    }
}
.multi-custome .css-ggoss2-placeholder {
    border: 1px solid #e6e6e6 !important;
    background-color: #0000000d !important;
    color: #757782 !important;
}
.multi-custome .css-dcou69 {
    position: static !important;
}
.multi-custome .css-145bw43-menu {
    margin: 5px 0px !important;
}
.multi-custome .css-1wldsz6 {
    padding: 6px !important;
}
.multi-custome .css-qg6fe3-multiValue {
    overflow: hidden !important;
}

.modal-container {
    padding: 20px;
}

/* Info Box */
.info-box {
    background: #f5f8fa;
    padding: 12px;
    border-radius: 5px;
    font-size: 14px;
    display: flex;
    align-items: self-start;
    gap: 5px;
}

.info-content span {
    color: #181c32;
    font-weight: 500;
    font-size: 13px;
}

/* Layout for Input and Dynamic Variables */
.input-group {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

/* Textarea Section */
.input-section {
    flex: 2;
}

.input-label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
    color: #3f4254;
    font-size: 14px;
}

.custom-textarea {
    width: 100%;
    height: 100px;
    padding: 8px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 5px;
    /* resize: none; */
}

/* Dynamic Variables */
.variable-section {
    flex: 1;
}

.variable-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.variable-button {
    background: #fff;
    border: 1px solid #ced4da;
    padding: 5px 10px;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
    transition: 0.2s;
    width: 100%;
    text-align: left;
}

.variable-button:hover {
    background: #d6d8db;
}

/* Example Box */
.example-box {
    margin-top: 15px;
    padding: 10px;
    /* background: #F5F8FA; */
    font-size: 14px;
    color: #3f4254;
    font-weight: 500;
}
.example-box p {
    color: #3f4254;
    font-weight: 600;
}

.example-box code {
    background: #e9ecef;
    padding: 2px 5px;
    border-radius: 4px;
}

/* Footer Buttons */
.footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* .save-button {
    background: #6c5ce7;
    color: white;
    border: none;
    padding: 8px 15px;
    font-size: 14px;
    border-radius: 5px;
    cursor: pointer;
} */

.cancel-button {
    background: #adb5bd;
    color: white;
    border: none;
    padding: 8px 15px;
    font-size: 14px;
    border-radius: 5px;
    cursor: pointer;
}

.cancel-button:hover {
    background: #868e96;
}

.custom-nav-tabs-recurring {
    position: absolute;
    top: 0;
    /* right: 30px; */
    .nav-item {
        .nav-link {
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 60px;
            padding: 7px;
        }
    }
    .arrow-btn {
        width: 35px;
        height: 35px;
        min-width: 35px;
        background-color: #4f158c;
        border: 1px solid #4f158c !important;
        border-radius: 50%;
    }

    .arrow-btn i {
        color: #eff2f5;
        font-size: 16px;
    }

    .arrow-btn:hover {
        background-color: #eff2f5;
        color: #4f158c;
    }

    .arrow-btn:hover i {
        color: #4f158c;
    }

    .arrow-btn.disabled {
        pointer-events: none;
        opacity: 0.6;
    }
}

.min-height-content {
    height: calc(100vh - 250px);
    scrollbar-width: none !important;
}

.min-height-content-1 {
    height: calc(100vh - 184px);
    scrollbar-width: none !important;
}
.min-height-content-2 {
    height: calc(100vh - 217px);
    scrollbar-width: none !important;
}

.customization-modal .form-floating-group > .floating-label-input:focus ~ .form-label {
    background-image: linear-gradient(0deg, #ffffff 46%, #f5f8fa 50%) !important;
    background-color: transparent !important;
}

.customization-modal .form-label {
    background-image: linear-gradient(0deg, #ffffff 46%, #f5f8fa 50%) !important;
}
.customization-modal .form-floating-group:has(input:disabled) .form-label {
    background-image: linear-gradient(0deg, #eff2f5 50%, #f5f8fa 50%) !important;
    background-color: transparent !important;
}

/* vastra */
.vastra-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
}

.vastra-label {
    font-size: 14px;
    font-weight: 500;
    color: #555;
}

.vastra-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    transition: all 0.3s ease-in-out;
}

.vastra-input:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0px 0px 5px rgba(0, 123, 255, 0.5);
}

.vastra-radio-card {
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    background-color: #fafafa;
    margin-bottom: 20px;
}

.vastra-radio label {
    font-size: 14px;
    color: #444;
    margin-left: 6px;
    font-weight: 700;
}

.vastra-button {
    width: fit-content !important;
    padding: 10px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease-in-out;
}

.save-btn {
    background-color: #007bff;
    border: none;
}

.save-btn:hover {
    background-color: #0056b3;
}

.sync-btn {
    background-color: #6c757d;
    border: none;
}

.sync-btn:hover {
    background-color: #565e64;
}

.spinner_size {
    width: 1.5rem !important;
    height: 1.5rem !important;
}
.additional-charges .form-floating-group:has(input:disabled) .form-label {
    background-image: linear-gradient(0deg, #eff2f5 50%, #f5f8fa 50%) !important;
    background-color: transparent !important;
}
.barcodeView2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.barcodeView1 {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.custom_text_header {
    font-size: 16px;
    font-weight: 600;
    columns: #4f158c;
    padding: 1rem 1rem !important;
}

.multi-custome .css-dcou69 {
    min-height: 114px !important;
    align-items: start !important;
}

.multi-custome .css-9jq23d .css-ggoss2-placeholder {
    top: 0 !important;
    left: 0 !important;
}
.barcodeSize48 {
    font-size: 12px;
}
.barcodeHeader64,
.barcodeHeader48,
.barcodesku24,
.barcodeline1_48,
.barcodeline2_48,
.barcodeline4_48 {
    font-size: 10px;
}
.barcodeHeader24,
.barcodeline1_24,
.barcodeline2_24,
.barcodeline4_24 {
    font-size: 12px;
}
.barcodeHeader-label {
    font-size: 12px;
    margin-bottom: 2px !important;
}
.barcodeline1_label {
    font-size: 8px;
    margin-bottom: 0px !important;
}
.barcodeline2_label {
    font-size: 8px;
    margin-bottom: 0px !important;
}
.barcodesku64,
.barcodesku48 {
    font-size: 7px;
}
.barcodeline1_64,
.barcodeline2_64,
.barcodeline4_64 {
    font-size: 8px;
}
.barcodesku-label {
    font-size: 8px;
    margin-bottom: 0px !important;
}
.barcodesku-label34 {
    font-size: 7px;
    margin-bottom: 0px !important;
}
.barcodeline4_label {
    font-size: 8px;
    margin-bottom: 0px !important;
}
.print-details-container .h-16px,
.barcode-content-print .h-16px {
    height: 16px !important;
}
.print-details-container .h-18px,
.barcode-content-print .h-18px {
    height: 18px !important;
}
.print-details-container .h-20px,
.barcode-content-print .h-20px {
    height: 20px !important;
}
.print-details-container .h-22px,
.barcode-content-print .h-22px {
    height: 22px !important;
}
.print-details-container .h-25px,
.barcode-content-print .h-25px {
    height: 25px !important;
}
.dynamic-description-header {
    font-weight: 600 !important;
}
.pagination-container-Recurring {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    padding: 10px 0;
    border-radius: 8px;
}
.recurring-tabs .nav-item .nav-link {
    font-size: 14px;
    font-weight: 600;
    padding: 8px 15px !important;
}
.custom_text_btn {
    width: fit-content !important;
}

.custom_text_modal .modal-dialog .modal-content {
    width: 430px !important;
}

.print-barcode-container {
    display: flex;
    flex-direction: column;
    /* align-items: center; */
    /* justify-content: center; */
    padding-left: 2px;
    padding-right: 2px;
    /* padding-top: 3px; */
    /* padding-bottom: 10px; */
}

.barcodeContainer64 {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    /* grid-column-gap: 5px;
    grid-row-gap: 6.5px !important; */
}
.barcodeContainer48 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    /* grid-column-gap: 5px;
    grid-row-gap: 6px !important; */
}
.barcodeContainer24 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* grid-column-gap: 5px;
    grid-row-gap: 11px !important; */
}

.barcodeContainerlabel13 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 5px;
    grid-row-gap: 16.5px;
}

.barcodeContainerlabel13Print {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    /* grid-column-gap: 5px;
    grid-row-gap: 2px !important; */
    margin-top: 2px;
}

.barcodeContainerlabel24 {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    grid-column-gap: 5px;
    grid-row-gap: 16.5px;
}

.barcodeContainerlabel24Print {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    /* grid-column-gap: 5px;
    grid-row-gap: 2.5px !important; */
    margin-top: 2px;
}

.barcodeContainerlabel5 {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    grid-column-gap: 5px;
    grid-row-gap: 17px;
}

.barcodeContainerlabel5Print {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    /* grid-column-gap: 5px;
    grid-row-gap: 4.5px !important; */
    margin-top: 2px;
}

.barcodeNotExist {
    padding-top: 6rem;
    padding-bottom: 6rem;
}
.barcode-details {
    max-height: 620px;
}
.barcode-details .label-details-table thead {
    position: sticky;
    top: 0;
    z-index: 9;
}
.label-details-table thead tr th {
    position: relative;
}
.label-details-table thead tr th::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 1px;
    background-color: gray;
    bottom: -1px;
    left: 0;
    z-index: 1;
}
.barcode_lines {
    gap: 1px;
}
.print-gap-5 {
    row-gap: 20px;
}
.bill-wise-table thead th {
    color: white;
    background-color: #4f158c;
}

.bill-wise-table .form-floating-group .form-label {
    max-width: calc(100% - 20px);
    font-size: 13.5px;
}

.bill-wise-table .form-check-input {
    width: 20px !important;
    height: 20px !important;
    border-radius: 5px;
    margin: auto;
    display: flex;
}

.bill-wise-table td,
.bill-wise-table th {
    border: 1px solid #dde0e4 !important;
    padding: 12px 10px 10px 10px;
}

.bill-wise-table .search-input::placeholder {
    color: #73757d;
}

.bill-wise-modal .upload-document.form-label {
    background-image: linear-gradient(0deg, #f5f8fa 50%, #ffffff 50%) !important;
}
.row-gap-15px {
    row-gap: 15px;
}
.overflowX-auto {
    overflow-x: auto;
    overflow-y: hidden;
}

.view-recurring-invoice-modal .modal-content {
    max-height: calc(100vh - 3.5rem);
    min-width: 850px !important;
}
.view-recurring-invoice-modal .modal-body {
    height: 94vh;
    overflow: auto;
}

.barcodeBreak {
    page-break-after: always;
}
.input-divider {
    position: absolute;
    right: 34px;
    top: 4px;
    font-size: 16px;
    color: #6f6f6f;
}

@media print {
    .barcodeBreak {
        page-break-after: always;
    }

    .print-barcode-container * {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    .print-barcode-container {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .print-barcode-container img {
        image-rendering: auto !important;
    }

    .print-barcode-container canvas,
    .print-barcode-container img {
        filter: none !important;
        image-rendering: pixelated !important;
    }
}

.ocr-main {
    /* border: 1px solid #4f158c; */
    /* padding: 20px; */
    border-radius: 7px;
}

.ocr_filter_wrapper {
    width: 60%;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.ocr_notes {
    width: 50%;
}

.ocr_date_filter {
    display: flex;
    align-items: center;
    gap: 16px;
}

.filter_main_container {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.ocr_seelct_bank {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 15%;
    align-items: self-start;
}

.bank_label {
    color: #181c32 !important;
    font-size: 16px;
    font-weight: 600;
}

.ocr_bulk_actions {
    font-size: 20px;
    color: #181c32;
    font-weight: 600;
}

.recurring_note_btn {
    padding: 4px 14px !important;
    margin-left: 8px !important;
    box-shadow: none !important;
    color: #ffffff !important;
    background-color: #551a94 !important;
}
.recurring_note_btn:hover {
    color: #ffffff !important;
}
.recurring_note_btn:focus {
    box-shadow: none !important;
}
.vastra-Other-title {
    color: #4f158c;
    font-size: 18px;
    font-weight: 500;
}
.vastra-delivery-title {
    color: #181c32;
    font-size: 18px;
    font-weight: 500;
}

.vastra-report-main {
    border-radius: 7px;
}

.vastra-details .card {
    min-height: calc(100vh - 213px);
}

.vastra-details-container {
    border: 1px solid #9f73ce;
    border-radius: 10px;
    padding: 35px 50px 40px 50px;
    min-height: calc(100vh - 120px);
}

@media (min-width: 992px) {
    .pt-lg-100px {
        padding-top: 100px;
    }
}
@media (min-width: 768px) {
    .pt-md-100px {
        padding-top: 100px;
    }
}
@media (max-width: 767px) {
    .pt-md-110px {
        padding-top: 110px;
    }
}

.max-w-285px {
    max-width: 285px;
}
.max-w-795px {
    max-width: 795px;
}

.save_and_sync_spinner {
    width: 16px !important;
    height: 16px !important;
}
.vastra-check-table-transactions {
    max-height: 200px;
    overflow: auto;
}
.vastra-check-table-transactions table thead th {
    background-color: white;
    position: sticky;
    top: 0;
    z-index: 2;
}

.actions_button_checkbox {
    /* width: fit-content !important; */
    display: flex;
    align-items: center;
    justify-content: center;
}
.actions_button_checkbox_header {
    margin-right: 10px !important;
    display: flex;
    align-items: center;
    justify-content: center;
}
.p-5x td {
    padding: 5px !important;
}

.statement-review-table .customer-report-table .datepicker-container .custom-date-input {
    border: 0 !important;
    box-shadow: none !important;
}
.statement-review-table .customer-report-table thead tr th:first-child,
.statement-review-table .customer-report-table tbody tr td:first-child {
    width: 50px !important;
    min-width: 50px !important;
}
.statement-review-table .customer-report-table thead tr:first-child th:last-child {
    text-align: start !important;
    max-width: 120px;
}
.statement-review-table .customer-report-table tbody tr:first-child td:last-child {
    width: 120px;
}
.statement-review-table .custom-group-text {
    border: none !important;
    height: 32px !important;
    top: 1px;
    right: 1px !important;
}
.statement-review-table .customer-report-table tbody tr td,
.statement-review-table .customer-report-table thead tr:first-child th,
.statement-review-table .customer-report-table thead tr:last-child th {
    padding: 6px 8px !important;
}
.statement-review-table .customer-report-table thead th:nth-child(4),
.statement-review-table .customer-report-table thead th:nth-child(5) {
    min-width: min-content !important;
}
.statement-review-table .customer-report-table tbody tr td:last-child {
    z-index: 1;
}

.statement-review-table .customer-report-table input::placeholder,
.statement-review-table .customer-report-table input {
    /* color: #ABAAAA !important; */
    font-size: 14px !important;
    font-weight: 600 !important;
}
.statement-review-table .customer-report-table thead tr:first-child th {
    font-size: 15px;
    padding: 10px 8px !important;
}

.statement-review-table .border-box,
.settle-transaction-modal .border-box {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #abaaba;
}
.outstanding-transaction-table thead th {
    color: white;
    background-color: #4f158c;
    border-top: 0 !important;
}
.outstanding-transaction-table thead th,
.outstanding-transaction-table tbody td,
.outstanding-transaction-table tfoot td {
    border-left: 1px solid #abaaba;
    border-top: 1px solid #abaaba;
    padding: 6px 8px !important;
}
.outstanding-transaction-table tbody td {
    padding: 12px 8px !important;
    vertical-align: middle;
}
.outstanding-transaction-table thead th:first-child,
.outstanding-transaction-table tbody td:first-child,
.outstanding-transaction-table tfoot td:first-child {
    border-left: none;
}

.settle-transaction-modal .modal-dialog {
    max-width: 1779px;
}
.settle-transaction-modal .form-floating-group .form-label {
    max-width: calc(100% - 25px);
}
.page-wrapper {
    min-height: calc(100vh - 210px);
}
.ellipsis-pagination {
    display: flex;
    align-items: center;
    padding: 0 5px;
}

.vastra-page {
    border: 1px solid #4f158c;
    border-radius: 7px;
    background: white;
}
.row-gap-12 {
    row-gap: 12px;
}

@media (max-width: 991px) and (min-width: 576px) {
    .printview-modal {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }
}
@media (max-width: 991px) {
    .aside {
        position: fixed !important;
    }
    .aside-logo {
        padding: 10px !important;
    }
    .search-text {
        top: 0px !important;
        height: 35px !important;
        width: 35px !important;
    }
    .select-box {
        margin: 0 !important;
        width: 230px !important;
    }
    .bacode-2 .fs-20 {
        font-size: 18px;
    }

    .print-details-container {
        padding: 30px 20px;
    }
    .barcode-section {
        width: max-content;
        padding: 10px 25px 40px 35px;
    }
    .px-20px {
        padding-left: 12px !important;
        padding-right: 12px !important;
    }
    .ml-20px,
    .mr-20px {
        margin-left: -12px !important;
        margin-right: -12px !important;
    }
    .custom-nav-tabs {
        right: 15px;
    }
    .demo-book-img {
        padding: 12px 30px !important;
    }
    .custom-nav-tabs-purchase-ocr {
        right: 15px;
    }
    /* .custom-nav-tabs-recurring {
        right: 15px;
    } */
    .pt-lg-110px {
        padding-top: 110px;
    }
    .custom-min-height {
        min-height: calc(100vh - 115px);
    }
    .printview-modal .modal-body {
        padding: 27px 20px 20px 20px;
    }
    .printview-modal .modal-header {
        padding: 15px 20px;
    }
    .printview-modal .modal-header .btn-primary {
        font-size: 0.925rem;
        padding: calc(0.55rem + 1px) calc(1.25rem + 1px) !important;
        border-radius: 0.325rem;
    }
    .printview-modal .modal-header .btn-primary img {
        width: 18px;
    }
    .printview-modal .modal-header p {
        font-size: 18px;
    }
}
@media (max-width: 768px) {
    .search-text {
        height: 32px !important;
        width: 32px !important;
    }
    .printview-modal .modal-header .btn-close {
        padding: 6px !important;
        margin: 0 !important;
    }
}

@media (max-width: 575px) {
    .nav-tabs {
        gap: 8px;
    }
    .nav-tabs .nav-item .nav-link {
        min-width: 85px;
        height: 36px;
    }
    .custom-nav-tabs .nav-item .nav-link {
        min-width: 50px;
    }
    .content-wrapper {
        padding: 15px;
    }

    .additional-charges {
        padding: 15px;
    }
    .configuration-btn {
        min-width: 50px;
        height: 36px;
    }
    .custom-offcanvas-modal {
        right: -98%;
        width: 98%;
    }
    .offcanvas-header,
    .offcanvas-body {
        padding: 16px;
    }

    .offcanvas-footer .fixed-buttons {
        padding: 8px 16px;
    }
    .printview-modal .modal-header p {
        font-size: 16px;
    }
    .printview-modal .modal-header {
        padding: 15px;
    }
    .custom-nav-tabs-purchase-ocr .nav-item .nav-link {
        min-width: 50px;
    }
    .custom-nav-tabs-recurring .nav-item .nav-link {
        min-width: 50px;
    }
    .sale-configuration-modal .offcanvas-header,
    .sale-configuration-modal .offcanvas-body {
        padding: 12px;
    }
    .sale-configuration-modal .offcanvas-footer .fixed-buttons {
        padding: 15px 12px;
    }
    .desc-box {
        padding: 20px 15px;
    }
}

.vastra_challan_number {
    color: #4f158c !important;
}

.vastra_table_Data {
    color: #181c32 !important;
}

.vastra-tabs {
    border-bottom: 1px solid #9f73ce;
}
.vastra-border-top tbody tr td:last-child {
    border-top: 2px solid #abaaba !important;
}
.vastra-border-top tbody tr td:last-child::after,
.vastra-border-top thead tr th:last-child::after {
    border-left: 1px solid #abaaba !important;
}
.vastra-radio .form-check-input {
    margin-top: 3px;
}
.vastra-radio {
    margin-bottom: 4px !important;
}

.third_party_img {
    width: fit-content !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px;
    flex-wrap: wrap !important;
}
.flex-1 {
    flex: 1;
}

.recurring-height-132px .css-1w7iqlm {
    max-height: 126px;
    overflow: auto;
}

.print-barcode-setting-modal {
    padding: 0 !important;
}
.display-block {
    display: block;
}

.top-and-least-report-main {
    border: 1px solid #4f158c;
    padding: 20px;
    border-radius: 7px;
}

.top-and-least-report-table td,
.top-and-least-report-table th {
    border: 1px solid #abaaba !important;
    background-color: white !important;
    vertical-align: middle !important;
}

.top-and-least-report-table thead tr th {
    border-top: none !important;
    border-bottom: none !important;
    position: relative;
}

.top-and-least-report-table thead tr th:first-child,
.top-and-least-report-table tbody tr td:first-child {
    border-left: none !important;
}

.top-and-least-report-table thead tr th:last-child,
.top-and-least-report-table tbody tr td:last-child {
    border-right: none !important;
}

.top-and-least-report-table thead tr:first-child th {
    background-color: #4f158c !important;
    color: white !important;
    font-weight: 700;
    padding: 10px 5px 10px 15px !important;
    /* min-width: 170px !important; */
    font-size: 12px;
    white-space: nowrap;
}

.top-and-least-report-table thead th:nth-child(4),
.top-and-least-report-table thead th:nth-child(5) {
    min-width: 220px !important;
}

.top-and-least-report-table .fixed-header th div {
    border-bottom: none !important;
    padding: 0 !important;
}

.top-and-least-report-table .fixed-header th {
    border-left: 1px solid #abaaba !important;
    border-right: 1px solid #abaaba !important;
}

.top-and-least-report-table tbody tr td {
    padding: 5px 5px 5px 15px;
    white-space: normal;
    font-size: 12px !important;
    line-height: 1.5 !important;
}

.top-and-least-report-table input::placeholder,
.top-and-least-report-table input {
    /* color: #ABAAAA !important; */
    font-size: 12px !important;
    font-weight: 600 !important;
}

.top-and-least-report-table thead tr:last-child th {
    padding: 10px 8px 10px 13px !important;
}

.top-and-least-report-table tbody tr td:first-child {
    /* color: #4f158c !important; */
    padding: 10px 8px 10px 13px !important;
}

.w-65px {
    width: 65px !important;
}
.w-300px {
    width: 300px !important;
}
.w-200px {
    width: 200px !important;
}
.color-171c32 {
    color: #171c32 !important ;
}

.custom-date-range-picker {
    position: relative;
}

.range-display .form-control {
    background: #fff !important;
    cursor: pointer;
}

.calendar-container {
    position: absolute;
    z-index: 1000;
    background: #fff;
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 8px;
    right: 0;
}
.custom-date-range-picker .dropdown-item.active,
.custom-date-range-picker .dropdown-item.active:hover {
    color: #ffffff !important;
    background-color: #4f158c !important;
}
.custom-date-range-picker .dropdown-item:hover,
.custom-date-range-picker .dropdown-item:focus,
.custom-date-range-picker .dropdown-item:active {
    color: #4f158c !important;
    background-color: #f7efff !important;
}
.custom-date-range-picker .dropdown-item {
    border-bottom: 0 !important;
}

.add_payment_table_title {
    height: 250px !important;
    overflow: auto;
}

.default_title {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.sticky-add-payment-modal thead th {
    position: sticky;
    top: 0;
    z-index: 99;
    background-color: white;
}
.swift-code-btn {
    width: fit-content !important;
}

.W-fit {
    width: fit-content !important;
}

.w-140px {
    width: 140px !important;
}

.tab-title-with-badge {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    width: fit-content;
}

.badge-count {
    color: #e70c0c;
    border-radius: 50%;
    font-size: 12px;
    /* padding: 4px 6px; */
    margin-left: 4px;
    line-height: 1;
    font-weight: bold;
    border: 1px solid #cc0000;
    position: absolute;
    top: -20px;
    right: -26px;
    background: white;
    top: -20px;
    right: -26px;
    background: white;
    min-height: 26px;
    min-width: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
}

.get-started-recurring-btn {
    text-transform: capitalize;
    font-size: 12px;
    font-weight: 700;
    border-radius: 15px;
    padding: 5px 13px !important;
    color: white;
}

.get-started-recurring-btn:hover {
    color: white !important;
}

.gradient-recurring-btn {
    background-image: linear-gradient(to right, #d65907, #ffbd8d);
}

.w-190px {
    width: 190px !important;
}
.top-and-least-report-table thead tr th:nth-child(2) {
    min-width: 350px !important;
}
.top-and-least-report-table thead tr th:nth-child(3),
.top-and-least-report-table thead tr th:nth-child(4) {
    min-width: 170px !important;
}

.w-250px {
    width: 250px !important;
}
.fs-11 {
    font-size: 11px;
}
.low-report-table thead th {
    background-color: #4f158c !important;
    color: white !important;
    padding: 10px !important;
    font-size: 14px;
    white-space: nowrap;
    vertical-align: top;
}
.low-report-table thead .search-input th {
    background-color: white !important;
}
.low-report-table tr:first-child,
.low-report-table th:first-child,
.low-report-table td:first-child {
    padding-left: 10px !important;
}
.low-report-table td:last-child {
    padding-right: 10px !important;
}
.low-report-table thead th,
.low-report-table tbody td {
    border: 1px solid #b5b5c3 !important;
}
.low-report-table tbody tr:nth-child(even) {
    background-color: #ffffff;
}
.low-report-table tbody tr:nth-child(odd) {
    background-color: #ede8f3;
}
.low-report-table tbody tr td:last-child {
    background-color: #ede8f3;
    text-align: center;
}
.low-report-table thead tr th:first-child,
.low-report-table tbody tr td:first-child {
    border-left: 0 !important;
}
.low-report-table thead tr th:last-child,
.low-report-table tbody tr td:last-child {
    border-right: 0 !important;
}
.low-report-table tbody tr td:nth-child(5),
.low-report-table tbody tr td:nth-child(6),
.low-report-table tbody tr td:nth-child(7),
.low-report-table tbody tr td:nth-child(8) {
    text-align: center !important;
}
.low-report-table tbody tr td:nth-last-child(2) {
    text-align: end !important;
}
.header-fixed.modal-open .aside {
    z-index: 103 !important;
}
.aside {
    z-index: 999999999999 !important;
}
.driver-active .aside {
    z-index: 9999 !important;
}
.driver-end-overlay {
    z-index: 9999999999 !important;
}
/* body > div:last-of-type {
   font-size:12px;
  } */

.reorder_level {
    font-weight: 600;
    line-height: 1.2;
    font-size: 16px;
}
.swiper-slider-arrow-custome .swiper-button-prev:after {
    font-size: 18px !important;
}
.swiper-slider-arrow-custome .swiper-button-next::after {
    font-size: 18px !important;
}
.swiper-slider-arrow-custome .swiper-button-next,
.swiper-slider-arrow-custome .swiper-button-prev {
    width: 40px;
    height: 40px;
    background-color: #370d64 !important;
    border: 1px solid #370d64 !important;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ffffff !important;
}
@media (max-width: 1320px) {
    .invoice-modal .offcanvas-body .invoice-slider,
    .invoice-modal .offcanvas-body .invoice-full-slider {
        margin: 0 50px;
    }
    .swiper-slider-arrow-custome .swiper-button-prev:after {
        font-size: 14px !important;
    }
    .swiper-slider-arrow-custome .swiper-button-next::after {
        font-size: 14px !important;
    }
    .swiper-slider-arrow-custome .swiper-button-next,
    .swiper-slider-arrow-custome .swiper-button-prev {
        width: 35px;
        height: 35px;
    }
}
@media (max-width: 768px) {
    .setting-modal-print-height .offcanvas-body {
        max-height: calc(100vh - 186px);
        min-height: calc(100vh - 186px);
    }
}
@media (max-width: 576px) {
    .setting-modal-print-height .offcanvas-body {
        max-height: auto;
        min-height: auto;
    }
    .setting-modal-print-height .modal-dialog {
        display: flex;
        align-items: center;
        min-height: calc(100% - 1rem);
        border-radius: 20px;
        margin: auto !important;
    }
    .setting-modal-print-height .modal-dialog .modal-content {
        height: 100%;
        overflow: hidden;
    }
    .setting-modal-print-height .invoice-full-slider img {
        height: 180px !important;
    }
    .invoice-modal .offcanvas-body .invoice-template,
    .invoice-modal .offcanvas-body .invoice-full-slider {
        margin: 0px 0px 50px 0px;
    }
    .swiper-slider-arrow-custome .swiper-button-next,
    .swiper-slider-arrow-custome .swiper-button-prev {
        bottom: 0px;
        top: auto;
    }
    .swiper-slider-arrow-custome .swiper-button-prev {
        right: 53%;
        left: auto;
    }
    .swiper-slider-arrow-custome .swiper-button-next {
        right: 43%;
    }
}
@media (max-width: 425px) {
    .swiper-slider-arrow-custome .swiper-button-next {
        right: 40%;
    }
}
@media (max-width: 375px) {
    .swiper-slider-arrow-custome .swiper-button-next {
        right: 37%;
    }
}
@media (max-width: 320px) {
    .swiper-slider-arrow-custome .swiper-button-next {
        right: 34%;
    }
}
.payment-key-main .input-group div,
.payment-key .input-group div,
.track-key .input-group div,
.business-glance .input-group div {
    font-size: 12px;
}
.barcode-number-input-size-small input {
    border-radius: 4px !important;
    font-size: 12px !important;
    padding: 0px 4px !important;
    margin-top: 5px !important;
}

.cess_rate_details {
    display: flex;
    align-items: center;
}
.cess_rate_table {
    border-radius: 10px;
    padding: 18px 20px;
    margin-top: 20px;
}

.cess_rate_border {
    border: 1px solid #dde0e4 !important;
}

.cess_rate_border th,
.cess_rate_border td {
    border: 1px solid #dde0e4;
    padding: 10px 10px !important;
}
.cess_rate_actions_button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.sticky-add-cess-rate-modal thead th {
    position: sticky;
    top: 0;
    z-index: 99;
    background-color: white;
}

.party_img {
    height: 125px !important;
    width: 160px !important;
    border-radius: 10px;
}
.min-h-100vh-fixed-button {
    min-height: calc(100vh - 202px);
}
@media (max-width: 991px) {
    .min-h-100vh-fixed-button {
        min-height: calc(100vh - 196px);
    }
}
@media (max-width: 576px) {
    .min-h-100vh-fixed-button {
        min-height: calc(100vh - 192px);
    }
}
.min-h-100vh-fixed-button {
    .print-settings-container {
        min-height: inherit;
    }
}

.general-settings-container {
    display: flex !important;
    flex-direction: column !important;
    /* align-items: center !important; */
    gap: 10px !important;
    justify-content: space-between !important;
    width: 400px !important;
}

.other-general-settings {
    color: #4f158c !important;
    font-size: 18px !important;
}
.w-150px {
    width: 150px !important;
}

.fs-16px {
    font-size: 16px !important;
}
.barcode-image-height {
    min-height: 20px !important;
    max-height: 30px !important;
    margin-bottom: 2px !important;
}

.delete-icon-modal {
    width: 40px;
    height: 40px;
    right: 0;
    top: 0;
    z-index: 1;
    position: sticky;
    border-radius: 0 !important;
}
.w-input-300px {
    width: 300px;
}
.custome-field-modal .modal-dialog {
    max-width: 1392px !important;
}
.custom-field-table-modal {
    padding: 8px;
    background: #f5f8fa;
    border-radius: 10px;
}
.custom-table-field-test-serial-modal .close-btn {
width:24px !important;
height:24px !important;

}
.custom-table-field-test-serial-modal .close-btn svg {
    width:20px !important;
    height:20px !important;
}
.custom-field-table-modal table thead tr:first-child th {
    background-color: #4f158c !important;
    color: white !important;
    font-size: 12px;
}
.custom-field-table-modal table th,
.custom-field-table-modal table td {
    border: 1px solid #b5b5c3 !important;
    padding: 2px 10px !important;
    min-width: 100px;
}
.custom-field-table-modal table th:first-child,
.custom-field-table-modal table td:first-child {
    min-width: 50px !important;
       padding: 2px 10px !important;
}
.custom-field-table-modal table th .search-box {
    width: 100%;
    padding: 5px 5px 5px 12px;
    background: #ffffff;
    border: 1px solid #8e92a2 !important;
    border-radius: 5px;
}
.custom-field-table-modal table th .search-box::placeholder {
    color: #747685 !important;
}
.custom-field-table-modal table tbody .check-box {
    width: 20px !important;
    height: 20px !important;
}
@media (min-width: 576px) {
    .custome-field-modal .modal-dialog {
        margin: 1.75rem 10px;
    }
}
@media (min-width: 1410px) {
    .custome-field-modal .modal-dialog {
        margin: 1.75rem auto;
    }
}
.h-78px-calculation-input {
    height: 78px !important;
    margin-bottom: 10px !important;
}
.calculation_button-all table td {
    padding: 5px !important;
}
.calculation_button-all table td button {
    width: 45px;
    height: 45px;
    border: 1px solid #4f158c !important;
    text-align: center;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 10px;
    color: #181c32 !important;
}
.calculation_result {
    height: 100%;
    background: white;
    border-right: 1px solid #9f73ce;
}
.calculator-input {
    position: relative;
}
.calculator-input i {
    top: 0;
    right: 10px;
    bottom: 0;
    position: absolute;
    display: flex;
    align-items: center;
}
.gap-row-16px {
    row-gap: 16px;
}
.h-33px {
    height: 33px !important;
    cursor: not-allowed !important;
}
.cursor_not_allowed {
    cursor: not-allowed !important;
}

.wp-preview-container {
    background-color: #e5ddd5;
    padding: 20px;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    display: flex;
    justify-content: center;
}

.wp-message-card {
    background-color: #ffffff;
    border-radius: 8px;
    width: 360px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    font-size: 14px;
}

.wp-document-preview {
    background-color: #cccccc;
    height: 120px;
    border-radius: 5px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wp-doc-icon {
    font-size: 30px;
}

.wp-note {
    margin-top: 15px;
}

.min-h-100vh-fixed-button-11za {
    min-height: calc(100vh - 250px);
}
@media (max-width: 991px) {
    .min-h-100vh-fixed-button-11za {
        min-height: calc(100vh - 196px);
    }
}
@media (max-width: 576px) {
    .min-h-100vh-fixed-button-11za {
        min-height: calc(100vh - 192px);
    }
}
.one-one-za-table-ui thead tr th,
.one-one-za-table-ui tbody tr td {
    border: 1px solid #e4e4e4 !important;
    padding: 8px 12px !important;
}
.one-one-za-table-ui tbody tr:last-child td {
    border-bottom: 1px solid #e4e4e4 !important;
}
.one-one-za-table-ui thead tr th:last-child,
.one-one-za-table-ui tbody tr td:last-child {
    border-right: none !important;
}
.one-one-za-table-ui thead tr th:first-child,
.one-one-za-table-ui tbody tr td:first-child {
    border-left: none !important;
}

.third_party_title {
    font-size: 12px;
    font-weight: 500;
    color: #181c32;
}

.third_party_description {
    font-size: 10px;
    font-weight: 500;
    color: #455a64;
}
.button-disabled {
    pointer-events: none;
    opacity: 0.6;
    box-shadow: none !important;
}
.third-party-img {
    border: 1px solid #9f73ce;
    border-radius: 10px;
    padding: 20px;
    max-width: 295px;
    min-width: 295px;
    cursor: pointer;
}

.cess_rate_table_container {
    height: 300px;
    overflow: auto;
}
.cess_rate_border thead th,
.cess_rate_border,
.cess_rate_border tbody tr:first-child td {
    border-top: none !important;
}
.cess_rate_border thead th {
    border-bottom: none !important;
}
.cess_rate_border thead th::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 1px;
    top: 0px;
    left: 0;
    background-color: #dde0e4;
}
.cess_rate_border thead th::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 1px;
    bottom: 0px;
    left: 0;
    background-color: #dde0e4;
}
.cess_rate-modal .modal-dialog {
    max-width: 600px !important;
}

.barcode_add_size_btn {
    right: -111px;
    bottom: -2px;
}

.pr-7px {
    padding-right: 7px !important;
}
.barcode-height-span {
    position: absolute;
    transform: rotate(270deg);
    font-size: 10px;
    left: -38px;
    color: #4f158c;
}
.barcode-height-bottom {
    font-size: 10px;
    color: #4f158c;
    bottom: -30px;
    left: 0;
    right: 0;
    text-align: center;
    position: absolute;
}
.barcode-details-heading {
    position: relative !important;
    width: fit-content !important;
}
.barcode-left-line {
    position: absolute;
    background-color: #4f158c;
    width: 1px;
    height: 100%;
    left: -10px;
}
.barcode-left-line::after {
    position: absolute;
    content: "";
    background-color: #4f158c;
    width: 10px;
    height: 1px;
    left: -5px;
    top: -1px;
}
.barcode-left-line::before {
    position: absolute;
    content: "";
    background-color: #4f158c;
    width: 10px;
    height: 1px;
    left: -5px;
    bottom: -1px;
}
.barcode-bottom-line {
    position: absolute;
    background-color: #4f158c;
    width: 100%;
    height: 1px;
    bottom: -10px;
    left: 0;
    right: 0;
    margin: auto;
}
.barcode-bottom-line::after {
    position: absolute;
    content: "";
    background-color: #4f158c;
    width: 1px;
    height: 10px;
    top: -5px;
    left: -1px;
}
.barcode-bottom-line::before {
    position: absolute;
    content: "";
    background-color: #4f158c;
    width: 1px;
    height: 10px;
    top: -5px;
    right: -1px;
}
.custom-table-field-test-serial-modal  .page-item .page-link {
        width: 26px !important;
    height: 26px !important;
    min-width: 26px !important;
}
.custom-table-field-test-serial table thead tr th , .custom-table-field-test-serial table tbody tr td{
    background-color: white !important;
}
.custom-table-field-test-serial table tbody tr td .form-control {
    border: 1px solid transparent !important;
    height: 22px !important;
    font-size: 11px !important;
    padding: 3px 6px !important;
}
.custom-table-field-test-serial table tbody tr td .form-check-input {
    border: 1px solid transparent !important;
      height: 16px !important;
    width: 16px !important;
    font-size: 11px !important;
    padding: 3px 6px !important;
    margin-top: 2px !important;
}

.custom-table-field-test-serial table tbody tr td input:hover{
    border: 1px solid #4F158C !important;
}
.custom-table-field-test-serial table tbody tr td:first-child input{
    border: 1px solid rgba(0, 0, 0, 0.25) !important;
}
/* .custom-table-field-test-serial table tbody tr td:nth-child(2) , .custom-table-field-test-serial table tbody tr td:first-child{
    background-color: transparent !important;
} */
.custom-table-field-test-serial table thead tr th{
    white-space: nowrap !important;
        padding: 4px 10px !important;
}
.custom-table-field-test-serial table tbody tr td:first-child , .custom-table-field-test-serial table thead tr th:first-child{
    min-width: 30px !important;
    width:30px !important;
}
.custom-table-field-test-serial .w-30px {
    min-width: 30px !important;
    width:30px !important;
}
/* .custom-table-field-test-serial table tbody tr td:nth-child(2) ,.custom-table-field-test-serial table thead tr th:nth-child(2) {
    min-width: 60px !important;
    max-width:60px !important;
    width:60px !important;
} */
.custom-table-field-test-serial table tbody tr td input::placeholder{
    color: gray !important;
}
@media (min-width:991px) {
    .custom-table-field-test-serial-modal .modal-dialog{
    width: fit-content !important;
    max-width: fit-content !important;
    min-width: 400px !important;
}
}

.react-input-disabled input {
    background-color: #eff2f5 !important;
    border-radius: 8px !important;
    border: 1px solid #eff1f3 !important;
}
.react-input-disabled input:hover {
    border: 1px solid #eff1f3 !important;
}
.react-select-disabled {
    background-color: #eff2f5 !important;
    border-radius: 8px !important;
    border: 1px solid #eff1f3 !important;
}
.react-select-disabled:hover {
    border: 1px solid #eff1f3 !important;
}
.min-width-310px {
    min-width: 310px !important;
    max-width: 310px !important;
}

.react-input-disabled input {
    background-color: #eff2f5 !important;
    border-radius: 8px !important;
    border: 1px solid #eff1f3 !important;
}
.react-input-disabled input:hover {
    border: 1px solid #eff1f3 !important;
}
.react-select-disabled {
    background-color: #eff2f5 !important;
    border-radius: 8px !important;
    border: 1px solid #eff1f3 !important;
}
.react-select-disabled:hover {
    border: 1px solid #eff1f3 !important;
}

.payment_mode_form {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}
.payment_mode_table {
    border: 1px solid #9f73ce;
    border-radius: 10px;
    padding: 18px 20px;
    margin-top: 20px;
}

.payment_mode_border {
    border: 1px solid #dde0e4;
}

.payment_mode_border th,
.payment_mode_border td {
    border: 1px solid #dde0e4;
    padding: 10px 10px !important;
}
.payment_mode_actions_button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.payment_mode_table_body {
    height: 300px !important;
    overflow: auto;
}

.payment_mode_padding {
    padding: 18px 20px;
    border: 1px solid #9f73ce;
    border-radius: 10px;
}
.payment_mode_table {
    border-top: 1px solid #dde0e4 !important;
    border-bottom: 1px solid #dde0e4 !important;
}
.payment_mode_padding .payment_mode_border thead tr th {
    border-top: 0 !important;
    border-bottom: 0 !important;
}
.payment_mode_padding .payment_mode_border thead tr th::after {
    position: absolute;
    content: "";
    top: 100%;
    left: 0;
    width: 100%;
    border-top: 1px solid #dde0e4;
    height: 100%;
    z-index: 1;
}

.sticky_payment_modal thead th {
    position: sticky;
    top: 0;
    z-index: 99;
    background-color: white;
}

.h-34px {
    height: 34px !important;
}

.payment_mode_form {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}
.payment_mode_table {
    border: 1px solid #9f73ce;
    border-radius: 10px;
    padding: 18px 20px;
    margin-top: 20px;
}

.payment_mode_border {
    border: 1px solid #dde0e4;
}

.payment_mode_border th,
.payment_mode_border td {
    border: 1px solid #dde0e4;
    padding: 10px 10px !important;
}
.payment_mode_actions_button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.payment_mode_table_body {
    height: 300px !important;
    overflow: auto;
}

.payment_mode_padding {
    padding: 18px 20px;
    border: 1px solid #9f73ce;
    border-radius: 10px;
}
.payment_mode_table {
    border-top: 1px solid #dde0e4 !important;
    border-bottom: 1px solid #dde0e4 !important;
}
.payment_mode_padding .payment_mode_border thead tr th {
    border-top: 0 !important;
    border-bottom: 0 !important;
}
.payment_mode_padding .payment_mode_border thead tr th::after {
    position: absolute;
    content: "";
    top: 100%;
    left: 0;
    width: 100%;
    border-top: 1px solid #dde0e4;
    height: 100%;
    z-index: 1;
}

.sticky_payment_modal thead th {
    position: sticky;
    top: 0;
    z-index: 99;
    background-color: white;
}

.h-34px {
    height: 34px !important;
}

.missing_tour_close_btn {
    position: absolute;
    top: -15px;
    right: -10px;
}
.react-input-disabled .react-input-disabled-td{
    background-color: #eff2f5 !important;
    border-radius: 8px !important;
}

.inventory_modal_button{
    height: 32px !important;
    border: none !important;
}
.datepicker-font input{
    font-size: 13px;
    border: 1px solid transparent !important;
    border-radius: 0 !important;
    height: 100% !important;
}
.custom-field-table-modal table td.custom-input-p-0{
    padding: 0 !important;
}
.custom-field-table-modal table td.custom-input-p-0 .form-control
{
    border-radius: 0 !important;
    font-size: 13px !important;
    height: 100% !important;
}
.width-140px{
    width: 140px !important;
}
.custom-table-field-test-serial table tbody tr td.bg-gray-normal{
background-color:#f0f0f0 !important;
}
