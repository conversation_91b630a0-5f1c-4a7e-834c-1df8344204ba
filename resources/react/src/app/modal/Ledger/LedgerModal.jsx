import { useFormik } from "formik";
import moment from "moment";
import { useContext, useEffect, useRef, useState } from "react";
import { Col, Form, Modal, OverlayTrigger, Row, Tooltip } from "react-bootstrap";
import PhoneInput from "react-phone-input-2";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import * as Yup from "yup";
import deleteSvg from "../../../assets/images/delete.svg";
import Close from "../../../assets/images/svg/close";
import CustomFieldDate from "../../../components/ui/CustomFieldDate";
import { FormInput } from "../../../components/ui/Input";
import ReactSelect from "../../../components/ui/ReactSelect";
import { toastType } from "../../../constants";
import { StateContext } from "../../../context/StateContext";
import { CheckGstValidate, CheckPanValidate, formattedDate } from "../../../shared/calculation";
import CountrySelect from "../../../shared/CountrySelect";
import useDropdownOption from "../../../shared/dropdownList";
import { convertNameIntoCamelCase, fetchGstDetail } from "../../../shared/prepareData";
import { addToast, errorToast } from "../../../store/actions/toastAction";
import { fetchBrokerDetailsById, getBrokerModelDetail } from "../../../store/broker/brokerSlice";
import {
    checkGstNumberAlreadyUsed,
    fetchEntityData,
    fetchGstData,
    fetchShippingGstData,
    gstDetail,
    resetGst,
    shippingGst,
} from "../../../store/gst/gstSlice";
import {
    addLedger,
    deleteItemStock,
    fetchBankDetail,
    fetchItemStockList,
    fetchPartyDetail,
    fetchTcsRateDetail,
    fetchTdsRateDetail,
    getLedgerById,
    getLedgerGroupDetail,
    ifscDetail,
    updateLedger,
} from "../../../store/ledger/ledgerSlice";
import { fetchGstRateList } from "../../../store/rate/rateSlice";
import { fetchShippingAddressList } from "../../../store/shippingAddress/shippingAddressSlice";
import { fetchTransportById } from "../../../store/transport/transportSlice";
import DocumentModal from "../../common/DocumentModal";
import WarningModal from "../../common/WarningModal";
import AddItemStockModal from "../../ledger-master/openingStockModal";
import BillWiseModal from "../../modal/Ledger/BillWiseModal";
import AddBrokerModal from "../Broker/BrokerModal";
import AddItemCategoryModal from "../Item/ItemCategoryModal";
import AddTransportModal from "../Transport/TransportModal";
import LocationOfAsset from "./locationOfAsset";
import { renderLedgerField } from "./SubLedgerModel";
import { getEditData, handleSubmitLedger } from "./SubmitLedger";
import CessRateModal from "../Item/CessRateModal";

const AddLedgerModal = ({
    show,
    handleClose,
    ledgerDetailOptions,
    entityType,
    ledgerGstOption,
    name,
    // ledgerGroupType,
    action,
    gstQuote,
    setGstQuote,
    getId,
    isPurchase,
    index,
    setIndex,
    items,
    setItems,
    addLessChanges,
    setAddLessChanges,
    additionalCharges,
    setAdditionalCharges,
    paymentLedgerDetail,
    setPaymentLedgerDetail,
    tcsRate,
    setTcsRate,
    changeLedgerName,
    setTdsRate,
    taxableValue,
    invoiceValue,
    itemType,
    changeTax,
    partyLedgerId,
    shipping_address_type,
    isPurchaseOcr=false,
    ocrData,
    setOcrData,
    statementId,
    configurationSale,
    setConfigurationSale,
    isBankDetails = false,
}) => {
    const initialValue = {
        id: "",
        name: "",
        group_id: "",
        is_use_in_other_companies: 0,
        party_details: {
            gstin: "",
            billing_address: {
                address_1: "",
                address_2: "",
                country_id: 101,
                state_id: "",
                city_id: "",
                pin_code: "",
            },
            same_as_billing_address: true,
            shipping_address: {
                party_name_same_as_address_name: true,
                address_name: "",
                address_id: "",
                shipping_gstin: null,
                shipping_name: null,
                address_1: "",
                address_2: "",
                country_id: 101,
                state_id: "",
                city_id: "",
                pin_code: "",
            },
            same_as_shipping_address: {
                shipping_gstin: null,
                shipping_name: null,
                address_1: "",
                address_2: "",
                country_id: 101,
                state_id: "",
                city_id: "",
                pin_code: "",
            },
            contact_person_name: "",
            region_iso_1: "in",
            region_code_1: "+91",
            contact_person_phone_1: "",
            contact_person_phone_input_1: "",
            region_iso_2: "in",
            region_code_2: "+91",
            contact_person_phone_2: "",
            contact_person_phone_input_2: "",
            contact_person_email: "",
        },
        tax_details: {
            pan: "",
            type_of_entity: "",
            gst_registration_type: "",
            gst_return_status: null,
            is_tds_applicable: false,
            tan: "",
            type_of_entity_character: "",
            cin_number: "",
        },
        other_details: {
            credit_period: "",
            credit_period_type: 1,
            allow_credit_limit: 0,
            credit_limit: "",
            credit_limit_action: "",
            broker: "",
            brokerage_percentage: "",
            brokerage_on_value: "",
            transporter: "",
            upload_document: null,
        },
        location_of_asset_id: "",
        //income
        income_type: "",
        is_gst_applicable: 0,
        gst_tax_id: "",
        gst_cess_rate: "",
        hsn_sac_code: "",
        description: "",
        //expense
        expense_type: "",
        //bank
        account_holder_name: "",
        account_number: "",
        account_type: 0,
        ifsc_code: "",
        bank_name: "",
        bank_branch: "",
        upi_id: "",
        swift_code: null,
        swift_code_label: "",
        // secure loan
        secured_loan_type: "",
        loan_account_number: "",
        name_of_financier: "",
        rate_of_annum: "",

        //unsecure
        unsecured_loan_type: "",
        pan: "",

        //Taxes
        tax_type: "",

        individual_huf: "",
        for_other: "",
        pan_not_given: "",
        calculated_on: 2,
        individual_bill_wise: "",
        yearly_total: "",

        // capital
        name_of_proprietor: "",
        holding_ratio: "",
        profit_loss_sharing_ratio: "",
        pan_number: "",
        aadhar_number: "",
        address_1: "",
        address_2: "",
        pin_code: "",
        country_id: 101,
        state_id: "",
        city_id: "",

        //loan
        address: {
            address_1: "",
            address_2: "",
            country_id: 101,
            state_id: "",
            city_id: "",
            pin_code: "",
        },
        contact_details: {
            contact_person_name: "",
            region_iso_1: "in",
            region_code_1: "+91",
            contact_person_phone_1: "",
            contact_person_phone_input_1: "",
            region_iso_2: "in",
            region_code_2: "+91",
            contact_person_phone_2: "",
            contact_person_phone_input_2: "",
            contact_person_email: "",
            contact_person_website: "",
            upload_document: null,
        },
        opening_balance_details: {
            opening_balance: "",
            opening_balance_dr_cr: 1,
            bill_wise: ""
        },
        action: action,
        parent_action: action,
        rounding_method: 1,
        bill_wise_opening_balance_details:Array(5).fill({
            voucher_number: "",
            voucher_date: "",
            due_date: "",
            transaction_type: null,
            total_amount: "",
            received_amount: "",
            paid_amount: "",
            pending_amount: "",
            is_editable: true
        })
    };

    const { id } = useParams();

    const schema = Yup.object({
        tax_details: Yup.object().shape({
            pan: Yup.string()
                .nullable()
                .test(
                    "is-valid-gst",
                    "PAN Number is Invalid",
                    value => !value || CheckPanValidate(value)
                ),
        }),
    });

    const dispatch = useDispatch();
    const { company, transport, ledger, gst } = useSelector(state => state);
    const {
        AccountTypeOptions,
        roundOffOption,
        brokerOptions,
        transportOptions,
        ledgerEntityType,
        brokerageOption,
        ledgerGroupOptions,
        gstCessOptions,
        gstOptions,
        billWiseTransactionType,
        ledgerGroupType
    } = useDropdownOption();
    const [groupLedgerList, setGroupLedgerList] = useState([]);
    const [isAllLedger, setIsAllLedger] = useState(true);
    const [fields, setFields] = useState([]);
    const [ledgerOption, setLedgerOption] = useState([]);
    const [oldGroupId, setOldGroupId] = useState("");
    const [transporterDetail, setTransporterDetail] = useState({
        transport_id: null,
        modelType: false,
    });
    const [error, setError] = useState({
        hsn_code: "",
        isError: false,
    });
    const [swiftCodeShow, setSwiftCodeShow] = useState(false);
    const [swiftCodeLabel, setSwiftCodeLabel] = useState("");
    const [isDefault, setIsDefault] = useState(false);
    const [descriptionCount, setDescriptionCount] = useState(0);
    const [nameCount, setNameCount] = useState(0);
    const [isFocusedItem, setIsFocusedItem] = useState(false);
    const [isFocused, setIsFocused] = useState(false);
    const handleFocusItem = () => setIsFocusedItem(true);
    const handleBlurItem = () => setIsFocusedItem(false);
    const [typingTimeout, setTypingTimeout] = useState(0);
    const [isAddCessRate, setIsAddCessRate] = useState(false);
    const [cessRateId, setCessRateId] = useState("");

    const inputRef = useRef();
    const companyEntityType = company?.company?.company_tax?.entity_type;
    const companyState = company?.company?.billing_address?.state_id || "";

    const {
        isTransportModel,
        openTransportModel,
        closeTransportModel,
        isItemCatModel,
        openItemCatModel,
        closeItemCatModel,
        isBrokerModel,
        openBrokerModel,
        closeBrokerModel,
        openShippingFromModel,
        shippingAddress,
        selectShippingAddress,
        isLocationOfAsset,
        openLocationOfAssetModel,
        closeLocationOfAssetModel,
        GSTError,
        setGSTError,
        setIsChangePartyId,
        setIsEditCalculation,
        isChangeGroupType,
        setIsChangeGroupType,
        openStockModel,
        closeStockModel,
        isShowItemStockModel,
        isDisable,
        setIsDisable,
        countryName,
        setCountryName,
        stateName,
        setStateName,
        cityName,
        setCityName,
        setIsInitialDataLoaded,
        setIsInitialDataLoaded2,
        setSameAsBill,
        partyAddress,
        setPartyAddress,
        isShippingAddressModel,
        transactionShippingAddressId,
        notExistParty,
        setNotExistParty,
        checkGroupLedgerType,
        setIsCheckGstType
    } = useContext(StateContext);

    const [countryId, setCountryId] = useState(101);
    const [stateId, setStateId] = useState(companyState);
    const [cityId, setCityId] = useState("");
    const [countryId2, setCountryId2] = useState(101);
    const [cityId2, setCityId2] = useState(companyState);
    const [stateId2, setStateId2] = useState("");
    const [selectedRows, setSelectedRows] = useState([]);
    const [showGstNotUnique, setShowGstNotUnique] = useState(false);
    const [showBulkDeleteModel, setShowBulkDeleteModel] = useState(false);
    const [gstNotUniqueMessage, setGstNotUniqueMessage] = useState("");
    const [isUpdateTaxType, setIsUpdateTaxType] = useState(false);
    const [multiDate, setMultiDate] = useState([]);
    const [isDeleteItemStock, setIsDeleteItemStock] = useState(false);
    const [editStockDetail, setEditStockDetail] = useState("");
    const [openingItemStock, setOpeningItemStock] = useState("");
    const [itemStockList, setItemStockList] = useState([]);

    const { Country, State, City, Country2, State2, City2 } = CountrySelect({
        countryId,
        setCountryId,
        stateId,
        setStateId,
        cityId,
        setCityId,
        countryId2,
        setCountryId2,
        stateId2,
        setStateId2,
        cityId2,
        setCityId2,
        countryName,
        setCountryName,
        stateName,
        setStateName,
        cityName,
        setCityName,
    });
    const [GSTError2, setGSTError2] = useState("");
    const [ledgerList, setLedgerList] = useState(initialValue);
    const [isCreatedItemGroup, setIsCreatedItemGroup] = useState(false);
    const [isOpenShippingAddress, setIsOpenShippingAddress] = useState(false);
    const [totalAmount, setTotalAmount] = useState(0);
    const [receivedAmount, setReceivedAmount] = useState(0);
    const [pendingAmount, setPendingAmount] = useState(0);
    const [DrCrAmount, setDrCrAmount] = useState(0);
    const [isShowDrCr, setIsShowDrCr] = useState("")
    const [brokerDetail, setBrokerDetail] = useState({
        broker_id: null,
        broker_percentage: null,
        brokerage_on_value: 1,
    });
    const [isFirstRender, setIsFirstRender] = useState(true);
    const [isShowProfitRatio, setIsShowProfitRatio] = useState("");

    useEffect(() => {
        if (inputRef.current) {
            inputRef.current.focus();
            dispatch(fetchGstRateList());
        }
    }, [dispatch]);

    const formik = useFormik({
        initialValues: initialValue,
        validationSchema: schema,
        onSubmit: values => {
            if (values?.parent_action === "Supplier" || values?.parent_action === "Customers") {
                const email = values.party_details.contact_person_email;

                if (email && !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email)) {
                    dispatch(
                        errorToast({
                            text: "Enter a valid email address",
                            type: toastType.ERROR,
                        })
                    );
                    return;
                }
            }
            handleSubmit(values);
        },
    });


    const swiftCodelabels = async(data) => {
         formik.setFieldValue("swift_code_label", data || "SWIFT Code");
         setSwiftCodeLabel(data);
    }

    const handleSwiftCode = async () => {
        formik.setFieldValue("swift_code_label", swiftCodeLabel);
        setSwiftCodeShow(false);
    }

    useEffect(() => {
        const ledgerId = checkGroupLedgerType && ledgerDetailOptions?.find(item => item?.label === (checkGroupLedgerType == "item" ? "Income" : "Supplier"))
        formik.setFieldValue("name", notExistParty);
        if (notExistParty && ledgerId && checkGroupLedgerType) {
            setTimeout(() => {
                formik.setFieldValue("group_id", parseFloat(ledgerId.value));
                formik.setFieldValue("parent_action", checkGroupLedgerType == "item" ? "Income" : "Supplier");
                setLedgerList({ ...ledgerList, group_id: ledgerId.value, action: ledgerId.label });
            }, 500);
        }
      }, [notExistParty, checkGroupLedgerType, ledgerDetailOptions])

    const gstRegistrationType =
        ledgerGstOption &&
        ledgerGstOption?.map(gst => {
            return {
                ...gst,
                isDisabled: formik.values?.party_details?.gstin
                    ? gst.label === "Unregistered" // Disable "Unregistered" if GST is present
                    : gst.label !== "Unregistered",
            };
        });

    const handleGroupChange = e => {
        setIsChangeGroupType(true);
        dispatch(ifscDetail([]));
        const group = e.label;
        const currentLedgerName = formik.values?.name;
        const currentLedgerId = formik.values?.id;
        const checkParentId = ledgerGroupOptions?.find(ledger => ledger.value == e.value);
        const getCurrentParentId = checkParentId?.parent_group
            ? { label: checkParentId?.parent_group?.name, value: checkParentId?.parent_group?.id }
            : { label: checkParentId?.label, value: checkParentId?.value };
        // const getCurrentParentId = checkParentId?.parent_id
        //     ? ledgerGroupOptions?.find(ledger => ledger.value === checkParentId.parent_id)
        //     : checkParentId;
        const isClearEditData = ledgerGroupOptions?.find(ledger => ledger.value == oldGroupId);
        const resetData =
            name?.name == "Update Ledger"
                ? isClearEditData?.label !== "Customers" && isClearEditData?.label !== "Supplier"
                : getCurrentParentId
                ? getCurrentParentId?.value !== "Customers" &&
                  getCurrentParentId?.value !== "Supplier"
                : group !== "Customers" && group !== "Supplier";
        if (resetData) {
            formik.resetForm();
            setDescriptionCount(0);
        }
        if (
            getCurrentParentId?.label === "Customers" ||
            getCurrentParentId?.label === "Supplier" ||
            getCurrentParentId?.label === "Fixed Asset" ||
            getCurrentParentId?.label === "Fixed Asset 1" ||
            getCurrentParentId?.label === "Income" ||
            getCurrentParentId?.label === "Expense" ||
            getCurrentParentId?.label === "Secured Loan" ||
            getCurrentParentId?.label === "Unsecured Loan" ||
            getCurrentParentId?.label === "Taxes - GST" ||
            getCurrentParentId?.label === "Taxes - TDS" ||
            getCurrentParentId?.label === "Taxes - TCS" ||
            getCurrentParentId?.label === "TDS Receivable" ||
            getCurrentParentId?.label === "TCS Receivable" ||
            getCurrentParentId?.label === "Loan and Advance (Asset)"
        ) {
            dispatch(getLedgerGroupDetail(getCurrentParentId?.value));
        }
        setFields(getCurrentParentId?.label ? [getCurrentParentId?.label] : []);
        setLedgerList({ ...ledgerList, group_id: e.value, action: group });
        formik.setFieldValue("group_id", parseFloat(e.value));
        formik.setFieldValue("parent_action", getCurrentParentId?.label);
        formik.setFieldValue("action", getCurrentParentId?.label);
        formik.setFieldValue(
            "opening_balance_details.opening_balance_dr_cr",
            group === "Customers" || group === "Income" ? 1 : 2
        );
        formik.setFieldValue("opening_balance_details.opening_balance", "");
        formik.setFieldValue("id", currentLedgerId);
        formik.setFieldValue("name", currentLedgerName);
        formik.setFieldValue("state_id", companyState);
        formik.setFieldValue("address.state_id", companyState);
        formik.setFieldValue("party_details.billing_address.state_id", companyState);
        if (formik.values?.party_details?.same_as_billing_address) {
            formik.setFieldValue("party_details.billing_address.state_id", companyState);
        }
        setIsDataStore(true);
        if (getCurrentParentId?.label === "Bank") {
            swiftCodelabels();
        };
    };

    useEffect(() => {
        if (
            name.name === "Update Ledger" ||
            name.name === "Update Customer" ||
            name.name === "Update Supplier"
        ) {
            const checkGroup = groupLedgerList?.find(
                group => group.value == formik.values.parent_id
            );
            if (checkGroup && checkGroup?.value == oldGroupId && !isAllLedger) {
                setGroupLedgerList(prev => [
                    ...prev,
                    { label: formik.values.new_group_name, value: formik.values.group_id },
                ]);
            } else if (formik.values.new_group_name && isAllLedger) {
                setGroupLedgerList(ledgerDetailOptions);
            }
        }
    }, [ledgerGroupOptions]);

    useEffect(() => {
        if (companyEntityType === 3 && name.name !== "Update Ledger") {
            formik.setFieldValue("holding_ratio", 100);
            formik.setFieldValue("profit_loss_sharing_ratio", 100);
        }
    }, [companyEntityType, formik.values.group_id]);

    useEffect(() => {
        const companyEntityType = company?.company?.company_tax?.entity_type;
        if((companyEntityType == 1 || companyEntityType == 2 || companyEntityType === null) && !formik.values.id && formik.values.name){
            formik.setFieldValue("name_of_proprietor", formik.values?.name);
        }
    }, [formik.values?.parent_action, company?.company, formik.values.id]);

    useEffect(() => {
        const defaultGstOption = ledgerGstOption?.find(option => option?.label == "Unregistered");
        if (defaultGstOption && !formik.values?.tax_details?.gst_registration_type) {
            formik.setFieldValue("tax_details.gst_registration_type", defaultGstOption?.value);
        }
    }, [ledgerGstOption]);

    useEffect(() => {
        const company_detail = company?.company?.company_tax?.pan_number?.slice(3, 4);
        setIsShowProfitRatio(company_detail);
        const data = company?.dispatch_address;
        setCountryId(data?.country_id || 101);
        setStateId(data?.state_id || companyState);
        setStateId2(data?.state_id || companyState);
        formik.setFieldValue("party_details.billing_address.country_id", data?.country_id || 101);
        formik.setFieldValue(
            "party_details.billing_address.state_id",
            data?.state_id || companyState
        );
        formik.setFieldValue(
            "party_details.shipping_address.state_id",
            data?.state_id || companyState
        );
        const Date = [
            moment(company?.company?.currentFinancialYear?.yearStartDate).format("DD-MM-YYYY"),
            moment(company?.company?.currentFinancialYear?.yearEndDate).format("DD-MM-YYYY"),
        ];
        setMultiDate(Date);
    }, [company, companyState]);

    useEffect(() => {
        ;(async () => {
            if (isPurchaseOcr && (partyAddress?.billingAddress || partyAddress?.shippingAddress)) {

            const same_as_billing_address = false;
            setTimeout(() => {
                formik.setFieldValue( "party_details.same_as_billing_address", same_as_billing_address);
            }, 500);


            if (gstQuote?.gstin?.length === 15 && CheckGstValidate(gstQuote?.gstin)) {
                const response = await checkGstNumberAlreadyUsed(gstQuote?.gstin, "");
                if (response?.is_not_unique_gst_number) {
                    setShowGstNotUnique(true);
                    setGstNotUniqueMessage(response?.massage);
                }
                setGSTError(""); // Clear error on valid GSTIN
                formik.setFieldValue("party_details.gstin", gstQuote?.gstin || "");
            }


            formik.setFieldValue("party_details.billing_address.address_1", partyAddress?.billingAddress?.address_1);
            formik.setFieldValue("party_details.billing_address.address_2", partyAddress?.billingAddress?.address_2);
            formik.setFieldValue("party_details.billing_address.country_id", partyAddress?.billingAddress?.country_id || 101);
            formik.setFieldValue("party_details.billing_address.state_id", partyAddress?.billingAddress?.state_id);
            formik.setFieldValue("party_details.billing_address.city_id", partyAddress?.billingAddress?.city_id);
            formik.setFieldValue("party_details.billing_address.pin_code", partyAddress?.billingAddress?.pin_code);
            setCountryId(partyAddress?.billingAddress?.country_id || 101);
            setStateId(partyAddress?.billingAddress?.state_id);
            setCityId(partyAddress?.billingAddress?.city_id);
            if(formik.values?.party_details?.shipping_address?.party_name_same_as_address_name){
                formik.setFieldValue(
                    "party_details.shipping_address.address_name",
                    convertNameIntoCamelCase(partyAddress?.shippingAddress?.shipping_name)
                );
            }
            formik.setFieldValue("party_details.shipping_address.address_1", partyAddress?.shippingAddress?.address_1);
            formik.setFieldValue("party_details.shipping_address.address_2", partyAddress?.shippingAddress?.address_2);
            formik.setFieldValue("party_details.shipping_address.country_id", partyAddress?.shippingAddress?.country_id || 101);
            formik.setFieldValue("party_details.shipping_address.state_id", partyAddress?.shippingAddress?.state_id);
            formik.setFieldValue("party_details.shipping_address.city_id", partyAddress?.shippingAddress?.city_id);
            formik.setFieldValue("party_details.shipping_address.pin_code", partyAddress?.shippingAddress?.pin_code);
            formik.setFieldValue("party_details.shipping_address.shipping_name", partyAddress?.shippingAddress?.shipping_name);
            formik.setFieldValue("party_details.shipping_address.shipping_gstin", partyAddress?.shippingAddress?.shipping_gstin);
            setCountryId2(partyAddress?.shippingAddress?.country_id || 101);
            setStateId2(partyAddress?.shippingAddress?.state_id);
            setCityId2(partyAddress?.shippingAddress?.city_id);
        }
        })();
    }, [partyAddress]);

    const handleDeleteItemStock = async () => {
        const formattedDates = multiDate?.map(date =>
            moment(date, "DD-MM-YYYY").format("YYYY-MM-DD")
        );
        dispatch(
            deleteItemStock(
                editStockDetail?.id,
                editStockDetail,
                formattedDates[0],
                formattedDates[1]
            )
        );
        setIsDeleteItemStock(false);
    };

    useEffect(() => {
        if (formik.values?.party_details?.same_as_billing_address) {
            formik.setFieldValue("country_id", countryId);
            formik.setFieldValue("state_id", stateId);
            formik.setFieldValue("city_id", cityId);
            formik.setFieldValue("party_details.billing_address.state_id", stateId);
            formik.setFieldValue("party_details.shipping_address.state_id", stateId);
        }
    }, [countryId, stateId, cityId]);

    useEffect(() => {
        setLedgerOption(ledgerDetailOptions);
    }, [ledgerDetailOptions]);

    useEffect(() => {
        if (
            (!action || ledgerDetailOptions.length === 0 || !isFirstRender) ||
            (name.name?.includes("Update"))
        )
            return;

        const currentGroup = ledgerDetailOptions.find(option => option.label === action);
        if (currentGroup) {
            formik.setFieldValue("group_id", currentGroup.value);
            if(!formik.values.group_id){
                dispatch(getLedgerGroupDetail(currentGroup.value));
            }
            setFields([currentGroup.value]);
            setIsFirstRender(false);
        }
    }, [ledgerDetailOptions]);

    useEffect(() => {
        if (
            shippingAddress[selectShippingAddress] &&
            isOpenShippingAddress &&
            !formik.values?.party_details?.same_as_billing_address
        ) {
            formik.setFieldValue(
                "party_details.shipping_address.address_id",
                shippingAddress[selectShippingAddress]?.id
            );
            formik.setFieldValue(
                "party_details.shipping_address.shipping_gstin",
                shippingAddress[selectShippingAddress]?.shipping_gstin ?? ""
            );
            if(shippingAddress[selectShippingAddress]?.party_name_same_as_address_name == true){
                formik.setFieldValue(
                    "party_details.shipping_address.address_name",
                    shippingAddress[selectShippingAddress]?.shipping_name ?? ""
                );
            }else{
                formik.setFieldValue("party_details.shipping_address.address_name", shippingAddress[selectShippingAddress]?.address_name ?? "");
            }
            formik.setFieldValue(
                "party_details.shipping_address.party_name_same_as_address_name",
                shippingAddress[selectShippingAddress]?.party_name_same_as_address_name ?? ""
            );
            formik.setFieldValue(
                "party_details.shipping_address.shipping_name",
                shippingAddress[selectShippingAddress]?.shipping_name ?? ""
            );
            formik.setFieldValue(
                "party_details.shipping_address.address_1",
                shippingAddress[selectShippingAddress]?.address_1 || ""
            );
            formik.setFieldValue(
                "party_details.shipping_address.address_2",
                shippingAddress[selectShippingAddress]?.address_2 || ""
            );
            formik.setFieldValue(
                "party_details.shipping_address.country_id",
                shippingAddress[selectShippingAddress]?.country_id || 101
            );
            formik.setFieldValue(
                "party_details.shipping_address.state_id",
                shippingAddress[selectShippingAddress]?.state_id || ""
            );
            formik.setFieldValue(
                "party_details.shipping_address.city_id",
                shippingAddress[selectShippingAddress]?.city_id || ""
            );
            formik.setFieldValue(
                "party_details.shipping_address.pin_code",
                shippingAddress[selectShippingAddress]?.pin_code || ""
            );
            formik.setFieldValue(
                "party_details.shipping_address.gstin",
                shippingAddress[selectShippingAddress]?.gstin || ""
            );
            formik.setFieldValue(
                "party_details.shipping_address.party",
                shippingAddress[selectShippingAddress]?.party || ""
            );
            setCountryId2(shippingAddress[selectShippingAddress]?.country_id || 101);
            setStateId2(shippingAddress[selectShippingAddress]?.state_id || companyState);
            setCityId2(shippingAddress[selectShippingAddress]?.city_id);
        }
    }, [shippingAddress[selectShippingAddress], isOpenShippingAddress]);

    const handleHSNCode = e => {
        const { value } = e.target;

        if (formik.values.item_type == 1 && !/^\d*$/.test(value)) {
            return;
        }

        if (value.length > 8) {
            return;
        }

        formik.setFieldValue("hsn_sac_code", value);
    };

    useEffect(() => {
        if (
            name.name === "Update Ledger" ||
            name.name === "Update Customer" ||
            name.name === "Update Supplier"
        ) {
            const ledgerData = ledger?.getLedgerById;
            const updateLedgerList = getEditData(ledgerData.ledger, ledgerData);

            if (updateLedgerList?.action === "Bank") {
                swiftCodelabels(updateLedgerList?.swift_code_label);
            };

            if (
                // ledgerData.ledger?.group_id ||
                ledgerData.ledger?.parent_group === "Customers" ||
                ledgerData.ledger?.parent_group === "Supplier" ||
                ledgerData.ledger?.parent_group === "Fixed Asset" ||
                ledgerData.ledger?.parent_group === "Fixed Asset 1" ||
                ledgerData.ledger?.parent_group === "Income" ||
                ledgerData.ledger?.parent_group === "Expense" ||
                ledgerData.ledger?.parent_group === "Loan and Advance (Asset)" ||
                ledgerData.ledger?.parent_group === "Secured Loan" ||
                ledgerData.ledger?.parent_group === "Unsecured Loan" ||
                ledgerData.ledger?.parent_group === "Taxes - GST" ||
                ledgerData.ledger?.parent_group === "Taxes - TDS" ||
                ledgerData.ledger?.parent_group === "Taxes - TCS" ||
                ledgerData.ledger?.parent_group === "TDS Receivable" ||
                ledgerData.ledger?.parent_group === "TCS Receivable"
            ) {
                dispatch(getLedgerGroupDetail(ledgerData.ledger?.group_id));
            }
            const name = ledgerDetailOptions.find(
                item => item.label === ledgerData.ledger?.group?.name
            );
            setIsDefault(ledgerData.ledger?.is_default);
            const groupList = ledgerData?.group_lists
                ? Object.entries(ledgerData?.group_lists).map(([key, value]) => {
                      return {
                          label: value,
                          value: key,
                      };
                  })
                : [];
            setIsInitialDataLoaded(false);
            setIsInitialDataLoaded2(false);
            setGroupLedgerList(groupList);
            setIsAllLedger(ledgerData?.is_all_ledger ? true : false);
            setFields(name ? [name] : []);
            if (updateLedgerList) {
                if (updateLedgerList?.party_details) {
                    dispatch(fetchShippingAddressList(parseFloat(updateLedgerList?.id)));
                    setCountryId(
                        updateLedgerList?.party_details?.billing_address?.country_id || 101
                    );
                    setStateId(updateLedgerList?.party_details?.billing_address?.state_id);
                    setCityId(updateLedgerList?.party_details?.billing_address?.city_id);
                }
                if (updateLedgerList?.address) {
                    setCountryId(updateLedgerList?.address?.country_id || 101);
                    setStateId(updateLedgerList?.address?.state_id);
                    setCityId(updateLedgerList?.address?.city_id);
                }
                if (updateLedgerList?.name_of_proprietor) {
                    setCountryId(updateLedgerList?.country_id || 101);
                    setStateId(updateLedgerList?.state_id);
                    setCityId(updateLedgerList?.city_id);
                }
                setCountryId2(updateLedgerList?.party_details?.shipping_address?.country_id || 101);
                setStateId2(updateLedgerList?.party_details?.shipping_address?.state_id);
                setCityId2(updateLedgerList?.party_details?.shipping_address?.city_id);
                formik.setFieldValue("id", updateLedgerList?.id);
                formik.setFieldValue("name", updateLedgerList?.name);
                setNameCount(updateLedgerList?.name?.length);
                formik.setFieldValue("group_id", updateLedgerList?.group_id);
                setOldGroupId(updateLedgerList?.group_id);
                formik.setFieldValue("is_use_in_other_companies", updateLedgerList?.is_use_in_other_companies);
                formik.setFieldValue("action", updateLedgerList?.action);
                formik.setFieldValue("party_details.gstin", updateLedgerList?.gstin);
                formik.setFieldValue("parent_action", updateLedgerList?.action);
                formik.setFieldValue("is_gst_applicable", updateLedgerList?.is_gst_applicable);
                formik.setFieldValue("gst_tax_id", updateLedgerList?.gst_tax_id);
                formik.setFieldValue("gst_cess_rate", updateLedgerList?.gst_cess_rate);
                formik.setFieldValue("hsn_sac_code", updateLedgerList?.hsn_sac_code);
                formik.setFieldValue("description", updateLedgerList?.description);
                setDescriptionCount(updateLedgerList?.description?.length);
                formik.setFieldValue(
                    "other_details.allow_credit_limit",
                    updateLedgerList?.allow_credit_limit
                );
                formik.setFieldValue("other_details.credit_limit", updateLedgerList?.credit_limit);
                formik.setFieldValue("other_details", updateLedgerList?.other_details);
                formik.setFieldValue("holding_ratio", updateLedgerList?.holding_ratio);
                formik.setFieldValue("name_of_proprietor", updateLedgerList?.name_of_proprietor);
                formik.setFieldValue(
                    "profit_loss_sharing_ratio",
                    updateLedgerList?.profit_loss_sharing_ratio
                );
                formik.setFieldValue("pan_number", updateLedgerList?.pan_number);
                formik.setFieldValue("pin_code", updateLedgerList?.pin_code);
                formik.setFieldValue("address_1", updateLedgerList?.address_1);
                formik.setFieldValue("address_2", updateLedgerList?.address_2);
                formik.setFieldValue("country_id", updateLedgerList?.country_id);
                formik.setFieldValue("state_id", updateLedgerList?.state_id);
                formik.setFieldValue("city_id", updateLedgerList?.city_id);
                formik.setFieldValue("aadhar_number", updateLedgerList?.aadhar_number);
                formik.setFieldValue("pan", updateLedgerList?.pan);
                formik.setFieldValue("individual_huf", updateLedgerList?.individual_huf);
                formik.setFieldValue(
                    "location_of_asset_id",
                    updateLedgerList?.location_of_asset_id
                );
                formik.setFieldValue("for_other", updateLedgerList?.for_other);
                formik.setFieldValue("pan_not_given", updateLedgerList?.pan_not_given);
                formik.setFieldValue(
                    "individual_bill_wise",
                    updateLedgerList?.individual_bill_wise
                );
                formik.setFieldValue("yearly_total", updateLedgerList?.yearly_total);
                formik.setFieldValue("rounding_method", updateLedgerList?.rounding_method);
                formik.setFieldValue("calculated_on", updateLedgerList?.calculated_on);
                formik.setFieldValue("unsecured_loan_type", updateLedgerList?.unsecured_loan_type);
                formik.setFieldValue(
                    "unsecured_loan_account_number",
                    updateLedgerList?.unsecured_loan_account_number
                );
                formik.setFieldValue(
                    "unsecured_loan_name_of_financier",
                    updateLedgerList?.unsecured_loan_name_of_financier
                );
                formik.setFieldValue(
                    "unsecured_loan_rate_of_annum",
                    updateLedgerList?.unsecured_loan_rate_of_annum
                );
                formik.setFieldValue(
                    "unsecured_loan_pan_number",
                    updateLedgerList?.unsecured_loan_pan_number
                );
                formik.setFieldValue("bill_wise_opening_balance_details", updateLedgerList?.bill_wise_opening_balance_details?.length > 0 ? updateLedgerList?.bill_wise_opening_balance_details : formik.values.bill_wise_opening_balance_details);
                formik.setFieldValue("account_holder_name", updateLedgerList?.account_holder_name);
                formik.setFieldValue("account_number", updateLedgerList?.account_number);
                formik.setFieldValue("account_type", updateLedgerList?.account_type);
                formik.setFieldValue("ifsc_code", updateLedgerList?.ifsc_code);
                formik.setFieldValue("bank_name", updateLedgerList?.bank_name);
                formik.setFieldValue("bank_branch", updateLedgerList?.bank_branch);
                formik.setFieldValue("upi_id", updateLedgerList?.upi_id);
                formik.setFieldValue("swift_code", updateLedgerList?.swift_code);
                formik.setFieldValue("rounding_method", updateLedgerList?.rounding_method);
                formik.setFieldValue("tax_type", updateLedgerList?.tax_type);
                formik.setFieldValue("name_of_financier", updateLedgerList?.name_of_financier);
                formik.setFieldValue("secured_loan_type", updateLedgerList?.secured_loan_type);
                formik.setFieldValue("loan_account_number", updateLedgerList?.loan_account_number);
                formik.setFieldValue("rate_of_annum", updateLedgerList?.rate_of_annum);
                formik.setFieldValue("party_details", updateLedgerList?.party_details);
                formik.setFieldValue(
                    "party_details.shipping_address.party_name_same_as_address_name",
                    updateLedgerList?.party_details?.party_name_same_as_address_name
                );
                formik.setFieldValue("tax_details", updateLedgerList?.tax_details);
                formik.setFieldValue("other_details", updateLedgerList?.other_details);
                formik.setFieldValue(
                    "party_details.same_as_billing_address",
                    updateLedgerList?.party_details?.same_as_billing_address
                );
                formik.setFieldValue("income_type", updateLedgerList?.income_type);
                formik.setFieldValue("expense_type", updateLedgerList?.expense_type);
                formik.setFieldValue("address", updateLedgerList?.address);
                formik.setFieldValue("contact_details", updateLedgerList?.contact_details);
                formik.setFieldValue(
                    "opening_balance_details",
                    updateLedgerList?.opening_balance_details
                );
                setPartyAddress({
                    ...partyAddress,
                    shippingAddress: {
                        ...partyAddress?.shippingAddress,
                        shipping_address_id:
                            updateLedgerList?.party_details?.shipping_address?.address_id,
                    },
                });
            }
        }
    }, [ledger?.getLedgerById]);

    useEffect(() => {
        const gstData = gst?.gst;
        if (gstData.length !== 0 && formik.values?.party_details?.gstin) {
            const pan_card = formik.values?.party_details?.gstin?.slice(2, 12);
            const gst_registration = gstData?.result?.dty;
            const find_gst_name = ledgerGstOption.find(name =>
                name?.label.includes(gst_registration)
            );
            formik.setFieldValue("tax_details.gst_registration_type", find_gst_name?.value || 1);
            formik.setFieldValue("tax_details.pan", pan_card);
            const entity_type = pan_card.slice(3, 4);
            dispatch(fetchEntityData(entity_type, formik, "tax_details.type_of_entity"));
            formik.setFieldValue("tax_details.type_of_entity_character", entity_type);
            const addr = gstData?.result?.pradr?.addr;

            let address1 = addr?.bno || "";

            if (addr?.flno) {
                address1 += ", " + addr?.flno;
            }

            if (addr?.bnm) {
                address1 += ", " + addr?.bnm;
            }

            let address2 = addr?.st || "";

            if (addr?.landMark) {
                address2 += ", " + addr?.landMark;
            }

            if (addr?.locality) {
                address2 += ", " + addr?.locality;
            }

            let pinCode = gstData?.result?.pradr?.addr?.pncd;
            let state = gstData?.result?.pradr?.addr?.stcd;
            let city = gstData?.result?.pradr?.addr?.dst;
            const response = fetchGstDetail({ state, city });
            formik.setFieldValue("party_details.billing_address.address_1", address1 || "");
            formik.setFieldValue("name", convertNameIntoCamelCase(gstData?.result?.tradeNam));
            setNameCount(gstData?.result?.tradeNam?.length);
            formik.setFieldValue("party_details.billing_address.address_2", address2 || "");
            formik.setFieldValue("party_details.billing_address.country_id", response.country_id);
            formik.setFieldValue("party_details.billing_address.state_id", response.state_id);
            formik.setFieldValue("party_details.billing_address.city_id", response.city_id);
            formik.setFieldValue("party_details.billing_address.pin_code", pinCode);
            setCountryId(response.country_id || 101);
            setStateId(response.state_id || companyState);
            setCityId(response.city_id);
            if (formik.values.party_details?.same_as_billing_address == true) {
                formik.setFieldValue(
                    "party_details.shipping_address.shipping_gstin",
                    formik.values.party_details?.gstin
                );
                formik.setFieldValue(
                    "party_details.shipping_address.shipping_name",
                    convertNameIntoCamelCase(gstData?.result?.tradeNam)
                );
                formik.setFieldValue(
                    "party_details.shipping_address.address_name",
                    convertNameIntoCamelCase(gstData?.result?.tradeNam)
                );
                formik.setFieldValue("party_details.shipping_address.address_1", address1);
                formik.setFieldValue("party_details.shipping_address.address_2", address2);
                formik.setFieldValue(
                    "party_details.shipping_address.country_id",
                    response.country_id
                );
                formik.setFieldValue("party_details.shipping_address.state_id", response.state_id ?? companyState);
                formik.setFieldValue("party_details.shipping_address.city_id", response.city_id);
                formik.setFieldValue("party_details.shipping_address.pin_code", pinCode);
                formik.setFieldValue("party_details.same_as_shipping_address.address_1", address1);
                formik.setFieldValue("party_details.same_as_shipping_address.address_2", address2);
                formik.setFieldValue(
                    "party_details.same_as_shipping_address.country_id",
                    response.country_id
                );
                formik.setFieldValue(
                    "party_details.same_as_shipping_address.state_id",
                    response.state_id
                );
                formik.setFieldValue(
                    "party_details.same_as_shipping_address.city_id",
                    response.city_id
                );
                formik.setFieldValue("party_details.same_as_shipping_address.pin_code", pinCode);
                setCountryId2(response.country_id || 101);
                setStateId2(response.state_id || companyState);
                setCityId2(response.city_id);
            }
        }
        setTimeout(() => {
            dispatch(gstDetail(""));
        }, 500);
    }, [gst?.gst]);

    useEffect(() => {
        const gstData = gst?.shippingGst;
        if (gstData.length !== 0) {
            const addr = gstData?.result?.pradr?.addr;

            let address1 = addr?.bno || "";

            if (addr?.flno) {
                address1 += ", " + addr?.flno;
            }

            if (addr?.bnm) {
                address1 += ", " + addr?.bnm;
            }

            let address2 = addr?.st || "";

            if (addr?.landMark) {
                address2 += ", " + addr?.landMark;
            }

            if (addr?.locality) {
                address2 += ", " + addr?.locality;
            }
            let pinCode = gstData?.result?.pradr?.addr?.pncd || "";
            let state = gstData?.result?.pradr?.addr?.stcd || "";
            let city = gstData?.result?.pradr?.addr?.dst || "";
            const response = fetchGstDetail({ state, city });
            formik.setFieldValue("party_details.shipping_address.address_1", address1);
            formik.setFieldValue(
                "party_details.shipping_address.shipping_name",
                convertNameIntoCamelCase(gstData?.result?.tradeNam)
            );
            if(formik.values?.party_details.shipping_address?.party_name_same_as_address_name){
                formik.setFieldValue(
                    "party_details.shipping_address.address_name",
                    convertNameIntoCamelCase(gstData?.result?.tradeNam)
                );
            }
            formik.setFieldValue("party_details.shipping_address.address_2", address2);
            formik.setFieldValue(
                "party_details.shipping_address.country_id",
                response.country_id || 101
            );
            formik.setFieldValue("party_details.shipping_address.state_id", response.state_id ?? companyState);
            formik.setFieldValue("party_details.shipping_address.city_id", response.city_id);
            formik.setFieldValue("party_details.shipping_address.pin_code", pinCode);
            setCountryId2(response.country_id || 101);
            setStateId2(response.state_id || companyState);
            setCityId2(response.city_id);
        }
        setTimeout(() => {
            dispatch(shippingGst(""));
        }, 500);
    }, [gst?.shippingGst]);

    useEffect(() => {
        const response = ledger?.ifscDetail;
        if (response.length != 0) {
            formik.setFieldValue("bank_branch", response?.BRANCH);
            formik.setFieldValue("bank_name", response?.BANK);
        }
    }, [ledger?.ifscDetail, formik.values.ifsc_code]);

    const handleChangeLoanAdvancePan = e => {
        const { value } = e.target;
        const entity_type = value?.slice(3, 4);
        dispatch(fetchEntityData(entity_type, formik, "tax_details.type_of_entity"));
        formik.setFieldValue("tax_details.pan", value?.toUpperCase());
    };

    function validateFiles(e) {
        const maxFileSize = 2 * 1024 * 1024;
        const files = Array.from(e.currentTarget.files);
        const validFiles = Array.from(e.currentTarget?.files || []).filter(
            file => file?.size <= maxFileSize
        );

        const totalDocuments = [...files];

        if (files.length > 5 || formik.values?.other_details?.upload_document?.length >= 5) {
            dispatch(
                errorToast({
                    text: "Please Enter Max 5 Documents",
                    type: toastType.ERROR,
                })
            );
            e.currentTarget.value = "";
            return;
        } else if (validFiles.length < (e.currentTarget?.files || []).length) {
            dispatch(
                errorToast({
                    text: "Please Select a file less than 2MB",
                    type: toastType.ERROR,
                })
            );
            e.target.value = "";
            return;
        }

        if (formik.values.parent_action === "Supplier") {
            formik.setFieldValue("other_details.supplier_upload_document", totalDocuments);
        } else {
            formik.setFieldValue("other_details.customer_upload_document", totalDocuments);
        }
    }

    const handlePhoneChange = (value, country) => {
        const valueStr = value.toString();
        const dialCodeStr = country.dialCode.toString();
        let number = "";
        if (valueStr.startsWith(dialCodeStr)) {
            number = valueStr.slice(dialCodeStr.length);
        }
        formik.setFieldValue("party_details.region_iso_1", country.countryCode);
        formik.setFieldValue("party_details.region_code_1", country.dialCode);
        formik.setFieldValue("party_details.contact_person_phone_1", number);
        formik.setFieldValue("party_details.contact_person_phone_input_1", value);
    };
    const handlePhoneChange2 = (value, country) => {
        const valueStr = value.toString();
        const dialCodeStr = country.dialCode.toString();
        let number = "";
        if (valueStr.startsWith(dialCodeStr)) {
            number = valueStr.slice(dialCodeStr.length);
        }
        formik.setFieldValue("party_details.region_iso_2", country.countryCode);
        formik.setFieldValue("party_details.region_code_2", country.dialCode);
        formik.setFieldValue("party_details.contact_person_phone_2", number);
        formik.setFieldValue("party_details.contact_person_phone_input_2", value);
    };
    const handleSubmit = values => {
        const response = handleSubmitLedger(values);
        setSameAsBill(values?.party_details?.same_as_billing_address);
        setIsDisable(true);
        setIsChangePartyId(true);
        if (!values.id) {
            dispatch(
                addLedger(
                    isPurchase,
                    response,
                    handleClose,
                    handleCloseLedgerModal,
                    setGstQuote,
                    getId,
                    index,
                    setIndex,
                    items,
                    setItems,
                    addLessChanges,
                    setAddLessChanges,
                    additionalCharges,
                    setAdditionalCharges,
                    paymentLedgerDetail,
                    setPaymentLedgerDetail,
                    tcsRate,
                    setTcsRate,
                    changeLedgerName,
                    taxableValue,
                    invoiceValue,
                    itemType,
                    changeTax,
                    partyLedgerId,
                    setIsDisable,
                    ocrData,
                    setOcrData,
                    id,
                    statementId,
                    configurationSale,
                    setConfigurationSale,
                    isBankDetails,
                    setIsEditCalculation
                )
            );
        } else {
            dispatch(
                updateLedger(
                    values?.id,
                    isPurchase,
                    response,
                    handleCloseLedgerModal,
                    index,
                    setIndex,
                    setGstQuote,
                    setTcsRate,
                    setTdsRate,
                    items,
                    setItems,
                    addLessChanges,
                    setAddLessChanges,
                    additionalCharges,
                    setAdditionalCharges,
                    paymentLedgerDetail,
                    setPaymentLedgerDetail,
                    tcsRate,
                    changeLedgerName,
                    taxableValue,
                    invoiceValue,
                    setIsEditCalculation,
                    itemType,
                    changeTax,
                    partyLedgerId,
                    setIsDisable,
                    ocrData,
                    setOcrData,
                    id,
                    statementId,
                    configurationSale,
                    setConfigurationSale,
                    isBankDetails
                )
            );
            if (values.action === "Supplier" || values.action === "Customers") {
                setIsChangePartyId(true);
                setTimeout(() => {
                    dispatch(fetchPartyDetail(gstQuote.party_ledger_id));
                }, 1000);
            }
        }
        if (!isShippingAddressModel) {
            setPartyAddress({
                ...partyAddress,
                shippingAddress: {
                    ...partyAddress?.shippingAddress,
                    shipping_address_id: transactionShippingAddressId,
                },
            });
        }
    };

    useEffect(() => {
        if (formik.values.party_details?.billing_address) {
            handleSameAsBill({
                target: {
                    checked: formik.values.party_details?.same_as_billing_address,
                },
            });
        }
    }, [
        formik.values.party_details?.billing_address,
        formik.values.party_details?.same_as_billing_address,
    ]);

    const handleSameAsBill = (e, type) => {
        const { checked } = e.target;
        formik.setFieldValue("party_details.same_as_billing_address", checked);
        if (checked) {
            formik.setFieldValue(
                "party_details.shipping_address.shipping_gstin",
                formik.values.party_details?.gstin
            );
            formik.setFieldValue(
                "party_details.shipping_address.shipping_name",
                formik.values.name
            );
            formik.setFieldValue("party_details.shipping_address.address_name", formik.values.name);
            formik.setFieldValue(
                "party_details.shipping_address.address_1",
                formik.values.party_details?.billing_address?.address_1
            );
            formik.setFieldValue(
                "party_details.shipping_address.address_2",
                formik.values.party_details?.billing_address?.address_2
            );
            formik.setFieldValue(
                "party_details.shipping_address.country_id",
                formik.values.party_details?.billing_address?.country_id
            );
            formik.setFieldValue(
                "party_details.shipping_address.state_id",
                formik.values.party_details?.billing_address?.state_id
            );
            formik.setFieldValue(
                "party_details.shipping_address.city_id",
                formik.values.party_details?.billing_address?.city_id
            );
            formik.setFieldValue(
                "party_details.shipping_address.pin_code",
                formik.values.party_details?.billing_address?.pin_code
            );
            formik.setFieldValue(
                "party_details.same_as_shipping_address.address_1",
                formik.values.party_details?.billing_address?.address_1
            );
            formik.setFieldValue(
                "party_details.same_as_shipping_address.address_2",
                formik.values.party_details?.billing_address?.address_2
            );
            formik.setFieldValue(
                "party_details.same_as_shipping_address.country_id",
                formik.values.party_details?.billing_address?.country_id
            );
            formik.setFieldValue(
                "party_details.same_as_shipping_address.state_id",
                formik.values.party_details?.billing_address?.state_id
            );
            formik.setFieldValue(
                "party_details.same_as_shipping_address.city_id",
                formik.values.party_details?.billing_address?.city_id
            );
            formik.setFieldValue(
                "party_details.same_as_shipping_address.pin_code",
                formik.values.party_details?.billing_address?.pin_code
            );
            formik.setFieldValue(
                "party_details.same_as_shipping_address.shipping_name",
                formik.values.party_details?.shipping_address?.shipping_name
            );
            formik.setFieldValue(
                "party_details.same_as_shipping_address.shipping_gstin",
                formik.values.party_details?.shipping_address?.shipping_gstin
            );
        } else if (type === "shipping" && !checked) {
            setCityId2("");
            setStateId2("");
            formik.setFieldValue("party_details.shipping_address.address_1", null);
            formik.setFieldValue("party_details.shipping_address.address_2", null);
            formik.setFieldValue(
                "party_details.shipping_address.country_id",
                formik.values?.party_details?.shipping_address?.country_id
            );
            formik.setFieldValue("party_details.shipping_address.state_id", companyState);
            formik.setFieldValue("party_details.shipping_address.city_id", null);
            formik.setFieldValue("party_details.shipping_address.pin_code", null);
            formik.setFieldValue("party_details.shipping_address.shipping_gstin", null),
            formik.setFieldValue("party_details.shipping_address.shipping_name", null);
            formik.setFieldValue("party_details.shipping_address.address_name", null);
        }
    };

    const handleOpenTransportModel = id => {
        openTransportModel();
        if (id) {
            dispatch(fetchTransportById(id));
            setTransporterDetail({
                transporter_id: id,
                modelType: true,
            });
        } else {
            setTransporterDetail({
                transporter_id: "",
                modelType: false,
            });
        }
    };

    const handleCloseLedgerModal = () => {
        formik.resetForm();
        formik.setFieldValue("bank_branch", "");
        formik.setFieldValue("bank_name", "");
        dispatch(ifscDetail([]));
        dispatch(getLedgerById([]));
        handleClose();
        dispatch(resetGst());
        setCountryId(101);
        setStateId("");
        setCityId("");
        setCountryId2(101);
        setStateId2("");
        setCityId2("");
        setGSTError("");
        setGSTError2("");
        if (gstQuote.party_ledger_id && !isBankDetails) {
            dispatch(fetchShippingAddressList(parseFloat(gstQuote.party_ledger_id), shipping_address_type, id));
        }
        if (!isShippingAddressModel) {
            setPartyAddress({
                ...partyAddress,
                shippingAddress: {
                    ...partyAddress?.shippingAddress,
                    shipping_address_id: transactionShippingAddressId,
                },
            });
        }
        setNotExistParty("");
        setIsCheckGstType(true);
    };

    const changeGstNumber = async e => {
        const { value } = e.target;
        const upperValue = value.trim().toUpperCase();
        formik.setFieldValue("party_details.gstin", upperValue);

        if (upperValue.length === 15 && CheckGstValidate(upperValue)) {
            dispatch(fetchGstData(upperValue));
            const response = await checkGstNumberAlreadyUsed(upperValue, name.id);
            if (response?.is_not_unique_gst_number) {
                setShowGstNotUnique(true);
                setGstNotUniqueMessage(response?.massage);
            }
            setGSTError(""); // Clear error on valid GSTIN
        } else if (upperValue.length > 0 && upperValue.length !== 15) {
            setGSTError("GSTIN must be exactly 15 characters long");
        } else if (upperValue.length === 0) {
            formik.setFieldValue("tax_details.pan", "");
            formik.setFieldValue("tax_details.type_of_entity", "");
            const defaultGstOption = ledgerGstOption?.find(
                option => option?.label == "Unregistered"
            );
            if (defaultGstOption) {
                formik.setFieldValue("tax_details.gst_registration_type", defaultGstOption?.value);
            }
            setGSTError(""); // Clear error if input is cleared
        } else {
            setGSTError("Please enter a valid GSTIN");
        }
    };

    const changeShippingGstNumber = e => {
        const { value } = e.target;
        const upperValue = value.toUpperCase();
        formik.setFieldValue("party_details.shipping_address.shipping_gstin", upperValue);

        if (upperValue.length === 15 && CheckGstValidate(upperValue)) {
            dispatch(fetchShippingGstData(upperValue));
            setGSTError2(""); // Clear error on valid GSTIN
        } else if (upperValue.length > 0 && upperValue.length !== 15) {
            setGSTError2("GSTIN must be exactly 15 characters long");
        } else if (upperValue.length === 0) {
            setGSTError2(""); // Clear error if input is cleared
        } else {
            setGSTError2("Please enter a valid GSTIN");
        }
    };

    const changePanDetail = e => {
        const { value, selectionStart } = e.target;
        const entity_type = value.slice(3, 4);
        dispatch(fetchEntityData(entity_type, formik, "tax_details.type_of_entity"));
        formik.setFieldValue("tax_details.pan", value?.toUpperCase());
        formik.setFieldValue("tax_details.type_of_entity_character", entity_type);

        setTimeout(() => {
            e.target.selectionStart = e.target.selectionEnd = selectionStart;
        }, 0);
    };
    const changeTanDetail = e => {
        const { value } = e.target;
        formik.setFieldValue("tax_details.tan", value);
    };

    const changeCinDetail = e => {
        const { value } = e.target;
        formik.setFieldValue("tax_details.cin_number", value);
    };

    const handleBrokerModel = () => {
        openBrokerModel();
        dispatch(getBrokerModelDetail());
    };

    const changeIfscCode = e => {
        const { value } = e.target;
        formik.setFieldValue("ifsc_code", value);
        if (value) {
            if (typingTimeout) {
                clearTimeout(typingTimeout);
            }
            setTypingTimeout(
                setTimeout(() => dispatch(fetchBankDetail(value)), 500)
            );
        } else {
            dispatch(ifscDetail(""));
            formik.setFieldValue("bank_branch", "");
            formik.setFieldValue("bank_name", "");
        }
    };

    const handleOpenShippingAddressModal = () => {
        openShippingFromModel();
        setIsOpenShippingAddress(true);
    };

    const handleCreditLimitChange = event => {
        formik.setFieldValue("other_details.allow_credit_limit", event);
    };
    const changeCreditLimitAction = event => {
        formik.setFieldValue("other_details.credit_limit_action", event);
    };

    const handleChangeTaxType = e => {
        setIsUpdateTaxType(true);
        formik.setFieldValue("tax_type", e.value);
    };

    const changeBrokerDetail = e => {
        if (e.value) {
            formik.setFieldValue("other_details.broker", e.value);
            dispatch(fetchBrokerDetailsById(e.value, formik, isPurchase));
        } else {
            formik.setFieldValue("other_details.broker", "");
            formik.setFieldValue("other_details.brokerage_on_value", "");
            formik.setFieldValue("other_details.brokerage_percentage", "");
        }
    };

    const handleWebsiteChange = e => {
        const { value } = e.target;
        formik.setFieldValue("contact_details.contact_person_website", value);
        formik.setFieldValue("party_details.contact_person_website", value);
    };

    useEffect(() => {
        if (
            formik.values.parent_action &&
            ledgerGroupType &&
            formik.values.group_id &&
            (!formik?.values?.id || isChangeGroupType)
        ) {
            let defaultOption = null;

            if (formik.values.parent_action === "Expense") {
                defaultOption = ledgerGroupType?.find(option => option.label === "Purchase");
            } else if (formik.values.parent_action === "Income") {
                defaultOption = ledgerGroupType?.find(option => option.label === "Sales");
            }

            if (defaultOption) {
                formik.setFieldValue("income_type", defaultOption.value);
                formik.setFieldValue("expense_type", defaultOption.value);
            }
        }
    }, [formik.values.parent_action, ledgerGroupType, formik.values.group_id]);

    const fetchTcsTdsTaxDetail = async () => {
        let response = null;
        if (formik.values.parent_action.includes("TCS")) {
            response = await dispatch(fetchTcsRateDetail(formik.values.tax_type));
        } else {
            response = await dispatch(fetchTdsRateDetail(formik.values.tax_type));
        }
        formik.setFieldValue("individual_huf", response?.for_individual_huf ?? 0);
        formik.setFieldValue("for_other", response?.for_other ?? 0);
        formik.setFieldValue("pan_not_given", response?.pan_not_given ?? 0);
        formik.setFieldValue("individual_bill_wise", response?.individual_bill_wise ?? 0);
    };

    const handleChangeAddressName = e => {
        const { value } = e.target;
        formik.setFieldValue("party_details.shipping_address.shipping_name", value);
        if (formik.values?.party_details?.shipping_address?.party_name_same_as_address_name) {
            formik.setFieldValue("party_details.shipping_address.address_name", value);
        }
    };

    const handleSameAsAddressName = e => {
        const { checked } = e.target;
        formik.setFieldValue(
            "party_details.shipping_address.address_name",
            checked ? formik.values.party_details.shipping_address.shipping_name || "" : ""
        );
        formik.setFieldValue(
            "party_details.shipping_address.party_name_same_as_address_name",
            checked
        );
    };

    useEffect(() => {
        if (formik.values.tax_type && isUpdateTaxType) {
            fetchTcsTdsTaxDetail();
        }
    }, [formik.values.tax_type]);

    useEffect(() => {
        if (formik.values.parent_action == "Stock in hand") {
            const formattedDates = multiDate?.map(date =>
                moment(date, "DD-MM-YYYY").format("YYYY-MM-DD")
            );
            dispatch(fetchItemStockList(formik.values.id, formattedDates[0], formattedDates[1]));
        }
    }, [formik.values.parent_action, multiDate]);

    useEffect(() => {
        if (ledger?.itemStockDetail) {
            const response = ledger?.itemStockDetail?.stocks;
            const openingStock = ledger?.itemStockDetail?.opening_balance;

            setOpeningItemStock(openingStock?.amount);
            setItemStockList(response);
        }
    }, [ledger?.itemStockDetail, multiDate]);

    const handleDateChange = date => {
        if (!date || date.length === 0) return;
        const DateFormatted = [
            moment(date[0]).format("DD-MM-YYYY"),
            date[1] ? moment(date[1]).format("DD-MM-YYYY") : null,
        ].filter(Boolean);
        setMultiDate(DateFormatted);
    };

    const handleOpenStockModel = data => {
        openStockModel();
        setEditStockDetail(data);
    };

    const handleDeleteStockModel = data => {
        setIsDeleteItemStock(true);
        setEditStockDetail(data);
    };

    const handleChangeLedgerName = e => {
        const capitalizedValue = e?.target?.value;
        formik.setFieldValue("name", capitalizedValue);
        setNameCount(capitalizedValue?.length);
        if (formik.values.party_details?.same_as_billing_address) {
            formik.setFieldValue("party_details.shipping_address.shipping_name", capitalizedValue);
        }
    };


    const handleCheckboxChange = (e) => {
        const check_opening_balance_used = formik.values.bill_wise_opening_balance_details?.filter((balance) => !balance?.is_editable);
        if(check_opening_balance_used?.length > 0) {
            dispatch(
                errorToast({
                    text: "This billed cannot be deleted because it is already used in transactions.",
                    type: toastType.ERROR,
                })
            )
            return
        }
        formik.setFieldValue("opening_balance_details.bill_wise", e.target.checked ? 1 : 0);
        formik.setFieldValue("opening_balance_details.bill_wise_opening_balance_details", Array(1).fill({
            voucher_number: "",
            voucher_date: "",
            due_date: "",
            transaction_type: null,
            total_amount: "",
            received_amount: "",
            pending_amount: "",
        }));
    };
    const handleAddRow = () => {
        const newRow = {
                voucher_number: "",
                voucher_date: "",
                due_date: "",
                transaction_type: null,
                total_amount: "",
                received_amount: "",
                pending_amount: "",
                is_editable: true
        }
        const updatedData = [...formik.values.bill_wise_opening_balance_details, newRow];
        formik.setFieldValue("bill_wise_opening_balance_details", updatedData);
        originalDataRef.current = [...originalDataRef.current, newRow];
    };
    const handleDeleteRow = (deleteIndex, event) => {
        event.preventDefault();

        const updatedData = formik.values.bill_wise_opening_balance_details.filter((_, index) => index !== deleteIndex);
        formik.setFieldValue("bill_wise_opening_balance_details", updatedData);
        const updatedRows = selectedRows
          .filter(index => index !== deleteIndex) // remove the deleted row
          .map(index => (index > deleteIndex ? index - 1 : index)); // shift remaining

        setSelectedRows(updatedRows);
      };

    const handleSelectChange = (selected, index) => {
        const updatedRows = formik.values.bill_wise_opening_balance_details.map((row, i) =>
            i === index ? { ...row, transaction_type: selected.value } : { ...row }
        );
        formik.setFieldValue("bill_wise_opening_balance_details", updatedRows);
    };

    useEffect(() => {
        setPendingAmount(formik.values.bill_wise_opening_balance_details.reduce((total, item) => total + (item.pending_amount || 0), 0));
        setReceivedAmount(formik.values.bill_wise_opening_balance_details.reduce((total, item) => total + (formik.values.parent_action?.toLowerCase() === "supplier" ? item.paid_amount || 0 : item.received_amount || 0), 0));
        setTotalAmount(formik.values.bill_wise_opening_balance_details.reduce((total, item) => total + (item.total_amount || 0), 0));
        const DrAmount = formik.values.bill_wise_opening_balance_details.filter((item) => [1,3,6,7].includes(item.transaction_type)).reduce((total, item) => total + item.pending_amount, 0);
        const CrAmount = formik.values.bill_wise_opening_balance_details.filter((item) => [2,4,5,8].includes(item.transaction_type)).reduce((total, item) => total + item.pending_amount, 0);
        if(DrAmount > CrAmount){
            setDrCrAmount(DrAmount - CrAmount)
            setIsShowDrCr("Dr")
            formik.setFieldValue("opening_balance_details.opening_balance_dr_cr", 1)
        }else{
            setDrCrAmount(CrAmount - DrAmount)
            setIsShowDrCr(CrAmount - DrAmount == 0 ? "Dr" : "Cr")
            formik.setFieldValue("opening_balance_details.opening_balance_dr_cr", CrAmount - DrAmount == 0 ? 1 : 2)
        }
    }, [formik.values.bill_wise_opening_balance_details])

    const handleBillWiseChange = (e, index, type) => {
        const { name, value } = e.target;
        if (type === "received_amount" || type === "paid_amount") {
            if (formik.values.bill_wise_opening_balance_details[index].total_amount >= value) {
                formik.setFieldValue(name, parseFloat(value || ""));
                formik.setFieldValue(
                    `bill_wise_opening_balance_details[${index}].pending_amount`, formik.values.bill_wise_opening_balance_details[index].total_amount - value,
                );
            }
        } else if (type === "total_amount") {
            if(parseFloat(value) >= parseFloat(formik.values.bill_wise_opening_balance_details[index].received_amount)){
                formik.setFieldValue(
                    `bill_wise_opening_balance_details[${index}].pending_amount`,
                    value - formik.values.bill_wise_opening_balance_details[index].received_amount
                );
            }else{
                formik.setFieldValue(
                    `bill_wise_opening_balance_details[${index}].pending_amount`,
                    0
                );
                formik.setFieldValue(
                    `bill_wise_opening_balance_details[${index}].received_amount`,
                    0
                );
                formik.setFieldValue(
                    `bill_wise_opening_balance_details[${index}].paid_amount`,
                    0
                );
            }
            formik.setFieldValue(name, parseFloat(value || ""));
        } else if(name?.includes("voucher_number")) {
            formik.setFieldValue(name, value || "");
        }else{
            formik.setFieldValue(name, parseFloat(value || ""));
        }
    };

    const handleSelectAll = (e) => {
        if (e.target.checked) {
            setSelectedRows(
                formik.values.bill_wise_opening_balance_details
                  .map((item, index) => ({ index, isEditable: item?.is_editable }))
                  .filter(item => item.isEditable)
                  .map(item => item.index)
              );
        } else {
            setSelectedRows([]);
        }
    };
    const handleRowSelect = (index) => {
        setSelectedRows((prevSelected) =>
            prevSelected.includes(index)
                ? prevSelected.filter((i) => i !== index)
                : [...prevSelected, index]
        );
    };
    const handleBulkDelete = () => {
        setShowBulkDeleteModel(true)
    };
    const handleConfirmBulkDelete = () =>{
        setShowBulkDeleteModel(false)
        setSelectedRows([]);
        formik.setFieldValue("bill_wise_opening_balance_details", formik.values.bill_wise_opening_balance_details.filter((_, index) => !selectedRows.includes(index)));
        dispatch(
            addToast({
                text: "Record deleted successfully",
                type: toastType.ADD_TOAST,
            })
        )
    }
    const [isDataStore, setIsDataStore] = useState(false);
    const [filters, setFilters] = useState({
        voucher_number: '',
        voucher_date: '',
        due_date: '',
        transaction_type: '',
        total_amount: '',
        received_amount: '',
        pending_amount: ''
    });
    const originalDataRef = useRef(null);
    useEffect(() => {
        // Only set originalDataRef once (initial render)
        const allFiltersEmpty = Object.values(filters).every((val) => val.trim?.() === "");

        if (formik.values.bill_wise_opening_balance_details.length > 0 && isDataStore) {
          originalDataRef.current = [...formik.values.bill_wise_opening_balance_details];
          setIsDataStore(false);
        } else if(!originalDataRef.current?.length || allFiltersEmpty){
          originalDataRef.current = [...formik.values.bill_wise_opening_balance_details];
        }
      }, [formik.values.bill_wise_opening_balance_details, isDataStore]);

      const customFilter = (e, type) =>{
        const { value } = e.target;


    // Build the new filters manually, not relying on filters state directly
    const updatedFilters = {
      ...filters,
      [type]: value, // empty string allowed
    };

    setFilters(updatedFilters);

    const allEmpty = Object.values(updatedFilters).every(val => val.trim() === "");
    if (allEmpty) {
      formik.setFieldValue("bill_wise_opening_balance_details", originalDataRef.current);
      return;
    }
      setFilters(updatedFilters);
      const filteredData = originalDataRef.current.filter(item => {
        return Object.keys(updatedFilters).every((key) => {
          const filterVal = updatedFilters[key].toString().toLowerCase();
          if (!filterVal) return true;

        if (key === "transaction_type") {
            // Special handling for transaction_type
            const transaction = billWiseTransactionType.find(t => t.value === item[key]);
            const transactionLabel = transaction?.label?.toLowerCase() || '';
            return transactionLabel.includes(filterVal);
        } else {
            const itemVal = (item[key] || '').toString().toLowerCase();
            return itemVal.includes(filterVal);
        }
        });
      });

      formik.setFieldValue("bill_wise_opening_balance_details", filteredData);
  }

      const handleChangeDueDate = (date, index, type) =>{
        const isValidDate = date && !isNaN(new Date(date).getTime());
        const formattedDateValue = isValidDate ? formattedDate(new Date(date)) : "";
        if(type == "voucher"){
            formik.setFieldValue(`bill_wise_opening_balance_details.${index}.voucher_date`, formattedDateValue);
        }else{
            formik.setFieldValue(`bill_wise_opening_balance_details.${index}.due_date`, formattedDateValue);
        }
      }

      const handleRequireOpeningBalance = (row) => {
        const hasNonEmptyData = Object.entries(row)
        .filter(([key]) => key !== "is_editable")
        .some(([, value]) => value !== "" && value !== null);
        return hasNonEmptyData
      }

      const handleOpenCessRateModal = (id) => {
        setIsAddCessRate(true);
        setCessRateId(id);
    }

    return (
        <>
            <Modal
                show={show}
                onHide={handleCloseLedgerModal}
                className={`modal fade`}
                centered
                size="xl"
                backdrop={true}
            >
                <div className="modal-dialog-centered modal-xl">
                    <div className="modal-content">
                        <div className="modal-header py-3">
                            <h5 className="modal-title">{name.name}</h5>
                            <button
                                type="button"
                                className="btn btn-icon btn-sm btn-active-light-primary ms-2"
                                onClick={() => handleCloseLedgerModal(null)}
                            >
                                <Close />
                            </button>
                        </div>
                        <div className="modal-body">
                            <form onSubmit={formik.handleSubmit}>
                                <Row className="align-items-center">
                                    <Col xl={3} lg={4} md={6} sm={12} className="mb-4">
                                        <Form.Group className="position-relative form-floating-group">
                                            <FormInput
                                                ref={inputRef}
                                                className="floating-label-input capitalize pe-6"
                                                type="text"
                                                placeholder=""
                                                required
                                                name="name"
                                                value={formik.values.name}
                                                onChange={handleChangeLedgerName}
                                                onBlur={handleBlurItem}
                                                onFocus={handleFocusItem}
                                                maxLength={100}
                                                disabled={isDefault}
                                            />
                                            <Form.Label className="required">
                                                Ledger Name
                                            </Form.Label>
                                            <p
                                                className={`position-absolute bottom-0 right-0 mb-0 end-0 fs-12 pt-1 bootstrap-maxlength badge text-primary limit-textarea`}
                                            >
                                                {isDefault
                                                    ? ""
                                                    : isFocusedItem
                                                    ? 100 - nameCount || 0
                                                    : ""}
                                            </p>
                                        </Form.Group>
                                    </Col>
                                    <Col
                                        xl={3}
                                        lg={4}
                                        md={6}
                                        sm={12}
                                        className="mb-4 form-group-select"
                                    >
                                        <Form.Group>
                                            <div className="input-group flex-nowrap">
                                                <div className="position-relative h-40px w-100 pe-36px radius-r-0 focus-shadow">
                                                    <ReactSelect
                                                        options={
                                                            ledgerDetailOptions
                                                        }
                                                        placeholder={"Group"}
                                                        required={true}
                                                        name="group_id"
                                                        value={formik.values.group_id}
                                                        onChange={e => handleGroupChange(e)}
                                                        className="h-40px"
                                                        portal={true}
                                                        // isDisabled={isDefault}
                                                    />
                                                </div>
                                                <button
                                                    type="button"
                                                    className="input-group-text custom-group-text"
                                                    onClick={openItemCatModel}
                                                    // disabled={isDefault}
                                                >
                                                    <i className="fas fa-plus text-gray-900"></i>
                                                </button>
                                            </div>
                                        </Form.Group>
                                    </Col>
                                    <Col
                                        xl={3}
                                        lg={4}
                                        md={6}
                                        sm={12}
                                        className="mb-4 "
                                    >
                                        <div className="form-check form-check-custom mt-4 w-100">
                                            <input
                                                className="form-check-input me-2"
                                                type="checkbox"
                                                name="is_use_in_other_companies"
                                                id="isUseInOtherCompanies"
                                                checked={formik.values.is_use_in_other_companies == 1 ? true : false}
                                                onChange={e =>
                                                    formik.setFieldValue(
                                                        "is_use_in_other_companies",
                                                        e.target.checked ? 1 : 0
                                                    )
                                                }
                                            />
                                            <label
                                                htmlFor="isUseInOtherCompanies"
                                                className="form-label fs-6 fw-bolder text-gray-900 mb-0"
                                            >
                                                Use in other Companies
                                            </label>
                                        </div>
                                    </Col>
                                </Row>
                                <Row className="align-items-center">
                                    <>
                                        {formik.values.parent_action !== "Supplier" &&
                                            formik.values.parent_action !== "Customers" &&
                                            fields?.map((field, index) =>
                                                renderLedgerField({
                                                    name,
                                                    ledgerGroupType,
                                                    formik,
                                                    AccountTypeOptions,
                                                    roundOffOption,
                                                    Country,
                                                    State,
                                                    City,
                                                    brokerageOption,
                                                    ledgerEntityType,
                                                    changeIfscCode,
                                                    openLocationOfAssetModel,
                                                    isShowProfitRatio,
                                                    error,
                                                    handleHSNCode,
                                                    gstCessOptions,
                                                    gstOptions,
                                                    company,
                                                    descriptionCount,
                                                    setDescriptionCount,
                                                    isFocused,
                                                    setIsFocused,
                                                    handleChangeTaxType,
                                                    handleChangeLoanAdvancePan,
                                                    handleDateChange,
                                                    multiDate,
                                                    handleOpenStockModel,
                                                    openingItemStock,
                                                    itemStockList,
                                                    handleDeleteStockModel,
                                                    setSwiftCodeShow,
                                                    handleOpenCessRateModal
                                                })
                                            )}
                                    </>
                                </Row>
                                {(formik.values.parent_action === "Supplier" ||
                                    formik.values.parent_action === "Customers") && (
                                    <>
                                        <Row>
                                            <Col sm={12}>
                                                <h4 className="text-primary mb-4">Party Details</h4>
                                            </Col>
                                            <Col xl={3} lg={4} md={6} sm={12} className="mb-4">
                                                <Form.Group className="position-relative form-floating-group">
                                                    <FormInput
                                                        className="floating-label-input"
                                                        type="text"
                                                        placeholder=""
                                                        name="party_details.gstin"
                                                        value={
                                                            formik.values.party_details?.gstin ??
                                                            null
                                                        }
                                                        onChange={changeGstNumber}
                                                    />
                                                    <Form.Label>GSTIN</Form.Label>
                                                </Form.Group>
                                                {GSTError && (
                                                    <span className="text-danger">{GSTError}</span>
                                                )}
                                            </Col>
                                        </Row>
                                        <Row>
                                            <Col sm={12}>
                                                <h5 className="text-primary mb-4">
                                                    Billing Address
                                                </h5>
                                            </Col>
                                            <Col
                                                xl={6}
                                                lg={6}
                                                md={12}
                                                sm={12}
                                                className="px-3 mb-5"
                                            >
                                                <Form.Group className="position-relative form-floating-group">
                                                    <FormInput
                                                        className="floating-label-input capitalize"
                                                        type="text"
                                                        placeholder=""
                                                        name="party_details.billing_address.address_1"
                                                        value={
                                                            formik.values.party_details
                                                                ?.billing_address?.address_1
                                                        }
                                                        onChange={formik.handleChange}
                                                    />
                                                    <Form.Label>Address Line 1</Form.Label>
                                                </Form.Group>
                                            </Col>
                                            <Col
                                                xl={6}
                                                lg={6}
                                                md={12}
                                                sm={12}
                                                className="px-3 mb-5"
                                            >
                                                <Form.Group className="position-relative form-floating-group">
                                                    <FormInput
                                                        className="floating-label-input capitalize"
                                                        type="text"
                                                        placeholder=""
                                                        name="party_details.billing_address.address_2"
                                                        value={
                                                            formik.values.party_details
                                                                ?.billing_address?.address_2
                                                        }
                                                        onChange={formik.handleChange}
                                                    />
                                                    <Form.Label>Address Line 2</Form.Label>
                                                </Form.Group>
                                            </Col>
                                            <Col
                                                xl={3}
                                                lg={6}
                                                md={12}
                                                sm={12}
                                                className="ps-3 mb-5"
                                            >
                                                <div className="input-group flex-nowrap">
                                                    <div className="position-relative h-40px w-100 focus-shadow">
                                                        <Country
                                                            required={
                                                                company?.company?.is_gst_applicable
                                                                    ? true
                                                                    : false
                                                            }
                                                            formik={formik}
                                                            name="party_details.billing_address.country_id"
                                                        />
                                                    </div>
                                                </div>
                                            </Col>
                                            <Col
                                                xl={3}
                                                lg={6}
                                                md={12}
                                                sm={12}
                                                className="ps-3 mb-5"
                                            >
                                                <div className="input-group flex-nowrap">
                                                    <div className="position-relative h-40px w-100 focus-shadow">
                                                        <State
                                                            required={
                                                                company?.company?.is_gst_applicable
                                                                    ? true
                                                                    : false
                                                            }
                                                            formik={formik}
                                                            name="party_details.billing_address.state_id"
                                                            city_name="party_details.billing_address.city_id"
                                                            companyState={companyState}
                                                        />
                                                    </div>
                                                </div>
                                            </Col>
                                            <Col
                                                xl={3}
                                                lg={6}
                                                md={12}
                                                sm={12}
                                                className="ps-3 mb-5"
                                            >
                                                <div className="input-group flex-nowrap">
                                                    <div className="position-relative h-40px w-100 focus-shadow">
                                                        <City
                                                            formik={formik}
                                                            name="party_details.billing_address.city_id"
                                                        />
                                                    </div>
                                                </div>
                                            </Col>
                                            <Col
                                                xl={3}
                                                lg={6}
                                                md={12}
                                                sm={12}
                                                className="px-3 mb-5"
                                            >
                                                <Form.Group className="position-relative form-floating-group">
                                                    <FormInput
                                                        className="floating-label-input"
                                                        type="text"
                                                        placeholder=""
                                                        name="party_details.billing_address.pin_code"
                                                        value={
                                                            formik.values.party_details
                                                                ?.billing_address?.pin_code
                                                        }
                                                        onChange={formik.handleChange}
                                                    />
                                                    <Form.Label>Pincode</Form.Label>
                                                </Form.Group>
                                            </Col>
                                        </Row>
                                        <Row>
                                            <Col sm={12}>
                                                <Form.Group>
                                                    <div>
                                                        <div className="d-flex">
                                                            <h5 className="text-primary mb-1 pe-2">
                                                                Shipping Address
                                                            </h5>
                                                        </div>
                                                        <div className="form-check mb-5">
                                                            <input
                                                                type="checkbox"
                                                                id="dispatchAddress"
                                                                className="form-check-input"
                                                                value={
                                                                    formik.values.party_details
                                                                        ?.same_as_billing_address
                                                                }
                                                                checked={
                                                                    formik.values.party_details
                                                                        ?.same_as_billing_address ===
                                                                    true
                                                                }
                                                                onChange={e =>
                                                                    handleSameAsBill(e, "shipping")
                                                                }
                                                            />
                                                            <label
                                                                title=""
                                                                htmlFor="dispatchAddress"
                                                                className="form-check-label"
                                                            >
                                                                Same as Billing Address
                                                            </label>
                                                        </div>
                                                    </div>
                                                </Form.Group>
                                            </Col>
                                        </Row>
                                        {!formik.values.party_details?.same_as_billing_address ===
                                            true && (
                                            <>
                                                <Row className="justify-content-start">
                                                    <Col
                                                        xl={6}
                                                        lg={6}
                                                        md={12}
                                                        sm={12}
                                                        className="px-3 mb-5"
                                                    >
                                                        <Form.Group className="position-relative form-floating-group">
                                                            <FormInput
                                                                className="floating-label-input"
                                                                type="text"
                                                                placeholder=""
                                                                name="party_details.shipping_address.address_name"
                                                                value={
                                                                    formik.values.party_details
                                                                        ?.shipping_address
                                                                        ?.address_name ?? null
                                                                }
                                                                onChange={formik.handleChange}
                                                                disabled={
                                                                    formik.values.party_details
                                                                        ?.shipping_address
                                                                        ?.party_name_same_as_address_name
                                                                }
                                                            />
                                                            <Form.Label>Address Name</Form.Label>
                                                        </Form.Group>
                                                    </Col>
                                                    <Col>
                                                        <div className="form-check mt-2">
                                                            <input
                                                                type="checkbox"
                                                                id="address_name"
                                                                className="form-check-input"
                                                                value={
                                                                    formik.values.party_details
                                                                        ?.shipping_address
                                                                        ?.party_name_same_as_address_name
                                                                }
                                                                checked={
                                                                    Boolean(
                                                                        formik.values.party_details
                                                                            ?.shipping_address
                                                                            ?.party_name_same_as_address_name
                                                                    ) == true
                                                                }
                                                                onChange={handleSameAsAddressName}
                                                            />
                                                            <label
                                                                title=""
                                                                htmlFor="address_name"
                                                                className="form-check-label"
                                                            >
                                                                Same as Party Name
                                                            </label>
                                                        </div>
                                                    </Col>
                                                </Row>
                                                <Row className="justify-content-start">
                                                    {company?.company?.is_gst_applicable ? (
                                                        <Col
                                                            xl={6}
                                                            lg={6}
                                                            md={12}
                                                            sm={12}
                                                            className="px-3 mb-5"
                                                        >
                                                            <Form.Group className="position-relative form-floating-group">
                                                                <FormInput
                                                                    className="floating-label-input"
                                                                    type="text"
                                                                    placeholder=""
                                                                    name="party_details.shipping_address.shipping_gstin"
                                                                    value={
                                                                        formik.values.party_details
                                                                            ?.shipping_address
                                                                            ?.shipping_gstin ?? null
                                                                    }
                                                                    onChange={
                                                                        changeShippingGstNumber
                                                                    }
                                                                    minLength="15"
                                                                    maxLength="15"
                                                                />
                                                                <Form.Label>GSTIN</Form.Label>
                                                            </Form.Group>
                                                            {GSTError2 && (
                                                                <span className="text-danger">
                                                                    {GSTError2}
                                                                </span>
                                                            )}
                                                        </Col>
                                                    ) : null}
                                                    <Col
                                                        xl={6}
                                                        lg={6}
                                                        md={12}
                                                        sm={12}
                                                        className="px-3 mb-5"
                                                    >
                                                        <Form.Group className="position-relative form-floating-group">
                                                            <FormInput
                                                                className="floating-label-input capitalize"
                                                                type="text"
                                                                placeholder=""
                                                                name="party_details.shipping_address.shipping_name"
                                                                value={
                                                                    formik.values.party_details
                                                                        ?.shipping_address
                                                                        ?.shipping_name ?? null
                                                                }
                                                                onChange={handleChangeAddressName}
                                                            />
                                                            <Form.Label>Party Name</Form.Label>
                                                        </Form.Group>
                                                    </Col>
                                                    {!company?.company?.is_gst_applicable ? (
                                                        <Col
                                                            xl={6}
                                                            lg={6}
                                                            md={12}
                                                            sm={12}
                                                            className="px-3 mb-5"
                                                        ></Col>
                                                    ) : null}
                                                    <Col
                                                        xl={6}
                                                        lg={6}
                                                        md={12}
                                                        sm={12}
                                                        className="px-3 mb-5"
                                                    >
                                                        <Form.Group className="position-relative form-floating-group">
                                                            <FormInput
                                                                className="floating-label-input capitalize"
                                                                type="text"
                                                                placeholder=""
                                                                name="party_details.shipping_address.address_1"
                                                                value={
                                                                    formik.values.party_details
                                                                        ?.shipping_address
                                                                        ?.address_1
                                                                }
                                                                onChange={formik.handleChange}
                                                            />
                                                            <Form.Label>Address Line 1</Form.Label>
                                                        </Form.Group>
                                                    </Col>
                                                    <Col
                                                        xl={6}
                                                        lg={6}
                                                        md={12}
                                                        sm={12}
                                                        className="px-3 mb-5"
                                                    >
                                                        <Form.Group className="position-relative form-floating-group">
                                                            <FormInput
                                                                className="floating-label-input capitalize"
                                                                type="text"
                                                                placeholder=""
                                                                name="party_details.shipping_address.address_2"
                                                                value={
                                                                    formik.values.party_details
                                                                        ?.shipping_address
                                                                        ?.address_2
                                                                }
                                                                onChange={formik.handleChange}
                                                            />
                                                            <Form.Label>Address Line 2</Form.Label>
                                                        </Form.Group>
                                                    </Col>
                                                    <Col
                                                        xl={3}
                                                        lg={6}
                                                        md={12}
                                                        sm={12}
                                                        className="ps-3 mb-5"
                                                    >
                                                        <div className="input-group flex-nowrap">
                                                            <div className="position-relative h-40px w-100 focus-shadow">
                                                                <Country2
                                                                    formik={formik}
                                                                    required={
                                                                        company?.company
                                                                            ?.is_gst_applicable
                                                                            ? true
                                                                            : false
                                                                    }
                                                                    name="party_details.shipping_address.country_id"
                                                                />
                                                            </div>
                                                        </div>
                                                    </Col>
                                                    <Col
                                                        xl={3}
                                                        lg={6}
                                                        md={12}
                                                        sm={12}
                                                        className="ps-3 mb-5"
                                                    >
                                                        <div className="input-group flex-nowrap">
                                                            <div className="position-relative h-40px w-100 focus-shadow">
                                                                <State2
                                                                    formik={formik}
                                                                    required={
                                                                        company?.company
                                                                            ?.is_gst_applicable
                                                                            ? true
                                                                            : false
                                                                    }
                                                                    name="party_details.shipping_address.state_id"
                                                                    city_name="party_details.shipping_address.city_id"
                                                                    companyState={companyState}
                                                                />
                                                            </div>
                                                        </div>
                                                    </Col>
                                                    <Col
                                                        xl={3}
                                                        lg={6}
                                                        md={12}
                                                        sm={12}
                                                        className="ps-3 mb-5"
                                                    >
                                                        <div className="input-group flex-nowrap">
                                                            <div className="position-relative h-40px w-100 focus-shadow">
                                                                <City2
                                                                    formik={formik}
                                                                    name="party_details.shipping_address.city_id"
                                                                />
                                                            </div>
                                                        </div>
                                                    </Col>
                                                    <Col
                                                        xl={3}
                                                        lg={6}
                                                        md={12}
                                                        sm={12}
                                                        className="px-3 mb-5"
                                                    >
                                                        <Form.Group className="position-relative form-floating-group">
                                                            <FormInput
                                                                className="floating-label-input"
                                                                type="text"
                                                                placeholder=""
                                                                name="party_details.shipping_address.pin_code"
                                                                value={
                                                                    formik.values.party_details
                                                                        ?.shipping_address?.pin_code
                                                                }
                                                                onChange={formik.handleChange}
                                                            />
                                                            <Form.Label>Pincode</Form.Label>
                                                        </Form.Group>
                                                    </Col>
                                                </Row>
                                                {formik.values.id ? (
                                                    <button
                                                        type="button"
                                                        className="btn-sm btn-icon btn-icon-primary mb-2 add-item-btn"
                                                        onClick={handleOpenShippingAddressModal}
                                                    >
                                                        Manage Addresses
                                                    </button>
                                                ) : (
                                                    ""
                                                )}
                                            </>
                                        )}
                                        <Row>
                                            <Col sm={12}>
                                                <h4 className="text-primary mb-4">
                                                    Contact Details
                                                </h4>
                                            </Col>
                                            <Col
                                                xl={3}
                                                lg={6}
                                                md={12}
                                                sm={12}
                                                className="px-3 mb-5"
                                            >
                                                <Form.Group className="position-relative form-floating-group">
                                                    <FormInput
                                                        className="floating-label-input capitalize"
                                                        type="text"
                                                        placeholder=""
                                                        name="party_details.contact_person_name"
                                                        value={
                                                            formik.values.party_details
                                                                ?.contact_person_name
                                                        }
                                                        onChange={formik.handleChange}
                                                    />
                                                    <Form.Label>Contact Person Name</Form.Label>
                                                </Form.Group>
                                            </Col>
                                            <Col
                                                xl={3}
                                                lg={6}
                                                md={12}
                                                sm={12}
                                                className="px-3 mb-5"
                                            >
                                                <PhoneInput
                                                    country={"in"}
                                                    value={
                                                        formik.values.party_details
                                                            ?.contact_person_phone_input_1 || "+91"
                                                    }
                                                    onChange={(phone, code) =>
                                                        handlePhoneChange(phone, code)
                                                    }
                                                    countryCodeEditable={false}
                                                    containerClass="w-100"
                                                    inputClass="w-100 h-40px fw-500 focus-shadow"
                                                />
                                            </Col>
                                            <Col
                                                xl={3}
                                                lg={6}
                                                md={12}
                                                sm={12}
                                                className="px-3 mb-5"
                                            >
                                                <PhoneInput
                                                    country={"in"}
                                                    value={
                                                        formik.values.party_details
                                                            ?.contact_person_phone_input_2 || "+91"
                                                    }
                                                    countryCodeEditable={false}
                                                    onChange={(phone, code) =>
                                                        handlePhoneChange2(phone, code)
                                                    }
                                                    containerClass="w-100"
                                                    inputClass="w-100 h-40px fw-500 focus-shadow"
                                                />
                                            </Col>
                                            <Col
                                                xl={3}
                                                lg={6}
                                                md={12}
                                                sm={12}
                                                className="px-3 mb-5"
                                            >
                                                <Form.Group className="position-relative form-floating-group">
                                                    <FormInput
                                                        className="floating-label-input"
                                                        type="email"
                                                        placeholder=""
                                                        name="party_details.contact_person_email"
                                                        value={
                                                            formik.values.party_details
                                                                ?.contact_person_email
                                                        }
                                                        onChange={formik.handleChange}
                                                    />
                                                    <Form.Label>Email</Form.Label>
                                                </Form.Group>
                                            </Col>
                                        </Row>
                                        <Row>
                                            <Col sm={12}>
                                                <div
                                                    className="accordion"
                                                    id="accordionPanelsStayOpenExample"
                                                >
                                                    <div className="accordion-item">
                                                        <h2 className="accordion-header">
                                                            <button
                                                                className="accordion-button fw-bolder mb-5" //collapsed
                                                                type="button"
                                                                data-bs-toggle="collapse"
                                                                data-bs-target="#panelsStayOpen-collapseOne"
                                                                aria-expanded="false"
                                                                aria-controls="panelsStayOpen-collapseOne"
                                                            >
                                                                Tax Details
                                                            </button>
                                                        </h2>
                                                        <div
                                                            id="panelsStayOpen-collapseOne"
                                                            className="accordion-collapse collapse show"
                                                        >
                                                            <div className="accordion-body">
                                                                <Row className="align-items-center">
                                                                    <Col
                                                                        xl={3}
                                                                        lg={6}
                                                                        md={12}
                                                                        sm={12}
                                                                        className="px-3 mb-5"
                                                                    >
                                                                        <Form.Group className="position-relative form-floating-group">
                                                                            <FormInput
                                                                                className="uppercase floating-label-input"
                                                                                type="text"
                                                                                placeholder=""
                                                                                name="tax_details.pan"
                                                                                value={
                                                                                    formik.values
                                                                                        .tax_details
                                                                                        ?.pan || ""
                                                                                }
                                                                                onChange={
                                                                                    changePanDetail
                                                                                }
                                                                                maxLength="10"
                                                                                minLength="10"
                                                                            />
                                                                            <Form.Label>
                                                                                PAN
                                                                            </Form.Label>
                                                                        </Form.Group>
                                                                        {formik?.errors?.tax_details
                                                                            ?.pan &&
                                                                            formik?.touched
                                                                                ?.tax_details
                                                                                ?.pan && (
                                                                                <div className="text-danger position-absolute ps-1">
                                                                                    {
                                                                                        formik
                                                                                            ?.errors
                                                                                            ?.tax_details
                                                                                            ?.pan
                                                                                    }
                                                                                </div>
                                                                            )}
                                                                    </Col>
                                                                    <Col
                                                                        xl={3}
                                                                        lg={6}
                                                                        md={12}
                                                                        sm={12}
                                                                        className="px-3 mb-5"
                                                                    >
                                                                        <Form.Group>
                                                                            <div className="input-group flex-nowrap">
                                                                                <div className="position-relative h-40px w-100 focus-shadow">
                                                                                    <ReactSelect
                                                                                        name="tax_details
                                                                                    .type_of_entity"
                                                                                        value={
                                                                                            formik
                                                                                                .values
                                                                                                .tax_details
                                                                                                ?.type_of_entity
                                                                                        }
                                                                                        onChange={e =>
                                                                                            formik.setFieldValue(
                                                                                                "tax_details.type_of_entity",
                                                                                                e.value
                                                                                            )
                                                                                        }
                                                                                        placeholder="Type of Entity"
                                                                                        options={
                                                                                            entityType
                                                                                        }
                                                                                        isCreatable={
                                                                                            false
                                                                                        }
                                                                                        position={
                                                                                            "top"
                                                                                        }
                                                                                    />
                                                                                </div>
                                                                            </div>
                                                                        </Form.Group>
                                                                    </Col>
                                                                    {formik.values.tax_details
                                                                        ?.type_of_entity_character ==
                                                                    "C" ? (
                                                                        <Col
                                                                            xl={3}
                                                                            lg={6}
                                                                            md={12}
                                                                            sm={12}
                                                                            className="px-3 mb-5"
                                                                        >
                                                                            <Form.Group className="position-relative form-floating-group">
                                                                                <FormInput
                                                                                    className="floating-label-input"
                                                                                    type="text"
                                                                                    placeholder=""
                                                                                    name="tax_details.cin_number"
                                                                                    value={
                                                                                        formik
                                                                                            .values
                                                                                            .tax_details
                                                                                            ?.cin_number
                                                                                    }
                                                                                    onChange={
                                                                                        changeCinDetail
                                                                                    }
                                                                                />
                                                                                <Form.Label>
                                                                                    CIN
                                                                                </Form.Label>
                                                                            </Form.Group>
                                                                        </Col>
                                                                    ) : null}
                                                                    {/* {company?.company
                                                                        ?.is_gst_applicable ? ( */}
                                                                    <Col
                                                                        xl={3}
                                                                        lg={6}
                                                                        md={12}
                                                                        sm={12}
                                                                        className="px-3 mb-5 w-full"
                                                                    >
                                                                        <Form.Group>
                                                                            <div className="input-group flex-nowrap">
                                                                                <div className="position-relative h-40px w-100 focus-shadow">
                                                                                    <ReactSelect
                                                                                        name="tax_details
                                                                            .gst_registration_type"
                                                                                        value={
                                                                                            formik
                                                                                                .values
                                                                                                .tax_details
                                                                                                ?.gst_registration_type
                                                                                        }
                                                                                        required={
                                                                                            true
                                                                                        }
                                                                                        onChange={e =>
                                                                                            formik.setFieldValue(
                                                                                                "tax_details.gst_registration_type",
                                                                                                e.value
                                                                                            )
                                                                                        }
                                                                                        placeholder="GST Registration Type"
                                                                                        options={
                                                                                            gstRegistrationType
                                                                                        }
                                                                                        isCreatable={
                                                                                            false
                                                                                        }
                                                                                    />
                                                                                </div>
                                                                            </div>
                                                                        </Form.Group>
                                                                    </Col>
                                                                    {/* ) : null} */}
                                                                    {company?.company
                                                                        ?.is_gst_applicable &&
                                                                    formik.values.parent_action ===
                                                                        "Supplier" ? (
                                                                        <Col
                                                                            xl={3}
                                                                            lg={6}
                                                                            md={12}
                                                                            sm={12}
                                                                            className="px-3 mb-5 w-full"
                                                                        >
                                                                            <Form.Group className="position-relative form-floating-group">
                                                                                <FormInput
                                                                                    className="floating-label-input"
                                                                                    type="text"
                                                                                    placeholder=""
                                                                                    name="tax_details.gst_return_status"
                                                                                    value={
                                                                                        formik
                                                                                            .values
                                                                                            .tax_details
                                                                                            ?.gst_return_status
                                                                                    }
                                                                                    onChange={
                                                                                        formik.handleChange
                                                                                    }
                                                                                />
                                                                                <Form.Label>
                                                                                    GST Return
                                                                                    Status
                                                                                </Form.Label>
                                                                            </Form.Group>
                                                                        </Col>
                                                                    ) : null}{" "}
                                                                    {formik.values.parent_action ===
                                                                        "Supplier" &&
                                                                    company?.company
                                                                        ?.is_tds_applicable ? (
                                                                        <Col
                                                                            xl={3}
                                                                            lg={6}
                                                                            md={12}
                                                                            sm={12}
                                                                            className="px-3 mb-5"
                                                                        >
                                                                            <label className="form-label fs-6 fw-bolder mb-0">
                                                                                TDS Applicable ?
                                                                            </label>
                                                                            <div className="ms-auto d-flex">
                                                                                <div className="form-check mx-2 mt-1 align-content-center">
                                                                                    <label
                                                                                        className="form-label mb-0"
                                                                                        htmlFor="Yes"
                                                                                    >
                                                                                        Yes
                                                                                    </label>
                                                                                    <input
                                                                                        className="form-check-input"
                                                                                        id="Yes"
                                                                                        name="tax_details.is_tds_applicable"
                                                                                        value={true}
                                                                                        checked={
                                                                                            formik
                                                                                                .values
                                                                                                .tax_details
                                                                                                ?.is_tds_applicable ===
                                                                                            true
                                                                                        }
                                                                                        onChange={() =>
                                                                                            formik.setFieldValue(
                                                                                                "tax_details.is_tds_applicable",
                                                                                                true
                                                                                            )
                                                                                        }
                                                                                        type="radio"
                                                                                    />
                                                                                </div>
                                                                                <div className="form-check mx-2 mt-1 align-content-center">
                                                                                    <label
                                                                                        className="form-label mb-0"
                                                                                        htmlFor="No"
                                                                                    >
                                                                                        No
                                                                                    </label>
                                                                                    <input
                                                                                        className="form-check-input"
                                                                                        id="No"
                                                                                        name="tax_details.is_tds_applicable"
                                                                                        value={
                                                                                            false
                                                                                        }
                                                                                        checked={
                                                                                            formik
                                                                                                .values
                                                                                                .tax_details
                                                                                                ?.is_tds_applicable ===
                                                                                            false
                                                                                        }
                                                                                        onChange={() =>
                                                                                            formik.setFieldValue(
                                                                                                "tax_details.is_tds_applicable",
                                                                                                false
                                                                                            )
                                                                                        }
                                                                                        type="radio"
                                                                                    />
                                                                                </div>
                                                                            </div>
                                                                        </Col>
                                                                    ) : null}
                                                                </Row>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="accordion-item">
                                                        <h2 className="accordion-header">
                                                            <button
                                                                className="accordion-button collapsed fw-bolder mb-5"
                                                                type="button"
                                                                data-bs-toggle="collapse"
                                                                data-bs-target="#panelsStayOpen-collapseTwo"
                                                                aria-expanded="false"
                                                                aria-controls="panelsStayOpen-collapseTwo"
                                                            >
                                                                Other Details
                                                            </button>
                                                        </h2>
                                                        <div
                                                            id="panelsStayOpen-collapseTwo"
                                                            className="accordion-collapse collapse show"
                                                        >
                                                            <div className="accordion-body">
                                                                <Row className="align-items-center">
                                                                    <Col
                                                                        xl={3}
                                                                        lg={6}
                                                                        md={12}
                                                                        sm={12}
                                                                        className="mb-5"
                                                                    >
                                                                        <Form.Group className="position-relative form-floating-group h-40px">
                                                                            <FormInput
                                                                                className="floating-label-input h-100"
                                                                                type="number"
                                                                                min={1}
                                                                                placeholder=""
                                                                                name="other_details.credit_period"
                                                                                value={
                                                                                    formik.values
                                                                                        .other_details
                                                                                        ?.credit_period
                                                                                }
                                                                                onChange={
                                                                                    formik.handleChange
                                                                                }
                                                                            />
                                                                            <Form.Label>
                                                                                Credit Period
                                                                            </Form.Label>
                                                                            <div className="w-fit-content bg-light position-absolute credit-dropdown input-group d-block">
                                                                                <ReactSelect
                                                                                    defaultValue={1}
                                                                                    options={[
                                                                                        {
                                                                                            label: "Month",
                                                                                            value: 1,
                                                                                        },
                                                                                        {
                                                                                            label: "Day",
                                                                                            value: 2,
                                                                                        },
                                                                                    ]}
                                                                                    name="other_details.credit_period_type"
                                                                                    value={
                                                                                        formik
                                                                                            .values
                                                                                            .other_details
                                                                                            ?.credit_period_type
                                                                                    }
                                                                                    onChange={e =>
                                                                                        formik.setFieldValue(
                                                                                            "other_details.credit_period_type",
                                                                                            e.value
                                                                                        )
                                                                                    }
                                                                                    placeholder=""
                                                                                    isCreatable={
                                                                                        false
                                                                                    }
                                                                                    showborder={
                                                                                        false
                                                                                    }
                                                                                    showbg={true}
                                                                                    height="32px"
                                                                                />
                                                                            </div>
                                                                        </Form.Group>
                                                                    </Col>
                                                                </Row>
                                                                <Row>
                                                                    <Col
                                                                        xl={3}
                                                                        lg={6}
                                                                        md={12}
                                                                        sm={12}
                                                                        className="mb-5 form-group-select"
                                                                    >
                                                                        <Form.Group>
                                                                            <div className="input-group flex-nowrap">
                                                                                <div className="position-relative h-40px w-100 pe-36px focus-shadow">
                                                                                    <ReactSelect
                                                                                        customLabel="party"
                                                                                        options={
                                                                                            brokerOptions
                                                                                        }
                                                                                        name="other_details.broker"
                                                                                        value={
                                                                                            formik
                                                                                                .values
                                                                                                .other_details
                                                                                                ?.broker
                                                                                        }
                                                                                        onChange={
                                                                                            changeBrokerDetail
                                                                                        }
                                                                                        placeholder={
                                                                                            "Broker Name"
                                                                                        }
                                                                                        defaultLabel={
                                                                                            "Select Broker"
                                                                                        }
                                                                                        islabel={
                                                                                            true
                                                                                        }
                                                                                        portal={
                                                                                            true
                                                                                        }
                                                                                        radius={
                                                                                            true
                                                                                        }
                                                                                        position={
                                                                                            "top"
                                                                                        }
                                                                                        className="h-40px"
                                                                                    />
                                                                                </div>
                                                                                <button
                                                                                    type="button"
                                                                                    className="input-group-text custom-group-text"
                                                                                    onClick={
                                                                                        handleBrokerModel
                                                                                    }
                                                                                >
                                                                                    <i className="fas fa-plus text-gray-900"></i>
                                                                                </button>
                                                                            </div>
                                                                        </Form.Group>
                                                                    </Col>
                                                                    <Col
                                                                        xl={3}
                                                                        lg={6}
                                                                        md={12}
                                                                        sm={12}
                                                                        className="mb-5"
                                                                    >
                                                                        <Form.Group>
                                                                            <div className="input-group flex-nowrap">
                                                                                <div className="position-relative h-40px w-100 form-floating-group">
                                                                                    <FormInput
                                                                                        className="position-relative floating-label-input h-100"
                                                                                        type="number"
                                                                                        step="0.01"
                                                                                        max={100}
                                                                                        placeholder=""
                                                                                        name="other_details.brokerage_percentage"
                                                                                        value={
                                                                                            formik
                                                                                                .values
                                                                                                .other_details
                                                                                                ?.brokerage_percentage
                                                                                        }
                                                                                        onChange={
                                                                                            formik.handleChange
                                                                                        }
                                                                                    />
                                                                                    <Form.Label>
                                                                                        Brokerage
                                                                                        Percentage
                                                                                    </Form.Label>
                                                                                </div>
                                                                                <button
                                                                                    className="input-group-text custom-group-text"
                                                                                    type="button"
                                                                                >
                                                                                    <i className="fas fa-percentage text-gray-900"></i>
                                                                                </button>
                                                                            </div>
                                                                        </Form.Group>
                                                                    </Col>
                                                                    <Col
                                                                        xl={3}
                                                                        lg={6}
                                                                        md={12}
                                                                        sm={12}
                                                                        className="mb-5 form-group-select"
                                                                    >
                                                                        <Form.Group>
                                                                            <div className="input-group flex-nowrap">
                                                                                <div className="position-relative h-40px w-100 focus-shadow">
                                                                                    <ReactSelect
                                                                                        options={
                                                                                            brokerageOption
                                                                                        }
                                                                                        placeholder={
                                                                                            "Brokerage On Value"
                                                                                        }
                                                                                        name="other_details.brokerage_on_value"
                                                                                        value={
                                                                                            formik
                                                                                                .values
                                                                                                .other_details
                                                                                                ?.brokerage_on_value
                                                                                        }
                                                                                        onChange={e =>
                                                                                            formik.setFieldValue(
                                                                                                "other_details.brokerage_on_value",
                                                                                                e.value
                                                                                            )
                                                                                        }
                                                                                        islabel={
                                                                                            true
                                                                                        }
                                                                                        portal={
                                                                                            true
                                                                                        }
                                                                                        className="h-40px"
                                                                                    />
                                                                                </div>
                                                                            </div>
                                                                        </Form.Group>
                                                                    </Col>
                                                                </Row>
                                                                <Row>
                                                                    <Col
                                                                        xl={3}
                                                                        lg={6}
                                                                        md={12}
                                                                        sm={12}
                                                                        className="mb-5 form-group-select"
                                                                    >
                                                                        <Form.Group>
                                                                            <div className="input-group flex-nowrap">
                                                                                <div className="position-relative h-40px w-100 pe-36px focus-shadow">
                                                                                    <ReactSelect
                                                                                        options={
                                                                                            transportOptions
                                                                                        }
                                                                                        customLabel="party"
                                                                                        placeholder={
                                                                                            "Transporter Name"
                                                                                        }
                                                                                        defaultLabel={
                                                                                            "Select Transporter"
                                                                                        }
                                                                                        name="other_details.transporter"
                                                                                        value={
                                                                                            formik
                                                                                                .values
                                                                                                .other_details
                                                                                                ?.transporter
                                                                                        }
                                                                                        onChange={e =>
                                                                                            formik.setFieldValue(
                                                                                                "other_details.transporter",
                                                                                                e.value
                                                                                            )
                                                                                        }
                                                                                        islabel={
                                                                                            true
                                                                                        }
                                                                                        portal={
                                                                                            true
                                                                                        }
                                                                                        radius={
                                                                                            true
                                                                                        }
                                                                                        position={
                                                                                            "top"
                                                                                        }
                                                                                        className="h-40px"
                                                                                        handleOpen={
                                                                                            handleOpenTransportModel
                                                                                        }
                                                                                    />
                                                                                </div>
                                                                                <button
                                                                                    type="button"
                                                                                    className="input-group-text custom-group-text"
                                                                                    onClick={() =>
                                                                                        handleOpenTransportModel(
                                                                                            ""
                                                                                        )
                                                                                    }
                                                                                >
                                                                                    <i className="fas fa-plus text-gray-900"></i>
                                                                                </button>
                                                                            </div>
                                                                        </Form.Group>
                                                                    </Col>
                                                                    <Col
                                                                        xl={3}
                                                                        lg={6}
                                                                        md={12}
                                                                        sm={12}
                                                                        className="mb-5"
                                                                    >
                                                                        <Form.Group className="position-relative form-floating-group">
                                                                            <OverlayTrigger
                                                                                placement="bottom"
                                                                                overlay={
                                                                                    <Tooltip id="file-upload-tooltip">
                                                                                        Maximum file
                                                                                        size is 2
                                                                                        MB.
                                                                                    </Tooltip>
                                                                                }
                                                                            >
                                                                                <FormInput
                                                                                    className="floating-label-input file-upload-validate mb-2"
                                                                                    data-bs-toggle="tooltip"
                                                                                    data-bs-placement="bottom"
                                                                                    title=""
                                                                                    name="sale_document"
                                                                                    type="file"
                                                                                    data-bs-original-title="Maximum file size is 2 MB."
                                                                                    aria-label="Maximum file size is 2 MB."
                                                                                    // onChange={(
                                                                                    //     e
                                                                                    // ) =>
                                                                                    //     setCharges(
                                                                                    //         {
                                                                                    //             ...charges,
                                                                                    //             upload_document:
                                                                                    //                 e
                                                                                    //                     .currentTarget
                                                                                    //                     .files[0],
                                                                                    //         }
                                                                                    //     )
                                                                                    // }
                                                                                    multiple
                                                                                    onChange={
                                                                                        validateFiles
                                                                                    }
                                                                                    accept=".jpg,.jpeg,.png,.pdf,.xlsx,.docx"
                                                                                />
                                                                            </OverlayTrigger>
                                                                            <Form.Label
                                                                                className="upload-document"
                                                                                htmlFor="sale_document"
                                                                            >
                                                                                Upload Document
                                                                            </Form.Label>
                                                                        </Form.Group>
                                                                    </Col>
                                                                    <Col
                                                                        xl={6}
                                                                        sm={12}
                                                                        className="mb-5"
                                                                    >
                                                                        <DocumentModal
                                                                            medias={
                                                                                formik.values
                                                                                    ?.other_details
                                                                                    ?.upload_document
                                                                            }
                                                                            formik={formik}
                                                                        />
                                                                    </Col>
                                                                </Row>
                                                                {formik.values.parent_action !==
                                                                "Supplier" ? (
                                                                    <Row className="align-items-center">
                                                                        <Col
                                                                            xl={3}
                                                                            lg={6}
                                                                            md={12}
                                                                            sm={12}
                                                                            className="mb-5"
                                                                        >
                                                                            <label
                                                                                htmlFor="itemType"
                                                                                className="form-label fs-6 fw-bolder text-gray-900 mb-1"
                                                                            >
                                                                                Allow Credit Limit
                                                                            </label>
                                                                            <div className="d-flex align-items-center mt-0">
                                                                                <div className="form-check mx-2">
                                                                                    <label
                                                                                        className="form-label mb-0 text-gray-900"
                                                                                        htmlFor="limitYes"
                                                                                    >
                                                                                        Yes
                                                                                    </label>
                                                                                    <input
                                                                                        className="form-check-input item-type valid-item-type"
                                                                                        id="limitYes"
                                                                                        name="other_details.allow_credit_limit"
                                                                                        type="radio"
                                                                                        checked={
                                                                                            formik
                                                                                                .values
                                                                                                .other_details
                                                                                                ?.allow_credit_limit ===
                                                                                            1
                                                                                        }
                                                                                        value={
                                                                                            formik
                                                                                                .values
                                                                                                .other_details
                                                                                                ?.allow_credit_limit
                                                                                        }
                                                                                        onChange={() =>
                                                                                            handleCreditLimitChange(
                                                                                                1
                                                                                            )
                                                                                        }
                                                                                    />
                                                                                </div>
                                                                                <div className="form-check mx-2">
                                                                                    <label
                                                                                        className="form-label mb-0 text-gray-900"
                                                                                        htmlFor="LimitNo"
                                                                                    >
                                                                                        No
                                                                                    </label>
                                                                                    <input
                                                                                        className="form-check-input item-type valid-item-type"
                                                                                        id="LimitNo"
                                                                                        name="creditLimit"
                                                                                        type="radio"
                                                                                        defaultChecked
                                                                                        checked={
                                                                                            formik
                                                                                                .values
                                                                                                .other_details
                                                                                                ?.allow_credit_limit ===
                                                                                            0
                                                                                        }
                                                                                        value={
                                                                                            formik
                                                                                                .values
                                                                                                .other_details
                                                                                                ?.allow_credit_limit
                                                                                        }
                                                                                        onChange={() =>
                                                                                            handleCreditLimitChange(
                                                                                                0
                                                                                            )
                                                                                        }
                                                                                    />
                                                                                </div>
                                                                            </div>
                                                                        </Col>
                                                                        {formik.values.other_details
                                                                            ?.allow_credit_limit ===
                                                                        1 ? (
                                                                            <>
                                                                                <Col
                                                                                    xl={3}
                                                                                    lg={6}
                                                                                    md={12}
                                                                                    sm={12}
                                                                                    className="mb-5"
                                                                                >
                                                                                    <Form.Group>
                                                                                        <div className="input-group flex-nowrap">
                                                                                            <div className="position-relative h-40px w-100 form-floating-group">
                                                                                                <Form.Control
                                                                                                    className="position-relative floating-label-input"
                                                                                                    type="number"
                                                                                                    style={{
                                                                                                        paddingLeft:
                                                                                                            "43px",
                                                                                                    }}
                                                                                                    placeholder=""
                                                                                                    name="other_details.credit_limit"
                                                                                                    value={
                                                                                                        formik
                                                                                                            .values
                                                                                                            .other_details
                                                                                                            ?.credit_limit ||
                                                                                                        ""
                                                                                                    }
                                                                                                    onChange={
                                                                                                        formik.handleChange
                                                                                                    }
                                                                                                />
                                                                                                <Form.Label
                                                                                                    style={{
                                                                                                        left: "43px",
                                                                                                    }}
                                                                                                >
                                                                                                    Credit
                                                                                                    Limit
                                                                                                    Amount
                                                                                                </Form.Label>
                                                                                            </div>
                                                                                            <button
                                                                                                className="input-group-text custom-group-text text-gray-900 fs-4"
                                                                                                style={{
                                                                                                    cssText:
                                                                                                        "border-radius: 8px 0 0 8px !important; left: 0 !important; right: auto !important;",
                                                                                                }}
                                                                                                type="button"
                                                                                            >
                                                                                                {company
                                                                                                    ?.company
                                                                                                    ?.currentCurrencySymbol ||
                                                                                                    "₹"}
                                                                                            </button>
                                                                                        </div>
                                                                                    </Form.Group>
                                                                                </Col>
                                                                                <Col
                                                                                    xl={3}
                                                                                    lg={6}
                                                                                    md={12}
                                                                                    sm={12}
                                                                                    className="mb-5"
                                                                                >
                                                                                    <div className="ms-auto d-flex">
                                                                                        <div className="form-check mx-2 mt-1 align-content-center">
                                                                                            <label
                                                                                                className="form-label mb-0"
                                                                                                htmlFor="warn"
                                                                                            >
                                                                                                Warn
                                                                                            </label>
                                                                                            <input
                                                                                                className="form-check-input"
                                                                                                id="warn"
                                                                                                name="group1"
                                                                                                type="radio"
                                                                                                defaultChecked
                                                                                                checked={
                                                                                                    formik
                                                                                                        .values
                                                                                                        .other_details
                                                                                                        ?.credit_limit_action ===
                                                                                                    1
                                                                                                }
                                                                                                value={
                                                                                                    formik
                                                                                                        .values
                                                                                                        .other_details
                                                                                                        ?.credit_limit_action
                                                                                                }
                                                                                                onChange={() =>
                                                                                                    changeCreditLimitAction(
                                                                                                        1
                                                                                                    )
                                                                                                }
                                                                                            />
                                                                                        </div>
                                                                                        <div className="form-check mx-2 mt-1 align-content-center">
                                                                                            <label
                                                                                                className="form-label mb-0"
                                                                                                htmlFor="block"
                                                                                            >
                                                                                                Block
                                                                                            </label>
                                                                                            <input
                                                                                                className="form-check-input"
                                                                                                id="block"
                                                                                                name="group1"
                                                                                                type="radio"
                                                                                                checked={
                                                                                                    formik
                                                                                                        .values
                                                                                                        .other_details
                                                                                                        ?.credit_limit_action ===
                                                                                                    2
                                                                                                }
                                                                                                value={
                                                                                                    formik
                                                                                                        .values
                                                                                                        .other_details
                                                                                                        ?.credit_limit_action
                                                                                                }
                                                                                                onChange={() =>
                                                                                                    changeCreditLimitAction(
                                                                                                        2
                                                                                                    )
                                                                                                }
                                                                                            />
                                                                                        </div>
                                                                                    </div>
                                                                                </Col>
                                                                            </>
                                                                        ) : null}
                                                                    </Row>
                                                                ) : null}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="accordion-item">
                                                        <div className="d-flex flex-wrap justify-content-sm-start justify-content-between gap-2 gap-sm-14 align-items-center mb-5">
                                                            <h2 className="accordion-header">
                                                                <button
                                                                    className="accordion-button collapsed fw-bolder mb-0"
                                                                    type="button"
                                                                    data-bs-toggle="collapse"
                                                                    data-bs-target="#panelsStayOpen-collapseThree"
                                                                    aria-expanded="false"
                                                                    aria-controls="panelsStayOpen-collapseThree"
                                                                >
                                                                    Opening Balance
                                                                </button>
                                                            </h2>
                                                            <div>
                                                                <div className="form-check form-switch form-check-custom">
                                                                    <label
                                                                        htmlFor="billWise"
                                                                        className="form-label fs-16 text-primary fw-bolder mb-0"
                                                                    >
                                                                        Bill Wise
                                                                    </label>
                                                                    <input
                                                                        className="form-check-input mx-2"
                                                                        type="checkbox"
                                                                        name="opening_balance_details.bill_wise"
                                                                        id="billWise"
                                                                        checked={formik.values.opening_balance_details?.bill_wise}
                                                                        onChange={handleCheckboxChange}
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div
                                                            id="panelsStayOpen-collapseThree"
                                                            className="accordion-collapse collapse show"
                                                        >
                                                            <div className="accordion-body">
                                                                    {formik.values.opening_balance_details.bill_wise ? (
                                                                        <div className="bill-wise-content">
                                                                            <div className="d-flex justify-content-between align-items-center gap-4 mb-4">
                                                                                <h5 style={{ marginBottom: "0" }}>Amount : {DrCrAmount} {isShowDrCr === "Cr" ? "Cr" : "Dr"}</h5>
                                                                                {selectedRows.length > 0 && (
                                                                                    <button type="button" className="btn btn-sm btn-danger" onClick={handleBulkDelete}>
                                                                                        Bulk Delete
                                                                                    </button>
                                                                                )}
                                                                                <div className="d-flex justify-content-end align-items-center gap-5 ms-auto">
                                                                                    <BillWiseModal formik={formik} billWiseTransactionType={billWiseTransactionType} />
                                                                                </div>
                                                                            </div>
                                                                            <div className="overflow-auto">
                                                                                <table className="bill-wise-table w-100 overflow-auto">
                                                                                    <thead>
                                                                                        <tr>
                                                                                            <th className="fw-5 text-nowrap"></th>
                                                                                            <th className="fw-5 text-nowrap">Voucher Number</th>
                                                                                            <th className="fw-5 text-nowrap">Voucher Date</th>
                                                                                            <th className="fw-5 text-nowrap">Due Date</th>
                                                                                            <th className="fw-5 text-nowrap">Transaction Type</th>
                                                                                            <th className="fw-5 text-nowrap">Total amount</th>
                                                                                            <th className="fw-5 text-nowrap">{formik.values.parent_action?.toLowerCase() === "supplier" ? "Paid Amount" : "Received Amount"}</th>
                                                                                            <th className="fw-5 text-nowrap">Pending Amount</th>
                                                                                            <th className="fw-5 text-nowrap"></th>
                                                                                        </tr>
                                                                                    </thead>
                                                                                    <tbody>
                                                                                        <tr>
                                                                                            <td className="fs-16 fw-5 text-center">
                                                                                                <input className="form-check-input" type="checkbox" onChange={handleSelectAll}
                                                                                                    checked={selectedRows.length === formik.values.bill_wise_opening_balance_details?.filter((item) => item?.is_editable).length && selectedRows.length > 0} />
                                                                                            </td>
                                                                                            <td className="fs-16 fw-5">
                                                                                                <FormInput className="floating-label-input search-input" type="text" placeholder="Search" onChange={(e) => customFilter(e, "voucher_number")} />
                                                                                            </td>
                                                                                            <td className="fs-16 fw-5">
                                                                                                <FormInput className="floating-label-input search-input" type="text" placeholder="Search" onChange={(e) => customFilter(e, "voucher_date")} />
                                                                                            </td>
                                                                                            <td className="fs-16 fw-5">
                                                                                                <FormInput className="floating-label-input search-input" type="text" placeholder="Search" onChange={(e) => customFilter(e, "due_date")} />
                                                                                            </td>
                                                                                            <td className="fs-16 fw-5">
                                                                                                <FormInput className="floating-label-input search-input" type="text" placeholder="Search" onChange={(e) => customFilter(e, "transaction_type")} />
                                                                                            </td>
                                                                                            <td className="fs-16 fw-5">
                                                                                                <FormInput className="floating-label-input search-input" type="number" placeholder="Search" onChange={(e) => customFilter(e, "total_amount")} />
                                                                                            </td>
                                                                                            <td className="fs-16 fw-5">
                                                                                               {formik.values.parent_action?.toLowerCase() === "supplier" ? (
                                                                                                    <FormInput className="floating-label-input search-input" type="number" placeholder="Search" onChange={(e)=>customFilter(e, "paid_amount")} />
                                                                                                ) : (
                                                                                                    <FormInput className="floating-label-input search-input" type="number" placeholder="Search" onChange={(e) => customFilter(e, "received_amount")} />
                                                                                                )}
                                                                                            </td>
                                                                                            <td className="fs-16 fw-5">
                                                                                                <FormInput className="floating-label-input search-input" type="number" placeholder="Search" onChange={(e) => customFilter(e, "pending_amount")} />
                                                                                            </td>
                                                                                            <td></td>
                                                                                        </tr>
                                                                                        {formik.values.bill_wise_opening_balance_details.map((row, index) => (
                                                                                            <tr key={index}>
                                                                                                <td className="fs-16 fw-5 text-center">
                                                                                                    <input className="form-check-input" type="checkbox" disabled={!row?.is_editable} checked={selectedRows.includes(index)}
                                                                                                        onChange={() => handleRowSelect(index)} />
                                                                                                </td>
                                                                                                <td className="fs-16 fw-5">
                                                                                                    <Form.Group className="position-relative form-floating-group">
                                                                                                        <FormInput className="floating-label-input" type="text" disabled={!row?.is_editable} name={`bill_wise_opening_balance_details.${index}.voucher_number`} placeholder="" value={row.voucher_number} onChange={handleBillWiseChange} required={handleRequireOpeningBalance(row)} />
                                                                                                        <Form.Label className={handleRequireOpeningBalance(row) ? "required" : ""}>Voucher Number</Form.Label>
                                                                                                    </Form.Group>
                                                                                                </td>
                                                                                                <td className="fs-16 fw-5">
                                                                                                    <Form.Group className="position-relative form-floating-group">
                                                                                                        <CustomFieldDate onChange={(date) => handleChangeDueDate(date, index, "voucher")} disabled={!row?.is_editable} value={row.voucher_date} placeholder={"Voucher Date"} input_type="date" required={handleRequireOpeningBalance(row)} />
                                                                                                    </Form.Group>
                                                                                                </td>
                                                                                                <td className="fs-16 fw-5">
                                                                                                    <Form.Group className="position-relative form-floating-group">
                                                                                                        <CustomFieldDate onChange={(date) => handleChangeDueDate(date, index, "due")} disabled={!row?.is_editable} value={row.due_date} placeholder={"Due Date"} input_type="date" required={handleRequireOpeningBalance(row)} />
                                                                                                    </Form.Group>
                                                                                                </td>
                                                                                                <td className="fs-16 fw-5">
                                                                                                    <Form.Group>
                                                                                                        <div className="input-group flex-nowrap">
                                                                                                            <div className="position-relative h-40px w-100 focus-shadow">
                                                                                                                <ReactSelect
                                                                                                                    options={billWiseTransactionType}
                                                                                                                    value={row.transaction_type}
                                                                                                                    onChange={(selected) => handleSelectChange(selected, index)}
                                                                                                                    placeholder="Transaction Type"
                                                                                                                    defaultLabel="Select Transaction Type"
                                                                                                                    className="react-select-container"
                                                                                                                    classNamePrefix="react-select"
                                                                                                                    position="top"
                                                                                                                    required={handleRequireOpeningBalance(row)}
                                                                                                                    isDisabled={!row?.is_editable}
                                                                                                                />
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </Form.Group>
                                                                                                </td>
                                                                                                <td className="fs-16 fw-5">
                                                                                                    <Form.Group className="position-relative form-floating-group">
                                                                                                        <FormInput className="floating-label-input" type="number" placeholder="" disabled={!row?.is_editable} name={`bill_wise_opening_balance_details.${index}.total_amount`} value={row.total_amount} onChange={(e) => handleBillWiseChange(e, index, "total_amount")} required={handleRequireOpeningBalance(row)} />
                                                                                                        <Form.Label className={handleRequireOpeningBalance(row) ? "required" : ""}>Total Amount</Form.Label>
                                                                                                    </Form.Group>
                                                                                                </td>
                                                                                                <td className="fs-16 fw-5">
                                                                                                    <Form.Group className="position-relative form-floating-group">
                                                                                                       {formik.values.parent_action?.toLowerCase() === "supplier" ? (
                                                                                                            <FormInput className="floating-label-input" type="number" disabled={!row?.is_editable} name={`bill_wise_opening_balance_details.${index}.paid_amount`} placeholder="" value={row.paid_amount} required={() =>handleRequireOpeningBalance(row)} onChange={(e)=>handleBillWiseChange(e, index, "paid_amount")} onClick={(e) => e.target.click()} />
                                                                                                        ) : (
                                                                                                            <FormInput className="floating-label-input" type="number" disabled={!row?.is_editable} name={`bill_wise_opening_balance_details.${index}.received_amount`} placeholder="" value={row.received_amount} required={handleRequireOpeningBalance(row)} onChange={(e) => handleBillWiseChange(e, index, "received_amount")} />
                                                                                                        )}
                                                                                                        <Form.Label className={handleRequireOpeningBalance(row) ? "required" : ""}>{formik.values.parent_action?.toLowerCase() === "supplier" ? "Paid Amount" : "Received Amount"}</Form.Label>
                                                                                                    </Form.Group>
                                                                                                </td>
                                                                                                <td className="fs-16 fw-5">
                                                                                                    <Form.Group className="position-relative form-floating-group">
                                                                                                        <FormInput disabled className="floating-label-input" type="number" name={`bill_wise_opening_balance_details.${index}.pending_amount`} placeholder="" value={row.pending_amount} required={handleRequireOpeningBalance(row)} />
                                                                                                        <Form.Label className={handleRequireOpeningBalance(row) ? "required" : ""}>Pending Amount</Form.Label>
                                                                                                    </Form.Group>
                                                                                                </td>
                                                                                                <td className="fs-16 fw-5">
                                                                                                    {row?.is_editable && (
                                                                                                        <button className="btn p-0 d-flex" onClick={(e) => handleDeleteRow(index, e)}>
                                                                                                            <img src={deleteSvg} alt="delete" />
                                                                                                        </button>
                                                                                                    )}
                                                                                                </td>
                                                                                            </tr>
                                                                                        ))}
                                                                                        <tr>
                                                                                            <td className="fs-16 fw-5 text-nowrap"></td>
                                                                                            <td className="fs-16 fw-5 text-nowrap">
                                                                                                <button
                                                                                                    type="button"
                                                                                                    className="btn-sm btn-icon btn-icon-primary add-item-btn"
                                                                                                    onClick={handleAddRow}
                                                                                                >
                                                                                                    Add New Bill
                                                                                                </button>
                                                                                            </td>
                                                                                            <td className="fs-16 fw-5 text-nowrap"></td>
                                                                                            <td className="fs-16 fw-5 text-nowrap"></td>
                                                                                            <td className="fs-16 fw-5 text-nowrap"></td>
                                                                                            <td className="fs-16 fw-5 text-nowrap"></td>
                                                                                            <td className="fs-16 fw-5 text-nowrap"></td>
                                                                                            <td className="fs-16 fw-5 text-nowrap"></td>
                                                                                            <td className="fs-16 fw-5 text-nowrap"></td>
                                                                                        </tr>
                                                                                    </tbody>
                                                                                    <tfoot>
                                                                                        <tr>
                                                                                            <td className="fs-16 fw-5 text-nowrap"></td>
                                                                                            <td className="fs-16 fw-5 text-nowrap">Total amount</td>
                                                                                            <td className="fs-16 fw-5 text-nowrap"></td>
                                                                                            <td className="fs-16 fw-5 text-nowrap"></td>
                                                                                            <td className="fs-16 fw-5 text-nowrap"></td>
                                                                                            <td className="fs-16 fw-5 text-nowrap">{company?.company?.currentCurrencySymbol ||"₹"}{" "}{parseFloat(totalAmount)}</td>
                                                                                            <td className="fs-16 fw-5 text-nowrap">{company?.company?.currentCurrencySymbol ||"₹"}{" "}{parseFloat(receivedAmount)}</td>
                                                                                            <td className="fs-16 fw-5 text-nowrap">{company?.company?.currentCurrencySymbol ||"₹"}{" "}{parseFloat(pendingAmount)}</td>
                                                                                            <td className="fs-16 fw-5 text-nowrap"></td>
                                                                                        </tr>
                                                                                    </tfoot>
                                                                                </table>
                                                                            </div>
                                                                        </div>
                                                                    ) : (
                                                                        <Row className="align-items-center">
                                                                            <Col
                                                                                xxl={2}
                                                                                xl={3}
                                                                                lg={4}
                                                                                sm={6}
                                                                                className="mb-sm-0 mb-5"
                                                                            >
                                                                                <Form.Group className="position-relative form-floating-group">
                                                                                    <FormInput
                                                                                        className="floating-label-input"
                                                                                        type="text"
                                                                                        placeholder=""
                                                                                        name="opening_balance_details.opening_balance"
                                                                                        value={
                                                                                            formik.values
                                                                                                .opening_balance_details
                                                                                                ?.opening_balance
                                                                                        }
                                                                                        onChange={e => {
                                                                                            const value = e.target.value;
                                                                                            const regex = /^[0-9]*\.?[0-9]*$/;
                                                                                            if (regex.test(Number(value))) {
                                                                                                formik.setFieldValue(
                                                                                                    "opening_balance_details.opening_balance",
                                                                                                    value
                                                                                                );
                                                                                            }
                                                                                        }}
                                                                                    />
                                                                                    <Form.Label>
                                                                                        Amount
                                                                                    </Form.Label>
                                                                                </Form.Group>
                                                                            </Col>
                                                                            <Col xxl={2} xl={3} lg={4} sm={6}>
                                                                        <label className="form-label fs-6 fw-bolder mb-0">
                                                                            Opening Balance
                                                                        </label>
                                                                        <div className="ms-auto d-flex">
                                                                            <div className="form-check mx-2 mt-1 align-content-center">
                                                                                <label
                                                                                    className="form-label mb-0"
                                                                                    htmlFor="Yes"
                                                                                >
                                                                                    Debit
                                                                                </label>
                                                                                <input
                                                                                    className="form-check-input"
                                                                                    id="Yes"
                                                                                    name="opening_balance_details.opening_balance_dr_cr"
                                                                                    checked={
                                                                                        formik
                                                                                            .values
                                                                                            .opening_balance_details
                                                                                            ?.opening_balance_dr_cr ===
                                                                                        1
                                                                                    }
                                                                                    value={1}
                                                                                    onChange={() =>
                                                                                        formik.setFieldValue(
                                                                                            "opening_balance_details.opening_balance_dr_cr",
                                                                                            1
                                                                                        )
                                                                                    }
                                                                                    type="radio"
                                                                                />
                                                                            </div>
                                                                            <div className="form-check mx-2 mt-1 align-content-center">
                                                                                <label
                                                                                    className="form-label mb-0"
                                                                                    htmlFor="No"
                                                                                >
                                                                                    Credit
                                                                                </label>
                                                                                <input
                                                                                    className="form-check-input"
                                                                                    id="No"
                                                                                    name="opening_balance_details.opening_balance_dr_cr"
                                                                                    checked={
                                                                                        formik.values
                                                                                            .opening_balance_details
                                                                                            ?.opening_balance_dr_cr ===
                                                                                        2
                                                                                    }
                                                                                    value={2}
                                                                                    onChange={() =>
                                                                                        formik.setFieldValue(
                                                                                            "opening_balance_details.opening_balance_dr_cr",
                                                                                            2
                                                                                        )
                                                                                    }
                                                                                    type="radio"
                                                                                />
                                                                            </div>
                                                                        </div>
                                                                    </Col>
                                                                </Row>
                                                            )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </Col>
                                        </Row>
                                    </>
                                )}
                                <div className="mt-4">
                                    <button
                                        type="submit"
                                        value="ledger"
                                        className="btn btn-primary me-2"
                                        disabled={isDisable}
                                    >
                                        Save
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <Modal show={swiftCodeShow} onHide={() => setSwiftCodeShow(false)} centered size='md' className={`modal fade custom_text_modal`}>
                <div className="modal-dialog-centered modal-xl">
                    <div className="modal-content">
                        <div className="modal-header py-3">
                            <h5 className="modal-title">Change Label</h5>
                            <button
                                type="button"
                                className="btn btn-icon btn-sm btn-active-light-primary ms-2"
                                onClick={() => setSwiftCodeShow(false)}
                            >
                                <Close />
                            </button>
                        </div>
                        <div className="modal-body item-transaction">
                                <div className='d-flex flex-column gap-4 mt-2'>
                                <Form.Group className="position-relative form-floating-group">
                                    <FormInput
                                        className="floating-label-input"
                                        type="text"
                                        placeholder=""
                                        value={swiftCodeLabel}
                                        onChange={(e) => setSwiftCodeLabel(e.target.value)}
                                    />
                                    <Form.Label>Label Name</Form.Label>
                                </Form.Group>

                                <button
                                    type="button"
                                    className="btn btn-primary swift-code-btn mt-3"
                                    onClick={handleSwiftCode}
                                >
                                    Save
                                </button>
                                </div>
                        </div>

                    </div>
                </div>
            </Modal>

                {isItemCatModel && (
                    <AddItemCategoryModal
                        show={isItemCatModel}
                        handleClose={closeItemCatModel}
                        isLedger={true}
                        itemFormik={formik}
                        setFields={setFields}
                        ledgerDetailOptions={ledgerDetailOptions}
                        isPurchase={isPurchase}
                        setIsCreatedItemGroup={setIsCreatedItemGroup}
                        groupLedgerList={ledgerDetailOptions}
                        // comment by dhruvin: ledger model update in show default group not get into api
                        // groupLedgerList={name.name === "Update Ledger" ||
                        //     name.name === "Update Customer" ||
                        //     name.name === "Update Supplier"
                        //         ? groupLedgerList
                        //         :
                        //         ledgerDetailOptions}
                    />
                )}
                {isTransportModel && (
                    <AddTransportModal
                        show={isTransportModel}
                        handleClose={closeTransportModel}
                        setModalType={setTransporterDetail}
                        modalType={transporterDetail.modelType}
                        transport={transport}
                        setTransporterDetail={setTransporterDetail}
                        partyFormik={formik}
                    />
                )}
                {isBrokerModel && (
                    <AddBrokerModal
                        show={isBrokerModel}
                        handleClose={closeBrokerModel}
                        modalType={false}
                        setBrokerDetail={setBrokerDetail}
                        partyFormik={formik}
                        isPurchase={isPurchase}
                    />
                )}
                {isShowItemStockModel && (
                    <AddItemStockModal
                        show={isShowItemStockModel}
                        handleClose={closeStockModel}
                        data={editStockDetail}
                        multiDate={multiDate}
                        ledgerId={formik.values.id}
                    />
                )}
                {isDeleteItemStock && (
                    <WarningModal
                        show={isDeleteItemStock}
                        handleClose={() => setIsDeleteItemStock(false)}
                        handleSubmit={handleDeleteItemStock} // Confirm profit/loss
                        message={`Are you sure want to delete this !`}
                        confirmText="Yes"
                        showConfirmButton
                        showCancelButton
                    />
                )}
                {isLocationOfAsset && (
                    <LocationOfAsset
                        show={isLocationOfAsset}
                        handleClose={closeLocationOfAssetModel}
                        modalType={false}
                        setBrokerDetail={setBrokerDetail}
                        partyFormik={formik}
                    />
                )}
                {showGstNotUnique && (
                    <WarningModal
                        show={showGstNotUnique}
                        handleSubmit={() => setShowGstNotUnique(false)}
                        message={gstNotUniqueMessage}
                        showConfirmButton
                    />
                )}
                {showBulkDeleteModel && (
                    <WarningModal
                        show={showBulkDeleteModel}
                        handleClose={() => setShowBulkDeleteModel(false)}
                        handleSubmit={() => handleConfirmBulkDelete()}
                        message={`Are you sure want to delete this Bill !`}
                        confirmText="Yes"
                        showConfirmButton
                        showCancelButton
                    />
                )}
            </Modal>
            {isAddCessRate && <CessRateModal isAddCessRate={isAddCessRate} handleClose={() => setIsAddCessRate(false)} cessRateId={cessRateId} setCessRateId={setCessRateId} formik={formik} />}
        </>
    );
};

export default AddLedgerModal;
