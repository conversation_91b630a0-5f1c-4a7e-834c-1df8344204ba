<?php

namespace App\Actions\IncomeDNTransaction;

use App\Jobs\UpdateIncomeBankDetails;
use App\Models\Configuration\IncomeDebitNote;
use App\Models\Master\IncomeDebitNoteTransactionMaster;
use Lorisle<PERSON>\Actions\Concerns\AsAction;

class UpdateConfiguration
{
    use AsAction;

    public function handle($input)
    {
        $incomeDebitNoteConfigurationData = [
            'is_change_gst_details' => $input['is_change_gst_details'] ?? false,
            'is_enabled_dispatch_details' => $input['is_enabled_dispatch_details'] ?? false,
            'is_enabled_shipping_address' => $input['is_enabled_shipping_address'] ?? false,
            'is_enabled_broker_details' => $input['is_enabled_broker_details'] ?? false,
            'is_enabled_transport_details' => $input['is_enabled_transport_details'] ?? false,
            'is_enabled_po_details_of_buyer' => $input['is_enabled_po_details_of_buyer'] ?? false,
            'is_enabled_credit_period_details' => $input['is_enabled_credit_period_details'] ?? false,
            'is_additional_ledger_description' => $input['is_additional_ledger_description'] ?? false,
            'consolidating_items_to_invoice' => $input['consolidating_items_to_invoice'] ?? false,
            'warn_on_negative_stock' => $input['warn_on_negative_stock'] ?? false,
            'is_additional_item_description' => $input['is_additional_item_description'] ?? false,
            'is_enable_narration' => $input['is_enable_narration'] ?? false,
            'is_enabled_terms_and_conditions' => $input['is_enabled_terms_and_conditions'] ?? false,
            'is_enabled_tcs_details' => $input['is_enabled_tcs_details'] ?? false,
            'is_enabled_tds_details' => $input['is_enabled_tds_details'] ?? false,
            'is_enabled_mrp' => $input['is_enabled_mrp'] ?? false,
            'is_enabled_payment_details' => $input['is_enabled_payment_details'] ?? false,
            'is_enabled_discount_2' => $input['is_enabled_discount_2'] ?? false,
            'is_enabled_phone_number' => $input['is_enabled_phone_number'] ?? false,
            'is_enabled_hsn_code' => $input['is_enabled_hsn_code'] ?? false,
        ];

        $incomeDebitNoteTransactionMaster = IncomeDebitNoteTransactionMaster::first();

        $incomeDebitNoteTransactionMasterData = [
            'round_off_method' => $input['round_off_method'] ?? null,
            'method_of_voucher_number' => $input['method_of_voucher_number'] ?? null,
            'start_with_number' => $input['start_with_number'] ?? null,
            'print_voucher_after_saving' => isset($incomeDebitNoteTransactionMaster->print_voucher_after_saving) ? $incomeDebitNoteTransactionMaster->print_voucher_after_saving : IncomeDebitNoteTransactionMaster::PRINT_VOUCHER_AFTER_SAVING_YES,
            'is_change_every_financial_year' => $input['is_change_every_financial_year'] ?? false,
            'prefix_method' => $input['prefix_method'] ?? null,
            'prefix' => $input['prefix'] ?? null,
            'suffix_method' => $input['suffix_method'] ?? null,
            'suffix' => $input['suffix'] ?? null,
            'bank_id' => $input['bank_id'] ?? null,
            'title_of_print' => $input['title_of_print'] ?? null,
            'set_alter_declaration' => $input['terms_and_conditions'] ?? null,
            'on_pay_ledger_id' => $input['on_pay_ledger_id'] ?? null,
        ];

        $incomeDebitNoteConfigurationId = IncomeDebitNote::first()?->id;

        IncomeDebitNote::updateOrCreate(
            ['id' => $incomeDebitNoteConfigurationId],
            $incomeDebitNoteConfigurationData
        );

        if (isset($input['show_item_image'])) {
            IncomeDebitNote::updateOrCreate(
                ['id' => $incomeDebitNoteConfigurationId],
                ['show_item_image' => $input['show_item_image']]
            );
        }

        if (isset($input['with_tax'])) {
            IncomeDebitNote::updateOrCreate(
                ['id' => $incomeDebitNoteConfigurationId],
                ['with_tax' => $input['with_tax']]
            );
        }

        if (isset($input['is_enabled_bank_details'])) {
            IncomeDebitNote::updateOrCreate(
                ['id' => $incomeDebitNoteConfigurationId],
                ['is_enabled_bank_details' => $input['is_enabled_bank_details']]
            );
        }

        IncomeDebitNoteTransactionMaster::updateOrCreate(
            ['id' => $incomeDebitNoteTransactionMaster->id],
            $incomeDebitNoteTransactionMasterData
        );

        // Update Bank Details for All Transactions
        if (isset($input['apply_income_bank_details']) && $input['apply_income_bank_details'] == 1) {
            $bankId = $input['bank_id'];
            if (! $bankId) {
                throw new \Exception('Bank ID is required to update income transaction bank details.');
            }
            $companyId = getCurrentCompany()->id;

            UpdateIncomeBankDetails::dispatch($companyId, $bankId);
        }

        return true;
    }
}
